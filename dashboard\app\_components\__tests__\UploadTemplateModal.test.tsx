import { describe, it, expect, beforeEach, vi } from "vitest";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { UploadTemplateModal } from "../modals/UploadTemplateModal";
import { ThemeProvider } from "../../_contexts/ThemeContext";
import { Template } from "../../_lib/types";

// Mock react-dropzone
vi.mock("react-dropzone", () => ({
  useDropzone: vi.fn(() => ({
    getRootProps: () => ({ "data-testid": "dropzone" }),
    getInputProps: () => ({ "data-testid": "file-input" }),
    isDragActive: false,
  })),
}));

// Mock matchMedia for theme detection
const mockMatchMedia = vi.fn().mockImplementation((query: string) => ({
  matches: query === "(prefers-color-scheme: dark)",
  media: query,
  onchange: null,
  addListener: vi.fn(),
  removeListener: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
}));

window.matchMedia = mockMatchMedia;

interface TestAppProps {
  onClose?: () => void;
  onUpload?: (template: Template) => void;
  initialTheme?: "light" | "dark" | "system";
}

function TestApp({
  onClose = vi.fn(),
  onUpload = vi.fn(),
  initialTheme = "light",
}: TestAppProps) {
  // Set initial theme in localStorage before rendering
  if (typeof window !== "undefined") {
    localStorage.setItem("theme", initialTheme);
  }

  return (
    <ThemeProvider>
      <UploadTemplateModal onClose={onClose} onUpload={onUpload} />
    </ThemeProvider>
  );
}

describe("UploadTemplateModal", () => {
  beforeEach(() => {
    localStorage.clear();
    document.documentElement.className = "";
    vi.clearAllMocks();
  });

  describe("Basic Functionality", () => {
    it("should render the upload modal with initial step", () => {
      render(<TestApp />);

      expect(screen.getByText("Subir Nueva Plantilla")).toBeInTheDocument();
      expect(
        screen.getByText(
          "Arrastra tu plantilla aquí o haz clic para seleccionar"
        )
      ).toBeInTheDocument();
      expect(
        screen.getByText("PDF, DOC, DOCX (máx. 10MB)")
      ).toBeInTheDocument();
    });

    it("should call onClose when close button is clicked", async () => {
      const onClose = vi.fn();
      const user = userEvent.setup();

      render(<TestApp onClose={onClose} />);

      const closeButton = screen.getByRole("button", { name: "" });
      await user.click(closeButton);

      expect(onClose).toHaveBeenCalledOnce();
    });
  });

  describe("Dark Theme Support", () => {
    it("should apply dark theme classes to modal content when dark mode is active", async () => {
      // Set up dark theme
      mockMatchMedia.mockImplementation((query: string) => ({
        matches: query === "(prefers-color-scheme: dark)",
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }));

      render(<TestApp initialTheme="dark" />);

      await waitFor(() => {
        expect(document.documentElement.classList.contains("dark")).toBe(true);
      });

      // Check modal content has dark theme classes
      const modalContent = screen
        .getByText("Subir Nueva Plantilla")
        .closest('[role="dialog"]');
      expect(modalContent).toHaveClass("dark:bg-gray-800");
    });

    it("should apply dark theme classes to title", async () => {
      render(<TestApp initialTheme="dark" />);

      await waitFor(() => {
        expect(document.documentElement.classList.contains("dark")).toBe(true);
      });

      // Check title has dark theme classes
      const title = screen.getByText("Subir Nueva Plantilla");
      expect(title).toHaveClass("dark:text-gray-100");
    });

    it("should apply dark theme classes to dropzone area", async () => {
      render(<TestApp initialTheme="dark" />);

      await waitFor(() => {
        expect(document.documentElement.classList.contains("dark")).toBe(true);
      });

      // Check dropzone has dark theme classes
      const dropzone = screen.getByTestId("dropzone");
      expect(dropzone).toHaveClass("dark:border-gray-600");
      expect(dropzone).toHaveClass("dark:hover:border-gray-500");
    });

    it("should apply dark theme classes to text elements", async () => {
      render(<TestApp initialTheme="dark" />);

      await waitFor(() => {
        expect(document.documentElement.classList.contains("dark")).toBe(true);
      });

      // Check text elements have dark theme classes
      const instructionText = screen.getByText(
        "Arrastra tu plantilla aquí o haz clic para seleccionar"
      );
      expect(instructionText).toHaveClass("dark:text-gray-300");

      const sizeText = screen.getByText("PDF, DOC, DOCX (máx. 10MB)");
      expect(sizeText).toHaveClass("dark:text-gray-400");
    });

    it("should apply dark theme classes to requirements section", async () => {
      render(<TestApp initialTheme="dark" />);

      await waitFor(() => {
        expect(document.documentElement.classList.contains("dark")).toBe(true);
      });

      // Check requirements section has dark theme classes
      const requirementsSection = screen
        .getByText("Requisitos para publicar:")
        .closest("div");
      expect(requirementsSection).toHaveClass("dark:bg-blue-900/20");

      const requirementsTitle = screen.getByText("Requisitos para publicar:");
      expect(requirementsTitle).toHaveClass("dark:text-blue-200");
    });

    it("should apply dark theme classes to close button", async () => {
      render(<TestApp initialTheme="dark" />);

      await waitFor(() => {
        expect(document.documentElement.classList.contains("dark")).toBe(true);
      });

      // Check close button has dark theme classes
      const closeButton = screen.getByRole("button", { name: "" });
      expect(closeButton).toHaveClass("dark:text-gray-500");
      expect(closeButton).toHaveClass("dark:hover:text-gray-300");
    });

    it("should maintain light theme classes when light mode is active", async () => {
      // Explicitly set light theme and reset dark class
      localStorage.setItem("theme", "light");
      document.documentElement.classList.remove("dark");

      // Mock system preference for light mode
      mockMatchMedia.mockImplementation((query: string) => ({
        matches: false, // System prefers light
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }));

      render(<TestApp initialTheme="light" />);

      await waitFor(() => {
        expect(document.documentElement.classList.contains("dark")).toBe(false);
      });

      // Check modal content has light theme classes
      const modalContent = screen
        .getByText("Subir Nueva Plantilla")
        .closest('[role="dialog"]');
      expect(modalContent).toHaveClass("bg-white");
    });
  });
});
