"use client";

import { motion } from "framer-motion";
import {
  ChartBarIcon,
  ClockIcon,
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ExclamationTriangleIcon,
  LightBulbIcon,
} from "@heroicons/react/24/outline";
import { Case } from "../../_lib/types";

interface CaseIntelligenceProps {
  case: Case;
}

export function CaseIntelligence({ case: caseData }: CaseIntelligenceProps) {
  // Mock data for similar cases comparison
  const similarCasesAverage = {
    duration: 4.2, // months
    successRate: 78,
    averageCost: 180000,
  };

  const getProgressComparison = () => {
    const expectedProgress = 0.3; // Based on time elapsed
    const actualProgress = caseData.progress;
    const difference = actualProgress - expectedProgress;

    return {
      difference,
      isAhead: difference > 0,
      percentage: Math.abs(difference * 100),
    };
  };

  const progressComparison = getProgressComparison();

  // Use case-specific AI recommendations from nextActions, fallback to generic ones
  const recommendations = caseData.nextActions || [
    "Acelerar la recopilación de documentos pendientes",
    "Programar reunión con el cliente para revisar estrategia",
    "Considerar mediación antes de proceder judicialmente",
    "Actualizar cálculo de indemnización con últimos precedentes",
  ];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 space-y-6">
      <div className="flex items-center space-x-2 mb-6">
        <ChartBarIcon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Inteligencia del Caso
        </h3>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Time Tracking */}
        {caseData.timeTracking && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4"
          >
            <div className="flex items-center space-x-2 mb-2">
              <ClockIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              <span className="text-xs font-medium text-blue-900 dark:text-blue-200">
                Tiempo Invertido
              </span>
            </div>
            <div className="space-y-1">
              <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                {caseData.timeTracking.totalHours}h
              </div>
              <div className="text-xs text-blue-700 dark:text-blue-300">
                {caseData.timeTracking.billableHours}h facturables
              </div>
            </div>
          </motion.div>
        )}

        {/* Success Probability */}
        {caseData.successProbability && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4"
          >
            <div className="flex items-center space-x-2 mb-2">
              <ArrowTrendingUpIcon className="h-4 w-4 text-green-600 dark:text-green-400" />
              <span className="text-xs font-medium text-green-900 dark:text-green-200">
                Prob. Éxito
              </span>
            </div>
            <div className="space-y-1">
              <div className="text-2xl font-bold text-green-900 dark:text-green-100">
                {caseData.successProbability}%
              </div>
              <div className="text-xs text-green-700 dark:text-green-300">
                vs {similarCasesAverage.successRate}% promedio
              </div>
            </div>
          </motion.div>
        )}

        {/* Progress Comparison */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className={`${
            progressComparison.isAhead
              ? "bg-green-50 dark:bg-green-900/20"
              : "bg-yellow-50 dark:bg-yellow-900/20"
          } rounded-lg p-4`}
        >
          <div className="flex items-center space-x-2 mb-2">
            {progressComparison.isAhead ? (
              <ArrowTrendingUpIcon className="h-4 w-4 text-green-600 dark:text-green-400" />
            ) : (
              <ArrowTrendingDownIcon className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
            )}
            <span
              className={`text-xs font-medium ${
                progressComparison.isAhead
                  ? "text-green-900 dark:text-green-200"
                  : "text-yellow-900 dark:text-yellow-200"
              }`}
            >
              Progreso
            </span>
          </div>
          <div className="space-y-1">
            <div
              className={`text-2xl font-bold ${
                progressComparison.isAhead
                  ? "text-green-900 dark:text-green-100"
                  : "text-yellow-900 dark:text-yellow-100"
              }`}
            >
              {Math.round(caseData.progress * 100)}%
            </div>
            <div
              className={`text-xs ${
                progressComparison.isAhead
                  ? "text-green-700 dark:text-green-300"
                  : "text-yellow-700 dark:text-yellow-300"
              }`}
            >
              {progressComparison.isAhead ? "+" : "-"}
              {progressComparison.percentage.toFixed(0)}% vs esperado
            </div>
          </div>
        </motion.div>

        {/* Estimated Value */}
        {caseData.estimatedCost && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4"
          >
            <div className="flex items-center space-x-2 mb-2">
              <CurrencyDollarIcon className="h-4 w-4 text-purple-600 dark:text-purple-400" />
              <span className="text-xs font-medium text-purple-900 dark:text-purple-200">
                Valor Estimado
              </span>
            </div>
            <div className="space-y-1">
              <div className="text-lg font-bold text-purple-900 dark:text-purple-100">
                {caseData.estimatedCost.split(" - ")[0]}
              </div>
              <div className="text-xs text-purple-700 dark:text-purple-300">
                Rango estimado
              </div>
            </div>
          </motion.div>
        )}
      </div>

      {/* Recommendations */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="bg-amber-50 dark:bg-amber-900/20 rounded-lg p-4"
      >
        <div className="flex items-center space-x-2 mb-3">
          <LightBulbIcon className="h-5 w-5 text-amber-600 dark:text-amber-400" />
          <h4 className="text-sm font-medium text-amber-900 dark:text-amber-200">
            Recomendaciones IA
          </h4>
        </div>
        <ul className="space-y-2">
          {recommendations.slice(0, 3).map((recommendation, index) => (
            <li key={index} className="flex items-start space-x-2">
              <div className="w-1.5 h-1.5 bg-amber-600 dark:bg-amber-400 rounded-full mt-2 flex-shrink-0" />
              <span className="text-sm text-amber-800 dark:text-amber-200">
                {recommendation}
              </span>
            </li>
          ))}
        </ul>
      </motion.div>

      {/* Risk Indicators */}
      {caseData.riskAssessment && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="border-t border-gray-200 dark:border-gray-600 pt-4"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <ExclamationTriangleIcon className="h-4 w-4 text-gray-500 dark:text-gray-400" />
              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                Indicadores de Riesgo
              </span>
            </div>
            <span
              className={`px-2 py-1 rounded-full text-xs font-medium ${
                caseData.riskAssessment === "low"
                  ? "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200"
                  : caseData.riskAssessment === "medium"
                  ? "bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200"
                  : "bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200"
              }`}
            >
              Riesgo{" "}
              {caseData.riskAssessment === "low"
                ? "Bajo"
                : caseData.riskAssessment === "medium"
                ? "Medio"
                : "Alto"}
            </span>
          </div>

          <div className="mt-3 grid grid-cols-3 gap-4 text-sm">
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {caseData.complexityScore}/10
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Complejidad
              </div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {similarCasesAverage.duration}m
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Duración promedio
              </div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {caseData.similarCount}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Casos similares
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
}
