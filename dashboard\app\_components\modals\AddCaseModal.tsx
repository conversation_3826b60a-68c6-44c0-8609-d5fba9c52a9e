"use client";

import { useState } from "react";
import {
  XMarkIcon,
  UserIcon,
  ScaleIcon,
} from "@heroicons/react/24/outline";
import * as Dialog from "@radix-ui/react-dialog";
import { Case } from "../../_lib/types";

interface AddCaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCaseAdded: (newCase: Case) => void;
}

interface FormData {
  title: string;
  type: string;
  client: string;
  description: string;
  priority: "low" | "medium" | "high";
  estimatedCost: string;
}

const CASE_TYPES = [
  "Laboral",
  "Civil",
  "Familia",
  "Penal",
  "Comercial",
  "Administrativo",
  "Constitucional",
  "Tributario",
];

const PRIORITIES = [
  { value: "low", label: "Baja", color: "text-green-600 dark:text-green-400" },
  { value: "medium", label: "Media", color: "text-yellow-600 dark:text-yellow-400" },
  { value: "high", label: "Alta", color: "text-red-600 dark:text-red-400" },
];

export function AddCaseModal({ isOpen, onClose, onCaseAdded }: AddCaseModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    title: "",
    type: "",
    client: "",
    description: "",
    priority: "medium",
    estimatedCost: "",
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) newErrors.title = "El título es obligatorio";
    if (!formData.type) newErrors.type = "El tipo de caso es obligatorio";
    if (!formData.client.trim()) newErrors.client = "El nombre del cliente es obligatorio";
    if (!formData.description.trim()) newErrors.description = "La descripción es obligatoria";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;
    setIsSubmitting(true);

    try {
      // Crear el nuevo caso
      const newCase: Case = {
        id: `c-${crypto.randomUUID().slice(0, 8)}`,
        title: formData.title,
        type: formData.type,
        client: formData.client,
        status: "new",
        progress: 0,
        createdAt: new Date().toISOString(),
        similarCount: 0,
        description: formData.description,
        priority: formData.priority,
        estimatedCost: formData.estimatedCost || undefined,
        complexityScore: Math.floor(Math.random() * 10) + 1, // Simulado
        riskAssessment: formData.priority === "high" ? "high" : formData.priority === "low" ? "low" : "medium",
        successProbability: Math.floor(Math.random() * 30) + 70, // 70-100%
        aiSummary: `Caso de ${formData.type.toLowerCase()} con prioridad ${formData.priority === "high" ? "alta" : formData.priority === "medium" ? "media" : "baja"}. ${formData.description.slice(0, 100)}${formData.description.length > 100 ? "..." : ""}`,
        keyFacts: [
          `Tipo de caso: ${formData.type}`,
          `Cliente: ${formData.client}`,
          `Prioridad: ${formData.priority === "high" ? "Alta" : formData.priority === "medium" ? "Media" : "Baja"}`,
          formData.estimatedCost ? `Costo estimado: ${formData.estimatedCost}` : "Costo por definir",
        ],
        nextActions: [
          "Revisar documentación inicial",
          "Programar reunión con el cliente",
          "Definir estrategia legal",
          "Establecer cronograma de trabajo",
        ],
        milestones: [],
        messages: [
          {
            id: `msg-${crypto.randomUUID()}`,
            sender: "lawyer",
            content: `Hola ${formData.client}, he creado tu caso "${formData.title}". Estaré en contacto contigo para coordinar los próximos pasos.`,
            timestamp: new Date().toISOString(),
            status: "sent",
          },
        ],
        documents: [],
        activities: [
          {
            id: `act-${crypto.randomUUID()}`,
            type: "status_change",
            description: "Caso creado",
            timestamp: new Date().toISOString(),
            user: "lawyer",
          },
        ],
        unreadMessagesCount: 0,
      };

      // Simular delay de creación
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Guardar en sessionStorage (persistencia durante la sesión)
      const existingCases = JSON.parse(sessionStorage.getItem("cases") || "[]");
      const updatedCases = [newCase, ...existingCases];
      sessionStorage.setItem("cases", JSON.stringify(updatedCases));

      // Notificar al componente padre
      onCaseAdded(newCase);

      // Cerrar modal directamente después de crear el caso
      handleClose();
    } catch (error) {
      console.error("Error al crear el caso:", error);
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    // Reset form
    setFormData({
      title: "",
      type: "",
      client: "",
      description: "",
      priority: "medium",
      estimatedCost: "",
    });
    setErrors({});
    setIsSubmitting(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <Dialog.Root open={isOpen} onOpenChange={handleClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 dark:bg-black/70 z-50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-xl z-50 w-full max-w-4xl p-6 max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between mb-6">
            <Dialog.Title className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              Agregar Nuevo Caso
            </Dialog.Title>
            <Dialog.Close asChild>
              <button className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 cursor-pointer">
                <XMarkIcon className="h-6 w-6" />
              </button>
            </Dialog.Close>
          </div>

          {/* Horizontal Form Layout */}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Column - Basic Info */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                  Información Básica
                </h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <UserIcon className="h-4 w-4 inline mr-1" />
                    Título del Caso *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    className={`w-full px-3 py-2 border rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.title ? "border-red-500" : "border-gray-300 dark:border-gray-600"
                    }`}
                    placeholder="Ej: Despido sin causa - Empresa XYZ"
                  />
                  {errors.title && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.title}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <ScaleIcon className="h-4 w-4 inline mr-1" />
                    Tipo de Caso *
                  </label>
                  <select
                    value={formData.type}
                    onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value }))}
                    className={`w-full px-3 py-2 border rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.type ? "border-red-500" : "border-gray-300 dark:border-gray-600"
                    }`}
                  >
                    <option value="">Seleccionar tipo de caso</option>
                    {CASE_TYPES.map(type => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                  {errors.type && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.type}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <UserIcon className="h-4 w-4 inline mr-1" />
                    Cliente *
                  </label>
                  <input
                    type="text"
                    value={formData.client}
                    onChange={(e) => setFormData(prev => ({ ...prev, client: e.target.value }))}
                    className={`w-full px-3 py-2 border rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.client ? "border-red-500" : "border-gray-300 dark:border-gray-600"
                    }`}
                    placeholder="Nombre completo del cliente"
                  />
                  {errors.client && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.client}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Prioridad
                  </label>
                  <select
                    value={formData.priority}
                    onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as "low" | "medium" | "high" }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {PRIORITIES.map(priority => (
                      <option key={priority.value} value={priority.value}>
                        {priority.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Right Column - Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                  Detalles del Caso
                </h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Descripción del Caso *
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    rows={6}
                    className={`w-full px-3 py-2 border rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none ${
                      errors.description ? "border-red-500" : "border-gray-300 dark:border-gray-600"
                    }`}
                    placeholder="Describe los detalles del caso, antecedentes, situación actual..."
                  />
                  {errors.description && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.description}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Costo Estimado (Opcional)
                  </label>
                  <input
                    type="text"
                    value={formData.estimatedCost}
                    onChange={(e) => setFormData(prev => ({ ...prev, estimatedCost: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Ej: ARS 150,000 - 200,000"
                  />
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-end mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 space-x-4">
              <button
                type="button"
                onClick={handleClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer"
              >
                Cancelar
              </button>

              <button
                type="submit"
                disabled={isSubmitting}
                className="px-6 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer flex items-center"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Creando...
                  </>
                ) : (
                  "Crear Caso"
                )}
              </button>
            </div>
          </form>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
