"use client";

import { useState } from "react";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import {
  MagnifyingGlassIcon,
  ScaleIcon,
  ArrowTopRightOnSquareIcon,
} from "@heroicons/react/24/outline";
import { Jurisprudence } from "../../_lib/types";

interface JurisprudencePanelProps {
  caseType: string;
}

export function JurisprudencePanel({ caseType }: JurisprudencePanelProps) {
  const [jurisprudence, setJurisprudence] = useState<Jurisprudence[]>([]);
  const [loading, setLoading] = useState(false);
  const [searched, setSearched] = useState(false);

  const searchJurisprudence = async () => {
    setLoading(true);

    try {
      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1500));

      const response = await fetch("/data/jurisprudence.json");
      const allJurisprudence: Jurisprudence[] = await response.json();

      // Filter by case type and sort by relevance
      const filtered = allJurisprudence
        .filter((j) => j.caseType === caseType)
        .sort((a, b) => b.relevance - a.relevance)
        .slice(0, 3); // Show top 3 most relevant

      setJurisprudence(filtered);
      setSearched(true);
    } catch (error) {
      console.error("Error loading jurisprudence:", error);
    } finally {
      setLoading(false);
    }
  };

  const highlightText = (text: string, highlight: string) => {
    if (!highlight) return text;

    const parts = text.split(new RegExp(`(${highlight})`, "gi"));
    return parts.map((part, index) =>
      part.toLowerCase() === highlight.toLowerCase() ? (
        <mark key={index} className="bg-yellow-200 dark:bg-yellow-800 text-yellow-900 dark:text-yellow-200 px-1 rounded">
          {part}
        </mark>
      ) : (
        part
      )
    );
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
      {/* Search Button */}
      <div className="text-center mb-6">
        <button
          onClick={searchJurisprudence}
          disabled={loading}
          className="inline-flex items-center px-6 py-3 bg-blue-600 dark:bg-blue-500 text-white font-medium rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Buscando jurisprudencia...
            </>
          ) : (
            <>
              <MagnifyingGlassIcon className="h-5 w-5 mr-2" />
              Buscar jurisprudencia similar
            </>
          )}
        </button>
        <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
          Buscar casos similares de tipo: <strong>{caseType}</strong>
        </p>
      </div>

      {/* Results */}
      {searched && jurisprudence.length > 0 && (
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
            <ScaleIcon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400" />
            Jurisprudencia Relevante ({jurisprudence.length})
          </h4>

          {jurisprudence.map((item) => (
            <div
              key={item.id}
              className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:shadow-md dark:hover:shadow-lg transition-shadow bg-white dark:bg-gray-700"
            >
              <div className="flex items-start justify-between mb-2">
                <h5 className="font-medium text-gray-900 dark:text-gray-100 text-sm leading-tight">
                  {item.title}
                </h5>
                <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 ml-4">
                  <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full">
                    {Math.round(item.relevance * 100)}% relevante
                  </span>
                </div>
              </div>

              <div className="text-xs text-gray-500 dark:text-gray-400 mb-3 space-x-2">
                <span>{item.court}</span>
                <span>•</span>
                <span>
                  {format(new Date(item.date), "dd MMM yyyy", { locale: es })}
                </span>
              </div>

              <p className="text-sm text-gray-700 dark:text-gray-300 mb-3 leading-relaxed">
                {highlightText(item.excerpt, "relación laboral")}
              </p>

              <div className="flex items-center justify-between">
                <span className="bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 text-xs px-2 py-1 rounded-full">
                  {item.caseType}
                </span>
                <a
                  href={item.link}
                  className="inline-flex items-center text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
                >
                  Ver fallo completo
                  <ArrowTopRightOnSquareIcon className="h-3 w-3 ml-1" />
                </a>
              </div>
            </div>
          ))}
        </div>
      )}

      {searched && jurisprudence.length === 0 && !loading && (
        <div className="text-center text-gray-500 dark:text-gray-400 py-8">
          <ScaleIcon className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
          <p className="text-sm">
            No se encontró jurisprudencia relevante para casos de tipo{" "}
            <strong>{caseType}</strong>
          </p>
        </div>
      )}

      {!searched && !loading && (
        <div className="text-center text-gray-500 dark:text-gray-400 py-8">
          <ScaleIcon className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
          <p className="text-sm">
            Haz clic en el botón para buscar jurisprudencia similar a este caso
          </p>
        </div>
      )}
    </div>
  );
}
