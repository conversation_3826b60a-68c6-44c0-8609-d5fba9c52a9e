"use client";

import { useState, useEffect } from "react";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import { BellIcon, CheckIcon, TrashIcon } from "@heroicons/react/24/outline";
import { BellIcon as BellSolidIcon } from "@heroicons/react/24/solid";
import { useIsMounted } from "../../_lib/useIsomorphicDate";

interface Notification {
  id: string;
  title: string;
  message: string;
  type: "new_case" | "case_update" | "message" | "system" | "reminder";
  read: boolean;
  createdAt: string;
  caseId?: string;
}

interface NotificationDropdownProps {
  newCasesCount: number;
}

const notificationTypeColors = {
  new_case: "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200",
  case_update:
    "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200",
  message:
    "bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200",
  system: "bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200",
  reminder:
    "bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200",
};

const notificationTypeLabels = {
  new_case: "Nuevo Caso",
  case_update: "Actualización",
  message: "Mensaje",
  system: "Sistema",
  reminder: "Recordatorio",
};

export function NotificationDropdown({
  newCasesCount,
}: NotificationDropdownProps) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [open, setOpen] = useState(false);
  const isMounted = useIsMounted();

  useEffect(() => {
    const loadNotifications = async () => {
      try {
        // In a real app, this would come from an API
        // For now, we'll generate mock notifications

        const mockNotifications: Notification[] = [
          {
            id: "notif-001",
            title: "Nuevo caso asignado",
            message:
              "Se ha asignado el caso 'Despido sin causa – ACME S.A.' a tu cartera.",
            type: "new_case",
            read: false,
            createdAt: new Date("2024-01-15T10:00:00Z").toISOString(), // 2 hours ago
            caseId: "c-001",
          },
          {
            id: "notif-002",
            title: "Mensaje sin leer",
            message:
              "María López ha enviado un nuevo mensaje sobre el caso de filtración.",
            type: "message",
            read: false,
            createdAt: new Date("2024-01-15T08:00:00Z").toISOString(), // 4 hours ago
            caseId: "c-002",
          },
          {
            id: "notif-003",
            title: "Actualización de caso",
            message:
              "El caso de divorcio ha sido actualizado con nuevos documentos.",
            type: "case_update",
            read: true,
            createdAt: new Date("2024-01-14T12:00:00Z").toISOString(), // 1 day ago
            caseId: "c-003",
          },
          {
            id: "notif-004",
            title: "Recordatorio de audiencia",
            message:
              "Tienes una audiencia programada para mañana a las 10:00 AM.",
            type: "reminder",
            read: false,
            createdAt: new Date("2024-01-15T06:00:00Z").toISOString(), // 6 hours ago
          },
          {
            id: "notif-005",
            title: "Actualización del sistema",
            message: "El sistema se actualizará esta noche. Guarda tu trabajo.",
            type: "system",
            read: true,
            createdAt: new Date("2024-01-13T12:00:00Z").toISOString(), // 2 days ago
          },
        ];

        setNotifications(mockNotifications);
      } catch (error) {
        console.error("Error loading notifications:", error);
      } finally {
        setLoading(false);
      }
    };

    loadNotifications();
  }, []);

  const unreadCount = notifications.filter((n) => !n.read).length;
  // Use newCasesCount prop for badge display - only show badge when there are new cases
  const badgeCount = newCasesCount;

  const markAsRead = (notificationId: string) => {
    setNotifications((prev) =>
      prev.map((n) => (n.id === notificationId ? { ...n, read: true } : n))
    );
  };

  const markAllAsRead = () => {
    setNotifications((prev) => prev.map((n) => ({ ...n, read: true })));
  };

  const deleteNotification = (notificationId: string) => {
    setNotifications((prev) => prev.filter((n) => n.id !== notificationId));
  };

  const formatNotificationDate = (dateString: string) => {
    // Only calculate relative time after hydration to prevent SSR mismatch
    if (!isMounted) {
      return "Hace un momento";
    }

    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) {
      return "Hace unos minutos";
    } else if (diffInHours < 24) {
      return `Hace ${diffInHours} hora${diffInHours !== 1 ? "s" : ""}`;
    } else {
      return format(date, "dd MMM yyyy - HH:mm", { locale: es });
    }
  };

  return (
    <DropdownMenu.Root open={open} onOpenChange={setOpen}>
      <DropdownMenu.Trigger asChild>
        <button
          className="relative cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 mt-0.5 p-2 text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900 rounded-md transition-colors"
          aria-label="Notificaciones"
        >
          {badgeCount > 0 ? (
            <BellSolidIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          ) : (
            <BellIcon className="h-6 w-6" />
          )}
          {badgeCount > 0 && (
            <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs font-medium rounded-full flex items-center justify-center">
              {badgeCount > 9 ? "9+" : badgeCount}
            </span>
          )}
        </button>
      </DropdownMenu.Trigger>

      <DropdownMenu.Portal>
        <DropdownMenu.Content
          className="min-w-80 max-w-96 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-0 z-50"
          sideOffset={5}
          align="end"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Notificaciones
            </h3>
            {unreadCount > 0 && (
              <button
                onClick={markAllAsRead}
                className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium cursor-pointer"
              >
                Marcar todas como leídas
              </button>
            )}
          </div>

          {/* Content */}
          <div className="max-h-96 overflow-y-auto">
            {loading ? (
              <div className="flex justify-center items-center p-8">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              </div>
            ) : notifications.length === 0 ? (
              <div className="text-center py-8 px-4">
                <BellIcon className="h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-3" />
                <p className="text-gray-500 dark:text-gray-400 text-sm">
                  No tienes notificaciones
                </p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                      !notification.read ? "bg-blue-50 dark:bg-blue-900/10" : ""
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <span
                            className={`text-xs font-medium px-2 py-1 rounded-full ${
                              notificationTypeColors[notification.type]
                            }`}
                          >
                            {notificationTypeLabels[notification.type]}
                          </span>
                          {!notification.read && (
                            <div className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full"></div>
                          )}
                        </div>
                        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">
                          {notification.title}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                          {notification.message}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-500">
                          {formatNotificationDate(notification.createdAt)}
                        </p>
                      </div>
                      <div className="flex items-center space-x-1 ml-2">
                        {!notification.read && (
                          <button
                            onClick={() => markAsRead(notification.id)}
                            className="p-1 text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:hover:text-blue-400 transition-colors cursor-pointer"
                            title="Marcar como leída"
                          >
                            <CheckIcon className="h-4 w-4" />
                          </button>
                        )}
                        <button
                          onClick={() => deleteNotification(notification.id)}
                          className="p-1 text-gray-400 dark:text-gray-500 hover:text-red-600 dark:hover:text-red-400 transition-colors cursor-pointer"
                          title="Eliminar notificación"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          {notifications.length > 0 && (
            <div className="p-3 border-t border-gray-200 dark:border-gray-700">
              <button className="w-full text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium text-center cursor-pointer">
                Ver todas las notificaciones
              </button>
            </div>
          )}
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
}
