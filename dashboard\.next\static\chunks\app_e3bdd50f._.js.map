{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/cases/CaseCard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport { format } from \"date-fns\";\r\nimport { es } from \"date-fns/locale\";\r\nimport { useState } from \"react\";\r\nimport {\r\n  ClockIcon,\r\n  UserIcon,\r\n  ScaleIcon,\r\n  ChatBubbleLeftRightIcon,\r\n  ChevronDownIcon,\r\n  CheckIcon,\r\n} from \"@heroicons/react/24/outline\";\r\nimport { Case } from \"../../_lib/types\";\r\n\r\ninterface CaseCardProps {\r\n  case: Case;\r\n  onStatusChange?: (caseId: string, newStatus: \"new\" | \"in_progress\" | \"closed\") => void;\r\n}\r\n\r\nconst priorityColors = {\r\n  low: \"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200\",\r\n  medium:\r\n    \"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200\",\r\n  high: \"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200\",\r\n};\r\n\r\nconst typeColors = {\r\n  Laboral: \"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200\",\r\n  Civil:\r\n    \"bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200\",\r\n  Familia: \"bg-pink-100 dark:bg-pink-900 text-pink-800 dark:text-pink-200\",\r\n  Penal: \"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200\",\r\n  Comercial:\r\n    \"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200\",\r\n};\r\n\r\nexport function CaseCard({\r\n  case: caseData,\r\n  onStatusChange,\r\n}: CaseCardProps) {\r\n  const [showStatusDropdown, setShowStatusDropdown] = useState(false);\r\n  \r\n  const formattedDate = format(\r\n    new Date(caseData.createdAt),\r\n    \"dd MMM yyyy - HH:mm\",\r\n    {\r\n      locale: es,\r\n    }\r\n  );\r\n\r\n  // Verificar si es un caso creado por el usuario\r\n  const isUserCreatedCase = () => {\r\n    // Los casos creados por usuario tienen IDs con formato: c-xxxxxxxx (8 caracteres aleatorios)\r\n    // Los casos del JSON tienen IDs como: c-001, c-002, etc.\r\n    const idPattern = /^c-[a-f0-9]{8}$/i;\r\n    return idPattern.test(caseData.id);\r\n  };\r\n\r\n  const statusOptions = [\r\n    { value: \"new\" as const, label: \"Nuevo\", color: \"text-blue-600 dark:text-blue-400\" },\r\n    { value: \"in_progress\" as const, label: \"En Curso\", color: \"text-yellow-600 dark:text-yellow-400\" },\r\n    { value: \"closed\" as const, label: \"Cerrado\", color: \"text-green-600 dark:text-green-400\" },\r\n  ];\r\n\r\n  const currentStatus = statusOptions.find(option => option.value === caseData.status);\r\n\r\n  const handleStatusChange = (newStatus: \"new\" | \"in_progress\" | \"closed\", event: React.MouseEvent) => {\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n    \r\n    if (onStatusChange && newStatus !== caseData.status) {\r\n      onStatusChange(caseData.id, newStatus);\r\n    }\r\n    setShowStatusDropdown(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"group relative bg-white dark:bg-gray-800 border-0 rounded-xl p-5 shadow-sm hover:shadow-lg transition-all duration-200 overflow-hidden\">\r\n      {/* Gradient border effect */}\r\n      <div className=\"absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-200\" />\r\n\r\n      {/* Left border accent */}\r\n      <div className={`absolute left-0 top-0 bottom-0 w-1 rounded-l-xl ${\r\n        caseData.priority === \"high\"\r\n          ? \"bg-gradient-to-b from-red-500 to-red-600\"\r\n          : caseData.priority === \"medium\"\r\n          ? \"bg-gradient-to-b from-yellow-500 to-orange-500\"\r\n          : \"bg-gradient-to-b from-green-500 to-emerald-500\"\r\n      }`} />\r\n\r\n      <Link href={`/dashboard/${caseData.id}`} className=\"block relative z-10\">\r\n        <div className=\"space-y-4\">\r\n          {/* Header with priority and status dropdown */}\r\n          <div className=\"flex items-start justify-between\">\r\n            <div className=\"flex-1 pr-3\">\r\n              <h4 className=\"font-semibold text-gray-900 dark:text-gray-100 text-sm leading-tight line-clamp-2 mb-2\">\r\n                {caseData.title}\r\n              </h4>\r\n\r\n              {/* Type badge */}\r\n              <span\r\n                className={`inline-flex items-center text-xs font-medium px-2.5 py-1 rounded-lg ${\r\n                  typeColors[caseData.type as keyof typeof typeColors] ||\r\n                  \"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200\"\r\n                }`}\r\n              >\r\n                {caseData.type}\r\n              </span>\r\n            </div>\r\n\r\n            <div className=\"flex flex-col items-end space-y-2\">\r\n              {/* Priority badge */}\r\n              <span\r\n                className={`text-xs font-semibold px-2.5 py-1 rounded-lg shadow-sm ${\r\n                  priorityColors[caseData.priority]\r\n                }`}\r\n              >\r\n                {caseData.priority === \"high\"\r\n                  ? \"Alta\"\r\n                  : caseData.priority === \"medium\"\r\n                  ? \"Media\"\r\n                  : \"Baja\"}\r\n              </span>\r\n\r\n              {/* Status Dropdown - para todos los casos */}\r\n              {onStatusChange && (\r\n                <div className=\"relative\">\r\n                  <button\r\n                    onClick={(e) => {\r\n                      e.preventDefault();\r\n                      e.stopPropagation();\r\n                      setShowStatusDropdown(!showStatusDropdown);\r\n                    }}\r\n                    className={`flex items-center space-x-1 text-xs font-medium px-2.5 py-1 rounded-lg hover:opacity-80 transition-opacity ${currentStatus?.color} bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600`}\r\n                  >\r\n                    <span>{currentStatus?.label}</span>\r\n                    <ChevronDownIcon className=\"h-3 w-3\" />\r\n                  </button>\r\n\r\n                  {showStatusDropdown && (\r\n                    <>\r\n                      {/* Backdrop */}\r\n                      <div\r\n                        className=\"fixed inset-0 z-10\"\r\n                        onClick={(e) => {\r\n                          e.preventDefault();\r\n                          e.stopPropagation();\r\n                          setShowStatusDropdown(false);\r\n                        }}\r\n                      />\r\n                      \r\n                      {/* Dropdown */}\r\n                      <div className=\"absolute right-0 top-full mt-1 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 z-20 min-w-[120px]\">\r\n                        {statusOptions.map((option) => (\r\n                          <button\r\n                            key={option.value}\r\n                            onClick={(e) => handleStatusChange(option.value, e)}\r\n                            className={`w-full text-left px-3 py-2 text-xs hover:bg-gray-50 dark:hover:bg-gray-700 first:rounded-t-lg last:rounded-b-lg transition-colors flex items-center justify-between ${\r\n                              option.value === caseData.status ? 'bg-blue-50 dark:bg-blue-900/20' : ''\r\n                            }`}\r\n                          >\r\n                            <span className={option.color}>{option.label}</span>\r\n                            {option.value === caseData.status && (\r\n                              <CheckIcon className=\"h-3 w-3 text-blue-600 dark:text-blue-400\" />\r\n                            )}\r\n                          </button>\r\n                        ))}\r\n                      </div>\r\n                    </>\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Client info */}\r\n          <div className=\"flex items-center text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-700/50 rounded-lg px-3 py-2\">\r\n            <UserIcon className=\"h-4 w-4 mr-2 text-gray-400 dark:text-gray-500\" />\r\n            <span className=\"font-medium\">{caseData.client}</span>\r\n          </div>\r\n\r\n\r\n\r\n          {/* Footer with metadata */}\r\n          <div className=\"flex items-center justify-between pt-2 border-t border-gray-100 dark:border-gray-700\">\r\n            <div className=\"flex items-center text-xs text-gray-500 dark:text-gray-400\">\r\n              <ClockIcon className=\"h-3 w-3 mr-1\" />\r\n              {formattedDate}\r\n            </div>\r\n\r\n            {caseData.similarCount > 0 && (\r\n              <div className=\"flex items-center text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md\">\r\n                <ScaleIcon className=\"h-3 w-3 mr-1\" />\r\n                {caseData.similarCount} similares\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Messages indicator - solo para casos del sistema */}\r\n          {!isUserCreatedCase() && (() => {\r\n            const messageCount = caseData.unreadMessagesCount || 0;\r\n            const hasUnread = messageCount > 0;\r\n\r\n            return (\r\n              <div className={`relative flex items-center justify-center text-xs rounded-lg py-2 border ${\r\n                hasUnread\r\n                  ? \"text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800\"\r\n                  : \"text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800\"\r\n              }`}>\r\n                <ChatBubbleLeftRightIcon className=\"h-3 w-3 mr-1\" />\r\n                <span className=\"font-medium\">\r\n                  {hasUnread\r\n                    ? `${messageCount} mensaje${messageCount !== 1 ? \"s\" : \"\"} sin leer`\r\n                    : \"Sin mensajes pendientes\"\r\n                  }\r\n                </span>\r\n                {/* Punto rojo simple para mensajes sin leer */}\r\n                {hasUnread && (\r\n                  <div className=\"absolute -top-1 -right-1 h-2 w-2 bg-red-500 rounded-full\" />\r\n                )}\r\n              </div>\r\n            );\r\n          })()}\r\n        </div>\r\n      </Link>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAqBA,MAAM,iBAAiB;IACrB,KAAK;IACL,QACE;IACF,MAAM;AACR;AAEA,MAAM,aAAa;IACjB,SAAS;IACT,OACE;IACF,SAAS;IACT,OAAO;IACP,WACE;AACJ;AAEO,SAAS,SAAS,EACvB,MAAM,QAAQ,EACd,cAAc,EACA;;IACd,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,gBAAgB,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EACzB,IAAI,KAAK,SAAS,SAAS,GAC3B,uBACA;QACE,QAAQ,8IAAA,CAAA,KAAE;IACZ;IAGF,gDAAgD;IAChD,MAAM,oBAAoB;QACxB,6FAA6F;QAC7F,yDAAyD;QACzD,MAAM,YAAY;QAClB,OAAO,UAAU,IAAI,CAAC,SAAS,EAAE;IACnC;IAEA,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAgB,OAAO;YAAS,OAAO;QAAmC;QACnF;YAAE,OAAO;YAAwB,OAAO;YAAY,OAAO;QAAuC;QAClG;YAAE,OAAO;YAAmB,OAAO;YAAW,OAAO;QAAqC;KAC3F;IAED,MAAM,gBAAgB,cAAc,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK,SAAS,MAAM;IAEnF,MAAM,qBAAqB,CAAC,WAA6C;QACvE,MAAM,cAAc;QACpB,MAAM,eAAe;QAErB,IAAI,kBAAkB,cAAc,SAAS,MAAM,EAAE;YACnD,eAAe,SAAS,EAAE,EAAE;QAC9B;QACA,sBAAsB;IACxB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAW,CAAC,gDAAgD,EAC/D,SAAS,QAAQ,KAAK,SAClB,6CACA,SAAS,QAAQ,KAAK,WACtB,mDACA,kDACJ;;;;;;0BAEF,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAM,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE;gBAAE,WAAU;0BACjD,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,SAAS,KAAK;;;;;;sDAIjB,6LAAC;4CACC,WAAW,CAAC,oEAAoE,EAC9E,UAAU,CAAC,SAAS,IAAI,CAA4B,IACpD,iEACA;sDAED,SAAS,IAAI;;;;;;;;;;;;8CAIlB,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CACC,WAAW,CAAC,uDAAuD,EACjE,cAAc,CAAC,SAAS,QAAQ,CAAC,EACjC;sDAED,SAAS,QAAQ,KAAK,SACnB,SACA,SAAS,QAAQ,KAAK,WACtB,UACA;;;;;;wCAIL,gCACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS,CAAC;wDACR,EAAE,cAAc;wDAChB,EAAE,eAAe;wDACjB,sBAAsB,CAAC;oDACzB;oDACA,WAAW,CAAC,2GAA2G,EAAE,eAAe,MAAM,qEAAqE,CAAC;;sEAEpN,6LAAC;sEAAM,eAAe;;;;;;sEACtB,6LAAC,gOAAA,CAAA,kBAAe;4DAAC,WAAU;;;;;;;;;;;;gDAG5B,oCACC;;sEAEE,6LAAC;4DACC,WAAU;4DACV,SAAS,CAAC;gEACR,EAAE,cAAc;gEAChB,EAAE,eAAe;gEACjB,sBAAsB;4DACxB;;;;;;sEAIF,6LAAC;4DAAI,WAAU;sEACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC;oEAEC,SAAS,CAAC,IAAM,mBAAmB,OAAO,KAAK,EAAE;oEACjD,WAAW,CAAC,oKAAoK,EAC9K,OAAO,KAAK,KAAK,SAAS,MAAM,GAAG,mCAAmC,IACtE;;sFAEF,6LAAC;4EAAK,WAAW,OAAO,KAAK;sFAAG,OAAO,KAAK;;;;;;wEAC3C,OAAO,KAAK,KAAK,SAAS,MAAM,kBAC/B,6LAAC,oNAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;;mEARlB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAqBnC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,kNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAK,WAAU;8CAAe,SAAS,MAAM;;;;;;;;;;;;sCAMhD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCACpB;;;;;;;gCAGF,SAAS,YAAY,GAAG,mBACvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCACpB,SAAS,YAAY;wCAAC;;;;;;;;;;;;;wBAM5B,CAAC,uBAAuB,CAAC;4BACxB,MAAM,eAAe,SAAS,mBAAmB,IAAI;4BACrD,MAAM,YAAY,eAAe;4BAEjC,qBACE,6LAAC;gCAAI,WAAW,CAAC,yEAAyE,EACxF,YACI,mGACA,8GACJ;;kDACA,6LAAC,gPAAA,CAAA,0BAAuB;wCAAC,WAAU;;;;;;kDACnC,6LAAC;wCAAK,WAAU;kDACb,YACG,GAAG,aAAa,QAAQ,EAAE,iBAAiB,IAAI,MAAM,GAAG,SAAS,CAAC,GAClE;;;;;;oCAIL,2BACC,6LAAC;wCAAI,WAAU;;;;;;;;;;;;wBAIvB,CAAC;;;;;;;;;;;;;;;;;;AAKX;GA/LgB;KAAA", "debugId": null}}, {"offset": {"line": 373, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/modals/AddCaseModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport {\n  XMarkIcon,\n  UserIcon,\n  ScaleIcon,\n} from \"@heroicons/react/24/outline\";\nimport * as Dialog from \"@radix-ui/react-dialog\";\nimport { Case } from \"../../_lib/types\";\n\ninterface AddCaseModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onCaseAdded: (newCase: Case) => void;\n}\n\ninterface FormData {\n  title: string;\n  type: string;\n  client: string;\n  description: string;\n  priority: \"low\" | \"medium\" | \"high\";\n  estimatedCost: string;\n}\n\nconst CASE_TYPES = [\n  \"Laboral\",\n  \"Civil\",\n  \"Familia\",\n  \"Penal\",\n  \"Comercial\",\n  \"Administrativo\",\n  \"Constitucional\",\n  \"Tributario\",\n];\n\nconst PRIORITIES = [\n  { value: \"low\", label: \"Baja\", color: \"text-green-600 dark:text-green-400\" },\n  { value: \"medium\", label: \"Media\", color: \"text-yellow-600 dark:text-yellow-400\" },\n  { value: \"high\", label: \"Alta\", color: \"text-red-600 dark:text-red-400\" },\n];\n\nexport function AddCaseModal({ isOpen, onClose, onCaseAdded }: AddCaseModalProps) {\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [formData, setFormData] = useState<FormData>({\n    title: \"\",\n    type: \"\",\n    client: \"\",\n    description: \"\",\n    priority: \"medium\",\n    estimatedCost: \"\",\n  });\n\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  const validateForm = (): boolean => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.title.trim()) newErrors.title = \"El título es obligatorio\";\n    if (!formData.type) newErrors.type = \"El tipo de caso es obligatorio\";\n    if (!formData.client.trim()) newErrors.client = \"El nombre del cliente es obligatorio\";\n    if (!formData.description.trim()) newErrors.description = \"La descripción es obligatoria\";\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!validateForm()) return;\n    setIsSubmitting(true);\n\n    try {\n      // Crear el nuevo caso\n      const newCase: Case = {\n        id: `c-${crypto.randomUUID().slice(0, 8)}`,\n        title: formData.title,\n        type: formData.type,\n        client: formData.client,\n        status: \"new\",\n        progress: 0,\n        createdAt: new Date().toISOString(),\n        similarCount: 0,\n        description: formData.description,\n        priority: formData.priority,\n        estimatedCost: formData.estimatedCost || undefined,\n        complexityScore: Math.floor(Math.random() * 10) + 1, // Simulado\n        riskAssessment: formData.priority === \"high\" ? \"high\" : formData.priority === \"low\" ? \"low\" : \"medium\",\n        successProbability: Math.floor(Math.random() * 30) + 70, // 70-100%\n        aiSummary: `Caso de ${formData.type.toLowerCase()} con prioridad ${formData.priority === \"high\" ? \"alta\" : formData.priority === \"medium\" ? \"media\" : \"baja\"}. ${formData.description.slice(0, 100)}${formData.description.length > 100 ? \"...\" : \"\"}`,\n        keyFacts: [\n          `Tipo de caso: ${formData.type}`,\n          `Cliente: ${formData.client}`,\n          `Prioridad: ${formData.priority === \"high\" ? \"Alta\" : formData.priority === \"medium\" ? \"Media\" : \"Baja\"}`,\n          formData.estimatedCost ? `Costo estimado: ${formData.estimatedCost}` : \"Costo por definir\",\n        ],\n        nextActions: [\n          \"Revisar documentación inicial\",\n          \"Programar reunión con el cliente\",\n          \"Definir estrategia legal\",\n          \"Establecer cronograma de trabajo\",\n        ],\n        milestones: [],\n        messages: [\n          {\n            id: `msg-${crypto.randomUUID()}`,\n            sender: \"lawyer\",\n            content: `Hola ${formData.client}, he creado tu caso \"${formData.title}\". Estaré en contacto contigo para coordinar los próximos pasos.`,\n            timestamp: new Date().toISOString(),\n            status: \"sent\",\n          },\n        ],\n        documents: [],\n        activities: [\n          {\n            id: `act-${crypto.randomUUID()}`,\n            type: \"status_change\",\n            description: \"Caso creado\",\n            timestamp: new Date().toISOString(),\n            user: \"lawyer\",\n          },\n        ],\n        unreadMessagesCount: 0,\n      };\n\n      // Simular delay de creación\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      // Guardar en sessionStorage (persistencia durante la sesión)\n      const existingCases = JSON.parse(sessionStorage.getItem(\"cases\") || \"[]\");\n      const updatedCases = [newCase, ...existingCases];\n      sessionStorage.setItem(\"cases\", JSON.stringify(updatedCases));\n\n      // Notificar al componente padre\n      onCaseAdded(newCase);\n\n      // Cerrar modal directamente después de crear el caso\n      handleClose();\n    } catch (error) {\n      console.error(\"Error al crear el caso:\", error);\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleClose = () => {\n    // Reset form\n    setFormData({\n      title: \"\",\n      type: \"\",\n      client: \"\",\n      description: \"\",\n      priority: \"medium\",\n      estimatedCost: \"\",\n    });\n    setErrors({});\n    setIsSubmitting(false);\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <Dialog.Root open={isOpen} onOpenChange={handleClose}>\n      <Dialog.Portal>\n        <Dialog.Overlay className=\"fixed inset-0 bg-black/50 dark:bg-black/70 z-50\" />\n        <Dialog.Content className=\"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-xl z-50 w-full max-w-4xl p-6 max-h-[90vh] overflow-y-auto\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <Dialog.Title className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\n              Agregar Nuevo Caso\n            </Dialog.Title>\n            <Dialog.Close asChild>\n              <button className=\"text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 cursor-pointer\">\n                <XMarkIcon className=\"h-6 w-6\" />\n              </button>\n            </Dialog.Close>\n          </div>\n\n          {/* Horizontal Form Layout */}\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n              {/* Left Column - Basic Info */}\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">\n                  Información Básica\n                </h3>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    <UserIcon className=\"h-4 w-4 inline mr-1\" />\n                    Título del Caso *\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.title}\n                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}\n                    className={`w-full px-3 py-2 border rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                      errors.title ? \"border-red-500\" : \"border-gray-300 dark:border-gray-600\"\n                    }`}\n                    placeholder=\"Ej: Despido sin causa - Empresa XYZ\"\n                  />\n                  {errors.title && (\n                    <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.title}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    <ScaleIcon className=\"h-4 w-4 inline mr-1\" />\n                    Tipo de Caso *\n                  </label>\n                  <select\n                    value={formData.type}\n                    onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value }))}\n                    className={`w-full px-3 py-2 border rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                      errors.type ? \"border-red-500\" : \"border-gray-300 dark:border-gray-600\"\n                    }`}\n                  >\n                    <option value=\"\">Seleccionar tipo de caso</option>\n                    {CASE_TYPES.map(type => (\n                      <option key={type} value={type}>{type}</option>\n                    ))}\n                  </select>\n                  {errors.type && (\n                    <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.type}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    <UserIcon className=\"h-4 w-4 inline mr-1\" />\n                    Cliente *\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.client}\n                    onChange={(e) => setFormData(prev => ({ ...prev, client: e.target.value }))}\n                    className={`w-full px-3 py-2 border rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                      errors.client ? \"border-red-500\" : \"border-gray-300 dark:border-gray-600\"\n                    }`}\n                    placeholder=\"Nombre completo del cliente\"\n                  />\n                  {errors.client && (\n                    <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.client}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Prioridad\n                  </label>\n                  <select\n                    value={formData.priority}\n                    onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as \"low\" | \"medium\" | \"high\" }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  >\n                    {PRIORITIES.map(priority => (\n                      <option key={priority.value} value={priority.value}>\n                        {priority.label}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n              </div>\n\n              {/* Right Column - Details */}\n              <div className=\"space-y-4\">\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">\n                  Detalles del Caso\n                </h3>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Descripción del Caso *\n                  </label>\n                  <textarea\n                    value={formData.description}\n                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n                    rows={6}\n                    className={`w-full px-3 py-2 border rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none ${\n                      errors.description ? \"border-red-500\" : \"border-gray-300 dark:border-gray-600\"\n                    }`}\n                    placeholder=\"Describe los detalles del caso, antecedentes, situación actual...\"\n                  />\n                  {errors.description && (\n                    <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.description}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Costo Estimado (Opcional)\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.estimatedCost}\n                    onChange={(e) => setFormData(prev => ({ ...prev, estimatedCost: e.target.value }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    placeholder=\"Ej: ARS 150,000 - 200,000\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"flex items-center justify-end mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 space-x-4\">\n              <button\n                type=\"button\"\n                onClick={handleClose}\n                className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer\"\n              >\n                Cancelar\n              </button>\n\n              <button\n                type=\"submit\"\n                disabled={isSubmitting}\n                className=\"px-6 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer flex items-center\"\n              >\n                {isSubmitting ? (\n                  <>\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                    Creando...\n                  </>\n                ) : (\n                  \"Crear Caso\"\n                )}\n              </button>\n            </div>\n          </form>\n        </Dialog.Content>\n      </Dialog.Portal>\n    </Dialog.Root>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAKA;;;AARA;;;;AA0BA,MAAM,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,aAAa;IACjB;QAAE,OAAO;QAAO,OAAO;QAAQ,OAAO;IAAqC;IAC3E;QAAE,OAAO;QAAU,OAAO;QAAS,OAAO;IAAuC;IACjF;QAAE,OAAO;QAAQ,OAAO;QAAQ,OAAO;IAAiC;CACzE;AAEM,SAAS,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAqB;;IAC9E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,OAAO;QACP,MAAM;QACN,QAAQ;QACR,aAAa;QACb,UAAU;QACV,eAAe;IACjB;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI,UAAU,KAAK,GAAG;QAC9C,IAAI,CAAC,SAAS,IAAI,EAAE,UAAU,IAAI,GAAG;QACrC,IAAI,CAAC,SAAS,MAAM,CAAC,IAAI,IAAI,UAAU,MAAM,GAAG;QAChD,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI,UAAU,WAAW,GAAG;QAE1D,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,gBAAgB;QACrB,gBAAgB;QAEhB,IAAI;YACF,sBAAsB;YACtB,MAAM,UAAgB;gBACpB,IAAI,CAAC,EAAE,EAAE,OAAO,UAAU,GAAG,KAAK,CAAC,GAAG,IAAI;gBAC1C,OAAO,SAAS,KAAK;gBACrB,MAAM,SAAS,IAAI;gBACnB,QAAQ,SAAS,MAAM;gBACvB,QAAQ;gBACR,UAAU;gBACV,WAAW,IAAI,OAAO,WAAW;gBACjC,cAAc;gBACd,aAAa,SAAS,WAAW;gBACjC,UAAU,SAAS,QAAQ;gBAC3B,eAAe,SAAS,aAAa,IAAI;gBACzC,iBAAiB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBAClD,gBAAgB,SAAS,QAAQ,KAAK,SAAS,SAAS,SAAS,QAAQ,KAAK,QAAQ,QAAQ;gBAC9F,oBAAoB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBACrD,WAAW,CAAC,QAAQ,EAAE,SAAS,IAAI,CAAC,WAAW,GAAG,eAAe,EAAE,SAAS,QAAQ,KAAK,SAAS,SAAS,SAAS,QAAQ,KAAK,WAAW,UAAU,OAAO,EAAE,EAAE,SAAS,WAAW,CAAC,KAAK,CAAC,GAAG,OAAO,SAAS,WAAW,CAAC,MAAM,GAAG,MAAM,QAAQ,IAAI;gBACtP,UAAU;oBACR,CAAC,cAAc,EAAE,SAAS,IAAI,EAAE;oBAChC,CAAC,SAAS,EAAE,SAAS,MAAM,EAAE;oBAC7B,CAAC,WAAW,EAAE,SAAS,QAAQ,KAAK,SAAS,SAAS,SAAS,QAAQ,KAAK,WAAW,UAAU,QAAQ;oBACzG,SAAS,aAAa,GAAG,CAAC,gBAAgB,EAAE,SAAS,aAAa,EAAE,GAAG;iBACxE;gBACD,aAAa;oBACX;oBACA;oBACA;oBACA;iBACD;gBACD,YAAY,EAAE;gBACd,UAAU;oBACR;wBACE,IAAI,CAAC,IAAI,EAAE,OAAO,UAAU,IAAI;wBAChC,QAAQ;wBACR,SAAS,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,qBAAqB,EAAE,SAAS,KAAK,CAAC,gEAAgE,CAAC;wBACxI,WAAW,IAAI,OAAO,WAAW;wBACjC,QAAQ;oBACV;iBACD;gBACD,WAAW,EAAE;gBACb,YAAY;oBACV;wBACE,IAAI,CAAC,IAAI,EAAE,OAAO,UAAU,IAAI;wBAChC,MAAM;wBACN,aAAa;wBACb,WAAW,IAAI,OAAO,WAAW;wBACjC,MAAM;oBACR;iBACD;gBACD,qBAAqB;YACvB;YAEA,4BAA4B;YAC5B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,6DAA6D;YAC7D,MAAM,gBAAgB,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC,YAAY;YACpE,MAAM,eAAe;gBAAC;mBAAY;aAAc;YAChD,eAAe,OAAO,CAAC,SAAS,KAAK,SAAS,CAAC;YAE/C,gCAAgC;YAChC,YAAY;YAEZ,qDAAqD;YACrD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc;QAClB,aAAa;QACb,YAAY;YACV,OAAO;YACP,MAAM;YACN,QAAQ;YACR,aAAa;YACb,UAAU;YACV,eAAe;QACjB;QACA,UAAU,CAAC;QACX,gBAAgB;QAChB;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC,qKAAA,CAAA,OAAW;QAAC,MAAM;QAAQ,cAAc;kBACvC,cAAA,6LAAC,qKAAA,CAAA,SAAa;;8BACZ,6LAAC,qKAAA,CAAA,UAAc;oBAAC,WAAU;;;;;;8BAC1B,6LAAC,qKAAA,CAAA,UAAc;oBAAC,WAAU;;sCACxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qKAAA,CAAA,QAAY;oCAAC,WAAU;8CAAyD;;;;;;8CAGjF,6LAAC,qKAAA,CAAA,QAAY;oCAAC,OAAO;8CACnB,cAAA,6LAAC;wCAAO,WAAU;kDAChB,cAAA,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAM3B,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA4D;;;;;;8DAI1E,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;;8EACf,6LAAC,kNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAwB;;;;;;;sEAG9C,6LAAC;4DACC,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DACxE,WAAW,CAAC,kOAAkO,EAC5O,OAAO,KAAK,GAAG,mBAAmB,wCAClC;4DACF,aAAY;;;;;;wDAEb,OAAO,KAAK,kBACX,6LAAC;4DAAE,WAAU;sEAA+C,OAAO,KAAK;;;;;;;;;;;;8DAI5E,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;;8EACf,6LAAC,oNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;gEAAwB;;;;;;;sEAG/C,6LAAC;4DACC,OAAO,SAAS,IAAI;4DACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DACvE,WAAW,CAAC,mLAAmL,EAC7L,OAAO,IAAI,GAAG,mBAAmB,wCACjC;;8EAEF,6LAAC;oEAAO,OAAM;8EAAG;;;;;;gEAChB,WAAW,GAAG,CAAC,CAAA,qBACd,6LAAC;wEAAkB,OAAO;kFAAO;uEAApB;;;;;;;;;;;wDAGhB,OAAO,IAAI,kBACV,6LAAC;4DAAE,WAAU;sEAA+C,OAAO,IAAI;;;;;;;;;;;;8DAI3E,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;;8EACf,6LAAC,kNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAwB;;;;;;;sEAG9C,6LAAC;4DACC,MAAK;4DACL,OAAO,SAAS,MAAM;4DACtB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DACzE,WAAW,CAAC,kOAAkO,EAC5O,OAAO,MAAM,GAAG,mBAAmB,wCACnC;4DACF,aAAY;;;;;;wDAEb,OAAO,MAAM,kBACZ,6LAAC;4DAAE,WAAU;sEAA+C,OAAO,MAAM;;;;;;;;;;;;8DAI7E,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAAkE;;;;;;sEAGnF,6LAAC;4DACC,OAAO,SAAS,QAAQ;4DACxB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oEAA8B,CAAC;4DACxG,WAAU;sEAET,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC;oEAA4B,OAAO,SAAS,KAAK;8EAC/C,SAAS,KAAK;mEADJ,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;sDASnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA4D;;;;;;8DAI1E,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAAkE;;;;;;sEAGnF,6LAAC;4DACC,OAAO,SAAS,WAAW;4DAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DAC9E,MAAM;4DACN,WAAW,CAAC,8OAA8O,EACxP,OAAO,WAAW,GAAG,mBAAmB,wCACxC;4DACF,aAAY;;;;;;wDAEb,OAAO,WAAW,kBACjB,6LAAC;4DAAE,WAAU;sEAA+C,OAAO,WAAW;;;;;;;;;;;;8DAIlF,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAAkE;;;;;;sEAGnF,6LAAC;4DACC,MAAK;4DACL,OAAO,SAAS,aAAa;4DAC7B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,eAAe,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DAChF,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;8CAOpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;sDAID,6LAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,6BACC;;kEACE,6LAAC;wDAAI,WAAU;;;;;;oDAAuE;;+DAIxF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB;GAnSgB;KAAA", "debugId": null}}, {"offset": {"line": 963, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/cases/CaseList.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { CaseCard } from \"./CaseCard\";\r\nimport { Case } from \"../../_lib/types\";\r\nimport { ChevronLeftIcon, ChevronRightIcon, PlusIcon } from \"@heroicons/react/24/outline\";\r\nimport { AddCaseModal } from \"../modals/AddCaseModal\";\r\n\r\nconst columns = [\r\n  { id: \"new\", title: \"Nuevos\", status: \"new\" as const },\r\n  { id: \"in_progress\", title: \"En Curso\", status: \"in_progress\" as const },\r\n  { id: \"closed\", title: \"Cerrados\", status: \"closed\" as const },\r\n];\r\n\r\n\r\n\r\nexport function CaseList() {\r\n  const [cases, setCases] = useState<Case[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [activeMobileTab, setActiveMobileTab] = useState(0); // Para navegación móvil\r\n  const [showAddCaseModal, setShowAddCaseModal] = useState(false);\r\n\r\n  // Load cases from JSON file and localStorage\r\n  useEffect(() => {\r\n    const loadCases = async () => {\r\n      try {\r\n        // Cargar casos desde JSON\r\n        const response = await fetch(\"/data/cases.json\");\r\n        const casesData = await response.json();\r\n\r\n        // Cargar casos adicionales desde sessionStorage\r\n        const localCases = JSON.parse(sessionStorage.getItem(\"cases\") || \"[]\");\r\n\r\n        // Combinar casos (localStorage primero para que aparezcan arriba)\r\n        const allCases = [...localCases, ...casesData];\r\n        setCases(allCases);\r\n      } catch (error) {\r\n        console.error(\"Error loading cases:\", error);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadCases();\r\n  }, []);\r\n\r\n  const handleCaseAdded = (newCase: Case) => {\r\n    // Agregar el nuevo caso al principio de la lista\r\n    setCases(prev => [newCase, ...prev]);\r\n    setShowAddCaseModal(false);\r\n  };\r\n\r\n  const handleStatusChange = (caseId: string, newStatus: \"new\" | \"in_progress\" | \"closed\") => {\r\n    setCases(prevCases => \r\n      prevCases.map(case_ => \r\n        case_.id === caseId \r\n          ? { ...case_, status: newStatus }\r\n          : case_\r\n      )\r\n    );\r\n\r\n    // También actualizar en sessionStorage si es un caso creado por el usuario\r\n    const localCases = JSON.parse(sessionStorage.getItem(\"cases\") || \"[]\");\r\n    const updatedLocalCases = localCases.map((case_: Case) => \r\n      case_.id === caseId \r\n        ? { ...case_, status: newStatus }\r\n        : case_\r\n    );\r\n    \r\n    if (updatedLocalCases.some((case_: Case) => case_.id === caseId)) {\r\n      sessionStorage.setItem(\"cases\", JSON.stringify(updatedLocalCases));\r\n    }\r\n  };\r\n\r\n  const getCasesByStatus = (status: string) => {\r\n    return cases.filter((case_) => case_.status === status);\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex justify-center items-center h-64\">\r\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-400\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header con botón de agregar caso */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div>\r\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\r\n            Gestión de Casos\r\n          </h2>\r\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\r\n            {cases.length} casos en total\r\n          </p>\r\n        </div>\r\n        <button\r\n          onClick={() => setShowAddCaseModal(true)}\r\n          className=\"flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900 transition-colors cursor-pointer\"\r\n        >\r\n          <PlusIcon className=\"h-4 w-4 mr-2\" />\r\n          Agregar Caso\r\n        </button>\r\n      </div>\r\n\r\n      <div className=\"space-y-4\">\r\n        {/* Desktop: 3 columnas | Tablet: 2 columnas + scroll | Móvil: Tabs */}\r\n\r\n        {/* Navegación móvil con tabs (solo visible en móvil) */}\r\n        <div className=\"block sm:hidden mb-4\">\r\n          <div className=\"flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1\">\r\n            {columns.map((column, index) => {\r\n              const columnCases = getCasesByStatus(column.status);\r\n              return (\r\n                <button\r\n                  key={column.id}\r\n                  onClick={() => setActiveMobileTab(index)}\r\n                  className={`flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-md text-sm font-medium transition-all ${\r\n                    activeMobileTab === index\r\n                      ? \"bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm\"\r\n                      : \"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200\"\r\n                  }`}\r\n                >\r\n                  <span>{column.title}</span>\r\n                  <span className={`text-xs px-2 py-0.5 rounded-full ${\r\n                    activeMobileTab === index\r\n                      ? \"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200\"\r\n                      : \"bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300\"\r\n                  }`}>\r\n                    {columnCases.length}\r\n                  </span>\r\n                </button>\r\n              );\r\n            })}\r\n          </div>\r\n\r\n          {/* Navegación con flechas */}\r\n          <div className=\"flex justify-between items-center mt-3\">\r\n            <button\r\n              onClick={() => setActiveMobileTab(Math.max(0, activeMobileTab - 1))}\r\n              disabled={activeMobileTab === 0}\r\n              className=\"flex items-center space-x-1 px-3 py-2 text-sm text-gray-600 dark:text-gray-400 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            >\r\n              <ChevronLeftIcon className=\"h-4 w-4\" />\r\n              <span>Anterior</span>\r\n            </button>\r\n\r\n            <div className=\"flex space-x-1\">\r\n              {columns.map((_, index) => (\r\n                <div\r\n                  key={index}\r\n                  className={`w-2 h-2 rounded-full transition-all ${\r\n                    activeMobileTab === index\r\n                      ? \"bg-blue-500 dark:bg-blue-400\"\r\n                      : \"bg-gray-300 dark:bg-gray-600\"\r\n                  }`}\r\n                />\r\n              ))}\r\n            </div>\r\n\r\n            <button\r\n              onClick={() => setActiveMobileTab(Math.min(columns.length - 1, activeMobileTab + 1))}\r\n              disabled={activeMobileTab === columns.length - 1}\r\n              className=\"flex items-center space-x-1 px-3 py-2 text-sm text-gray-600 dark:text-gray-400 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            >\r\n              <span>Siguiente</span>\r\n              <ChevronRightIcon className=\"h-4 w-4\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Layout responsivo */}\r\n        <div className=\"hidden sm:block\">\r\n          {/* Desktop: Grid de 3 columnas */}\r\n          <div className=\"hidden lg:grid lg:grid-cols-3 lg:gap-6\">\r\n            {columns.map((column) => {\r\n              const columnCases = getCasesByStatus(column.status);\r\n              return (\r\n                <div\r\n                  key={column.id}\r\n                  className=\"bg-white dark:bg-gray-800 rounded-xl border-0 shadow-sm hover:shadow-md transition-all duration-200 p-5\"\r\n                >\r\n                  <div className=\"flex items-center justify-between mb-5\">\r\n                    <h3 className=\"font-bold text-gray-900 dark:text-gray-100 text-lg\">\r\n                      {column.title}\r\n                    </h3>\r\n                    <span className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-sm\">\r\n                      {columnCases.length}\r\n                    </span>\r\n                  </div>\r\n\r\n                  <div className=\"space-y-4 min-h-[300px] p-3 rounded-xl bg-gray-50/50 dark:bg-gray-900/50\">\r\n                    {columnCases.map((case_) => (\r\n                      <CaseCard \r\n                        key={case_.id} \r\n                        case={case_} \r\n                        onStatusChange={handleStatusChange}\r\n                      />\r\n                    ))}\r\n                    {columnCases.length === 0 && (\r\n                      <div className=\"flex flex-col items-center justify-center text-center text-gray-400 dark:text-gray-500 py-12 space-y-3\">\r\n                        <div className=\"w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center\">\r\n                          <svg className=\"w-8 h-8 text-gray-300 dark:text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n                          </svg>\r\n                        </div>\r\n                        <div>\r\n                          <p className=\"font-medium\">No hay casos</p>\r\n                          <p className=\"text-xs\">Los casos aparecerán aquí</p>\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n\r\n          {/* Tablet: Scroll horizontal */}\r\n          <div className=\"lg:hidden overflow-x-auto pb-4\">\r\n            <div className=\"flex space-x-4 min-w-max\">\r\n              {columns.map((column) => {\r\n                const columnCases = getCasesByStatus(column.status);\r\n                return (\r\n                  <div\r\n                    key={column.id}\r\n                    className=\"bg-white dark:bg-gray-800 rounded-xl border-0 shadow-sm hover:shadow-md transition-all duration-200 p-5 w-80 flex-shrink-0\"\r\n                  >\r\n                    <div className=\"flex items-center justify-between mb-5\">\r\n                      <h3 className=\"font-bold text-gray-900 dark:text-gray-100 text-lg\">\r\n                        {column.title}\r\n                      </h3>\r\n                      <span className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-sm\">\r\n                        {columnCases.length}\r\n                      </span>\r\n                    </div>\r\n\r\n                    <div className=\"space-y-4 min-h-[300px] p-3 rounded-xl bg-gray-50/50 dark:bg-gray-900/50\">\r\n                      {columnCases.map((case_) => (\r\n                        <CaseCard \r\n                          key={case_.id} \r\n                          case={case_} \r\n                          onStatusChange={handleStatusChange}\r\n                        />\r\n                      ))}\r\n                      {columnCases.length === 0 && (\r\n                        <div className=\"flex flex-col items-center justify-center text-center text-gray-400 dark:text-gray-500 py-12 space-y-3\">\r\n                          <div className=\"w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center\">\r\n                            <svg className=\"w-8 h-8 text-gray-300 dark:text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n                            </svg>\r\n                          </div>\r\n                          <div>\r\n                            <p className=\"font-medium\">No hay casos</p>\r\n                            <p className=\"text-xs\">Los casos aparecerán aquí</p>\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n\r\n        {/* Vista móvil: Solo mostrar la columna activa */}\r\n        <div className=\"block sm:hidden\">\r\n          {(() => {\r\n            const activeColumn = columns[activeMobileTab];\r\n            const columnCases = getCasesByStatus(activeColumn.status);\r\n\r\n            return (\r\n              <div className=\"bg-white dark:bg-gray-800 rounded-xl border-0 shadow-sm p-5\">\r\n                <div className=\"flex items-center justify-between mb-5\">\r\n                  <h3 className=\"font-bold text-gray-900 dark:text-gray-100 text-lg\">\r\n                    {activeColumn.title}\r\n                  </h3>\r\n                  <span className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-sm\">\r\n                    {columnCases.length}\r\n                  </span>\r\n                </div>\r\n\r\n                <div className=\"space-y-4 min-h-[300px] p-3 rounded-xl bg-gray-50/50 dark:bg-gray-900/50\">\r\n                  {columnCases.map((case_) => (\r\n                    <CaseCard \r\n                      key={case_.id} \r\n                      case={case_} \r\n                      onStatusChange={handleStatusChange}\r\n                    />\r\n                  ))}\r\n                  {columnCases.length === 0 && (\r\n                    <div className=\"flex flex-col items-center justify-center text-center text-gray-400 dark:text-gray-500 py-12 space-y-3\">\r\n                      <div className=\"w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center\">\r\n                        <svg className=\"w-8 h-8 text-gray-300 dark:text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n                        </svg>\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"font-medium\">No hay casos</p>\r\n                        <p className=\"text-xs\">Los casos aparecerán aquí</p>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            );\r\n          })()}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Modal para agregar caso */}\r\n      <AddCaseModal\r\n        isOpen={showAddCaseModal}\r\n        onClose={() => setShowAddCaseModal(false)}\r\n        onCaseAdded={handleCaseAdded}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;AACA;;;AANA;;;;;AAQA,MAAM,UAAU;IACd;QAAE,IAAI;QAAO,OAAO;QAAU,QAAQ;IAAe;IACrD;QAAE,IAAI;QAAe,OAAO;QAAY,QAAQ;IAAuB;IACvE;QAAE,IAAI;QAAU,OAAO;QAAY,QAAQ;IAAkB;CAC9D;AAIM,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,wBAAwB;IACnF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM;gDAAY;oBAChB,IAAI;wBACF,0BAA0B;wBAC1B,MAAM,WAAW,MAAM,MAAM;wBAC7B,MAAM,YAAY,MAAM,SAAS,IAAI;wBAErC,gDAAgD;wBAChD,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC,YAAY;wBAEjE,kEAAkE;wBAClE,MAAM,WAAW;+BAAI;+BAAe;yBAAU;wBAC9C,SAAS;oBACX,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,wBAAwB;oBACxC,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;6BAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,iDAAiD;QACjD,SAAS,CAAA,OAAQ;gBAAC;mBAAY;aAAK;QACnC,oBAAoB;IACtB;IAEA,MAAM,qBAAqB,CAAC,QAAgB;QAC1C,SAAS,CAAA,YACP,UAAU,GAAG,CAAC,CAAA,QACZ,MAAM,EAAE,KAAK,SACT;oBAAE,GAAG,KAAK;oBAAE,QAAQ;gBAAU,IAC9B;QAIR,2EAA2E;QAC3E,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC,YAAY;QACjE,MAAM,oBAAoB,WAAW,GAAG,CAAC,CAAC,QACxC,MAAM,EAAE,KAAK,SACT;gBAAE,GAAG,KAAK;gBAAE,QAAQ;YAAU,IAC9B;QAGN,IAAI,kBAAkB,IAAI,CAAC,CAAC,QAAgB,MAAM,EAAE,KAAK,SAAS;YAChE,eAAe,OAAO,CAAC,SAAS,KAAK,SAAS,CAAC;QACjD;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAO,MAAM,MAAM,CAAC,CAAC,QAAU,MAAM,MAAM,KAAK;IAClD;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAGvE,6LAAC;gCAAE,WAAU;;oCACV,MAAM,MAAM;oCAAC;;;;;;;;;;;;;kCAGlB,6LAAC;wBACC,SAAS,IAAM,oBAAoB;wBACnC,WAAU;;0CAEV,6LAAC,kNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAKzC,6LAAC;gBAAI,WAAU;;kCAIb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ;oCACpB,MAAM,cAAc,iBAAiB,OAAO,MAAM;oCAClD,qBACE,6LAAC;wCAEC,SAAS,IAAM,mBAAmB;wCAClC,WAAW,CAAC,0GAA0G,EACpH,oBAAoB,QAChB,yEACA,iFACJ;;0DAEF,6LAAC;0DAAM,OAAO,KAAK;;;;;;0DACnB,6LAAC;gDAAK,WAAW,CAAC,iCAAiC,EACjD,oBAAoB,QAChB,kEACA,iEACJ;0DACC,YAAY,MAAM;;;;;;;uCAdhB,OAAO,EAAE;;;;;gCAkBpB;;;;;;0CAIF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,mBAAmB,KAAK,GAAG,CAAC,GAAG,kBAAkB;wCAChE,UAAU,oBAAoB;wCAC9B,WAAU;;0DAEV,6LAAC,gOAAA,CAAA,kBAAe;gDAAC,WAAU;;;;;;0DAC3B,6LAAC;0DAAK;;;;;;;;;;;;kDAGR,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,GAAG,CAAC,CAAC,GAAG,sBACf,6LAAC;gDAEC,WAAW,CAAC,oCAAoC,EAC9C,oBAAoB,QAChB,iCACA,gCACJ;+CALG;;;;;;;;;;kDAUX,6LAAC;wCACC,SAAS,IAAM,mBAAmB,KAAK,GAAG,CAAC,QAAQ,MAAM,GAAG,GAAG,kBAAkB;wCACjF,UAAU,oBAAoB,QAAQ,MAAM,GAAG;wCAC/C,WAAU;;0DAEV,6LAAC;0DAAK;;;;;;0DACN,6LAAC,kOAAA,CAAA,mBAAgB;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAMlC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,GAAG,CAAC,CAAC;oCACZ,MAAM,cAAc,iBAAiB,OAAO,MAAM;oCAClD,qBACE,6LAAC;wCAEC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,OAAO,KAAK;;;;;;kEAEf,6LAAC;wDAAK,WAAU;kEACb,YAAY,MAAM;;;;;;;;;;;;0DAIvB,6LAAC;gDAAI,WAAU;;oDACZ,YAAY,GAAG,CAAC,CAAC,sBAChB,6LAAC,2IAAA,CAAA,WAAQ;4DAEP,MAAM;4DACN,gBAAgB;2DAFX,MAAM,EAAE;;;;;oDAKhB,YAAY,MAAM,KAAK,mBACtB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;oEAA2C,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EAClG,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAK,GAAE;;;;;;;;;;;;;;;;0EAG3E,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAc;;;;;;kFAC3B,6LAAC;wEAAE,WAAU;kFAAU;;;;;;;;;;;;;;;;;;;;;;;;;uCA7B1B,OAAO,EAAE;;;;;gCAoCpB;;;;;;0CAIF,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACZ,QAAQ,GAAG,CAAC,CAAC;wCACZ,MAAM,cAAc,iBAAiB,OAAO,MAAM;wCAClD,qBACE,6LAAC;4CAEC,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,OAAO,KAAK;;;;;;sEAEf,6LAAC;4DAAK,WAAU;sEACb,YAAY,MAAM;;;;;;;;;;;;8DAIvB,6LAAC;oDAAI,WAAU;;wDACZ,YAAY,GAAG,CAAC,CAAC,sBAChB,6LAAC,2IAAA,CAAA,WAAQ;gEAEP,MAAM;gEACN,gBAAgB;+DAFX,MAAM,EAAE;;;;;wDAKhB,YAAY,MAAM,KAAK,mBACtB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;wEAA2C,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFAClG,cAAA,6LAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAK,GAAE;;;;;;;;;;;;;;;;8EAG3E,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;sFAAc;;;;;;sFAC3B,6LAAC;4EAAE,WAAU;sFAAU;;;;;;;;;;;;;;;;;;;;;;;;;2CA7B1B,OAAO,EAAE;;;;;oCAoCpB;;;;;;;;;;;;;;;;;kCAON,6LAAC;wBAAI,WAAU;kCACZ,CAAC;4BACA,MAAM,eAAe,OAAO,CAAC,gBAAgB;4BAC7C,MAAM,cAAc,iBAAiB,aAAa,MAAM;4BAExD,qBACE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,aAAa,KAAK;;;;;;0DAErB,6LAAC;gDAAK,WAAU;0DACb,YAAY,MAAM;;;;;;;;;;;;kDAIvB,6LAAC;wCAAI,WAAU;;4CACZ,YAAY,GAAG,CAAC,CAAC,sBAChB,6LAAC,2IAAA,CAAA,WAAQ;oDAEP,MAAM;oDACN,gBAAgB;mDAFX,MAAM,EAAE;;;;;4CAKhB,YAAY,MAAM,KAAK,mBACtB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAA2C,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAClG,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAK,GAAE;;;;;;;;;;;;;;;;kEAG3E,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,6LAAC;gEAAE,WAAU;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAOrC,CAAC;;;;;;;;;;;;0BAKL,6LAAC,gJAAA,CAAA,eAAY;gBACX,QAAQ;gBACR,SAAS,IAAM,oBAAoB;gBACnC,aAAa;;;;;;;;;;;;AAIrB;GAlTgB;KAAA", "debugId": null}}, {"offset": {"line": 1664, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/cases/CaseFiltersPanel.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { XMarkIcon } from \"@heroicons/react/24/outline\";\r\nimport { CaseFilters } from \"../../_lib/types\";\r\n\r\ninterface CaseFiltersPanelProps {\r\n  filters: CaseFilters;\r\n  onFiltersChange: (filters: CaseFilters) => void;\r\n  onClose: () => void;\r\n}\r\n\r\nconst caseTypes = [\r\n  { value: \"all\", label: \"Todos los tipos\" },\r\n  { value: \"Laboral\", label: \"Laboral\" },\r\n  { value: \"Civil\", label: \"Civil\" },\r\n  { value: \"Familia\", label: \"Familia\" },\r\n  { value: \"Penal\", label: \"Penal\" },\r\n  { value: \"Comercial\", label: \"Comercial\" },\r\n];\r\n\r\nconst locations = [\r\n  { value: \"all\", label: \"Todas las ubicaciones\" },\r\n  { value: \"CABA\", label: \"CABA\" },\r\n  { value: \"Buenos Aires\", label: \"Buenos Aires\" },\r\n  { value: \"Córdoba\", label: \"Córdoba\" },\r\n  { value: \"Santa Fe\", label: \"Santa Fe\" },\r\n  { value: \"Mendoza\", label: \"Mendoza\" },\r\n  { value: \"Tucum<PERSON>\", label: \"Tucumán\" },\r\n  { value: \"Salta\", label: \"Salta\" },\r\n];\r\n\r\nconst budgetRanges = [\r\n  { value: \"all\", label: \"Todos los presupuestos\" },\r\n  { value: \"0-100000\", label: \"Hasta ARS 100,000\" },\r\n  { value: \"100000-250000\", label: \"ARS 100,000 - 250,000\" },\r\n  { value: \"250000-500000\", label: \"ARS 250,000 - 500,000\" },\r\n  { value: \"500000-999999999\", label: \"Más de ARS 500,000\" },\r\n];\r\n\r\nconst urgencyLevels = [\r\n  { value: \"all\", label: \"Todas las urgencias\" },\r\n  { value: \"urgent\", label: \"Urgente\" },\r\n  { value: \"high\", label: \"Alta\" },\r\n  { value: \"medium\", label: \"Media\" },\r\n  { value: \"low\", label: \"Baja\" },\r\n];\r\n\r\nconst sortOptions = [\r\n  { value: \"newest\", label: \"Más recientes\" },\r\n  { value: \"budget_high\", label: \"Mayor presupuesto\" },\r\n  { value: \"budget_low\", label: \"Menor presupuesto\" },\r\n  { value: \"bids_count\", label: \"Menos propuestas\" },\r\n];\r\n\r\nexport function CaseFiltersPanel({\r\n  filters,\r\n  onFiltersChange,\r\n  onClose,\r\n}: CaseFiltersPanelProps) {\r\n  const handleFilterChange = (key: keyof CaseFilters, value: string) => {\r\n    onFiltersChange({\r\n      ...filters,\r\n      [key]: value,\r\n    });\r\n  };\r\n\r\n  const clearFilters = () => {\r\n    onFiltersChange({\r\n      type: \"all\",\r\n      location: \"all\",\r\n      budgetRange: \"all\",\r\n      urgencyLevel: \"all\",\r\n      sortBy: \"newest\",\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6\">\r\n      <div className=\"flex items-center justify-between mb-6\">\r\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n          Filtros de Búsqueda\r\n        </h3>\r\n        <button\r\n          onClick={onClose}\r\n          className=\"text-gray-400 cursor-pointer dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\"\r\n        >\r\n          <XMarkIcon className=\"h-5 w-5\" />\r\n        </button>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\">\r\n        {/* Case Type */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Tipo de Caso\r\n          </label>\r\n          <select\r\n            value={filters.type}\r\n            onChange={(e) => handleFilterChange(\"type\", e.target.value)}\r\n            className=\"w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer\"\r\n          >\r\n            {caseTypes.map((type) => (\r\n              <option key={type.value} value={type.value}>\r\n                {type.label}\r\n              </option>\r\n            ))}\r\n          </select>\r\n        </div>\r\n\r\n        {/* Location */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Ubicación\r\n          </label>\r\n          <select\r\n            value={filters.location}\r\n            onChange={(e) => handleFilterChange(\"location\", e.target.value)}\r\n            className=\"w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer\"\r\n          >\r\n            {locations.map((location) => (\r\n              <option key={location.value} value={location.value}>\r\n                {location.label}\r\n              </option>\r\n            ))}\r\n          </select>\r\n        </div>\r\n\r\n        {/* Budget Range */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Presupuesto\r\n          </label>\r\n          <select\r\n            value={filters.budgetRange}\r\n            onChange={(e) => handleFilterChange(\"budgetRange\", e.target.value)}\r\n            className=\"w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer\"\r\n          >\r\n            {budgetRanges.map((range) => (\r\n              <option key={range.value} value={range.value}>\r\n                {range.label}\r\n              </option>\r\n            ))}\r\n          </select>\r\n        </div>\r\n\r\n        {/* Urgency Level */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Urgencia\r\n          </label>\r\n          <select\r\n            value={filters.urgencyLevel}\r\n            onChange={(e) => handleFilterChange(\"urgencyLevel\", e.target.value)}\r\n            className=\"w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer\"\r\n          >\r\n            {urgencyLevels.map((level) => (\r\n              <option key={level.value} value={level.value}>\r\n                {level.label}\r\n              </option>\r\n            ))}\r\n          </select>\r\n        </div>\r\n\r\n        {/* Sort By */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Ordenar por\r\n          </label>\r\n          <select\r\n            value={filters.sortBy}\r\n            onChange={(e) => handleFilterChange(\"sortBy\", e.target.value)}\r\n            className=\"w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer\"\r\n          >\r\n            {sortOptions.map((option) => (\r\n              <option key={option.value} value={option.value}>\r\n                {option.label}\r\n              </option>\r\n            ))}\r\n          </select>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex justify-between mt-6\">\r\n        <button\r\n          onClick={clearFilters}\r\n          className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer\"\r\n        >\r\n          Limpiar Filtros\r\n        </button>\r\n        <button\r\n          onClick={onClose}\r\n          className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer\"\r\n        >\r\n          Aplicar Filtros\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAWA,MAAM,YAAY;IAChB;QAAE,OAAO;QAAO,OAAO;IAAkB;IACzC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C;AAED,MAAM,YAAY;IAChB;QAAE,OAAO;QAAO,OAAO;IAAwB;IAC/C;QAAE,OAAO;QAAQ,OAAO;IAAO;IAC/B;QAAE,OAAO;QAAgB,OAAO;IAAe;IAC/C;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAS,OAAO;IAAQ;CAClC;AAED,MAAM,eAAe;IACnB;QAAE,OAAO;QAAO,OAAO;IAAyB;IAChD;QAAE,OAAO;QAAY,OAAO;IAAoB;IAChD;QAAE,OAAO;QAAiB,OAAO;IAAwB;IACzD;QAAE,OAAO;QAAiB,OAAO;IAAwB;IACzD;QAAE,OAAO;QAAoB,OAAO;IAAqB;CAC1D;AAED,MAAM,gBAAgB;IACpB;QAAE,OAAO;QAAO,OAAO;IAAsB;IAC7C;QAAE,OAAO;QAAU,OAAO;IAAU;IACpC;QAAE,OAAO;QAAQ,OAAO;IAAO;IAC/B;QAAE,OAAO;QAAU,OAAO;IAAQ;IAClC;QAAE,OAAO;QAAO,OAAO;IAAO;CAC/B;AAED,MAAM,cAAc;IAClB;QAAE,OAAO;QAAU,OAAO;IAAgB;IAC1C;QAAE,OAAO;QAAe,OAAO;IAAoB;IACnD;QAAE,OAAO;QAAc,OAAO;IAAoB;IAClD;QAAE,OAAO;QAAc,OAAO;IAAmB;CAClD;AAEM,SAAS,iBAAiB,EAC/B,OAAO,EACP,eAAe,EACf,OAAO,EACe;IACtB,MAAM,qBAAqB,CAAC,KAAwB;QAClD,gBAAgB;YACd,GAAG,OAAO;YACV,CAAC,IAAI,EAAE;QACT;IACF;IAEA,MAAM,eAAe;QACnB,gBAAgB;YACd,MAAM;YACN,UAAU;YACV,aAAa;YACb,cAAc;YACd,QAAQ;QACV;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyD;;;;;;kCAGvE,6LAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAIzB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,6LAAC;gCACC,OAAO,QAAQ,IAAI;gCACnB,UAAU,CAAC,IAAM,mBAAmB,QAAQ,EAAE,MAAM,CAAC,KAAK;gCAC1D,WAAU;0CAET,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC;wCAAwB,OAAO,KAAK,KAAK;kDACvC,KAAK,KAAK;uCADA,KAAK,KAAK;;;;;;;;;;;;;;;;kCAQ7B,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,6LAAC;gCACC,OAAO,QAAQ,QAAQ;gCACvB,UAAU,CAAC,IAAM,mBAAmB,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC9D,WAAU;0CAET,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC;wCAA4B,OAAO,SAAS,KAAK;kDAC/C,SAAS,KAAK;uCADJ,SAAS,KAAK;;;;;;;;;;;;;;;;kCAQjC,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,6LAAC;gCACC,OAAO,QAAQ,WAAW;gCAC1B,UAAU,CAAC,IAAM,mBAAmB,eAAe,EAAE,MAAM,CAAC,KAAK;gCACjE,WAAU;0CAET,aAAa,GAAG,CAAC,CAAC,sBACjB,6LAAC;wCAAyB,OAAO,MAAM,KAAK;kDACzC,MAAM,KAAK;uCADD,MAAM,KAAK;;;;;;;;;;;;;;;;kCAQ9B,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,6LAAC;gCACC,OAAO,QAAQ,YAAY;gCAC3B,UAAU,CAAC,IAAM,mBAAmB,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAClE,WAAU;0CAET,cAAc,GAAG,CAAC,CAAC,sBAClB,6LAAC;wCAAyB,OAAO,MAAM,KAAK;kDACzC,MAAM,KAAK;uCADD,MAAM,KAAK;;;;;;;;;;;;;;;;kCAQ9B,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,6LAAC;gCACC,OAAO,QAAQ,MAAM;gCACrB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;gCAC5D,WAAU;0CAET,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC;wCAA0B,OAAO,OAAO,KAAK;kDAC3C,OAAO,KAAK;uCADF,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;0BAQjC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAGD,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT;KAhJgB", "debugId": null}}, {"offset": {"line": 2065, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/modals/AvailableCaseDetailModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as Dialog from \"@radix-ui/react-dialog\";\nimport {\n  XMarkIcon,\n  MapPinIcon,\n  ClockIcon,\n  UserGroupIcon,\n  ExclamationTriangleIcon,\n  CheckCircleIcon,\n  DocumentTextIcon,\n  CalendarIcon,\n  UserIcon,\n} from \"@heroicons/react/24/outline\";\nimport { formatDistanceToNow } from \"date-fns\";\nimport { es } from \"date-fns/locale\";\nimport { AvailableCase } from \"../../_lib/types\";\nimport { useIsMounted } from \"../../_lib/useIsomorphicDate\";\n\ninterface AvailableCaseDetailModalProps {\n  case: AvailableCase;\n  onClose: () => void;\n  onSendProposal: (caseData: AvailableCase) => void;\n}\n\nexport function AvailableCaseDetailModal({\n  case: caseData,\n  onClose,\n  onSendProposal,\n}: AvailableCaseDetailModalProps) {\n  const isMounted = useIsMounted();\n\n  const getUrgencyIcon = (urgency: string) => {\n    switch (urgency) {\n      case \"urgent\":\n        return ExclamationTriangleIcon;\n      case \"high\":\n        return ExclamationTriangleIcon;\n      case \"medium\":\n        return ClockIcon;\n      case \"low\":\n        return CheckCircleIcon;\n      default:\n        return ClockIcon;\n    }\n  };\n\n  const getUrgencyColor = (urgency: string) => {\n    switch (urgency) {\n      case \"urgent\":\n        return \"text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20\";\n      case \"high\":\n        return \"text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20\";\n      case \"medium\":\n        return \"text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20\";\n      case \"low\":\n        return \"text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20\";\n      default:\n        return \"text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-900/20\";\n    }\n  };\n\n  const getUrgencyText = (urgency: string) => {\n    switch (urgency) {\n      case \"urgent\":\n        return \"Urgente\";\n      case \"high\":\n        return \"Alta\";\n      case \"medium\":\n        return \"Media\";\n      case \"low\":\n        return \"Baja\";\n      default:\n        return \"Media\";\n    }\n  };\n\n  const UrgencyIcon = getUrgencyIcon(caseData.urgencyLevel);\n  const userBids = JSON.parse(localStorage.getItem(\"userBids\") || \"[]\");\n  const hasBid = userBids.some(\n    (bid: { caseId: string }) => bid.caseId === caseData.id\n  );\n\n  return (\n    <Dialog.Root open onOpenChange={onClose}>\n      <Dialog.Portal>\n        <Dialog.Overlay className=\"fixed inset-0 bg-black/50 z-50\" />\n        <Dialog.Content className=\"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-xl z-50 w-full max-w-4xl p-6 max-h-[90vh] overflow-y-auto\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <Dialog.Title className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\n              Detalles del Caso\n            </Dialog.Title>\n            <Dialog.Close asChild>\n              <button\n                onClick={onClose}\n                className=\"text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 cursor-pointer\"\n              >\n                <XMarkIcon className=\"h-6 w-6\" />\n              </button>\n            </Dialog.Close>\n          </div>\n\n          <div className=\"space-y-6\">\n            {/* Header del caso */}\n            <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-6\">\n              <div className=\"flex items-start justify-between mb-4\">\n                <div className=\"flex-1\">\n                  <h2 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2\">\n                    {caseData.title}\n                  </h2>\n                  <div className=\"flex items-center space-x-4 mb-4\">\n                    <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200\">\n                      {caseData.type}\n                    </span>\n                    <div\n                      className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getUrgencyColor(\n                        caseData.urgencyLevel\n                      )}`}\n                    >\n                      <UrgencyIcon className=\"h-4 w-4 mr-1\" />\n                      Urgencia {getUrgencyText(caseData.urgencyLevel)}\n                    </div>\n                  </div>\n                </div>\n                <div className=\"text-right\">\n                  <div className=\"text-2xl font-bold text-green-600 dark:text-green-400 mb-1\">\n                    {caseData.budgetRange}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-300\">\n                    Presupuesto estimado\n                  </div>\n                </div>\n              </div>\n\n              {/* Información básica */}\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n                <div className=\"flex items-center text-gray-600 dark:text-gray-300\">\n                  <MapPinIcon className=\"h-4 w-4 mr-2\" />\n                  {caseData.clientLocation}\n                </div>\n                <div className=\"flex items-center text-gray-600 dark:text-gray-300\">\n                  <UserGroupIcon className=\"h-4 w-4 mr-2\" />\n                  {caseData.bidsCount} propuestas\n                </div>\n                <div className=\"flex items-center text-gray-600 dark:text-gray-300\">\n                  <CalendarIcon className=\"h-4 w-4 mr-2\" />\n                  {isMounted\n                    ? formatDistanceToNow(new Date(caseData.postedAt), {\n                        addSuffix: true,\n                        locale: es,\n                      })\n                    : \"Hace poco\"}\n                </div>\n              </div>\n            </div>\n\n            {/* Descripción */}\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center\">\n                <DocumentTextIcon className=\"h-5 w-5 mr-2\" />\n                Descripción del Caso\n              </h3>\n              <p className=\"text-gray-700 dark:text-gray-300 leading-relaxed\">\n                {caseData.description}\n              </p>\n            </div>\n\n            {/* Cliente */}\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center\">\n                <UserIcon className=\"h-5 w-5 mr-2\" />\n                Información del Cliente\n              </h3>\n              <div className=\"bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 p-4\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">\n                      Cliente:\n                    </span>\n                    <p className=\"text-gray-900 dark:text-gray-100\">\n                      {caseData.clientName}\n                    </p>\n                  </div>\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">\n                      Ubicación:\n                    </span>\n                    <p className=\"text-gray-900 dark:text-gray-100\">\n                      {caseData.clientLocation}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Detalles específicos */}\n            {caseData.details && (\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3\">\n                  Detalles Específicos\n                </h3>\n                <div className=\"bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 p-4\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    {Object.entries(caseData.details).map(([key, value]) => (\n                      <div key={key}>\n                        <span className=\"text-sm font-medium text-gray-500 dark:text-gray-400 capitalize\">\n                          {key.replace(/([A-Z])/g, \" $1\").toLowerCase()}:\n                        </span>\n                        <p className=\"text-gray-900 dark:text-gray-100\">\n                          {typeof value === \"boolean\" \n                            ? (value ? \"Sí\" : \"No\") \n                            : typeof value === \"object\" \n                            ? JSON.stringify(value)\n                            : String(value)}\n                        </p>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Acciones */}\n            <div className=\"flex space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700\">\n              <button\n                onClick={onClose}\n                className=\"flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors cursor-pointer\"\n              >\n                Cerrar\n              </button>\n              <button\n                onClick={() => onSendProposal(caseData)}\n                disabled={hasBid}\n                className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${\n                  hasBid\n                    ? \"bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed\"\n                    : \"bg-blue-600 dark:bg-blue-700 text-white hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 cursor-pointer\"\n                }`}\n              >\n                {hasBid ? \"Propuesta Enviada\" : \"Enviar Propuesta\"}\n              </button>\n            </div>\n          </div>\n        </Dialog.Content>\n      </Dialog.Portal>\n    </Dialog.Root>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AAEA;;;AAjBA;;;;;;AAyBO,SAAS,yBAAyB,EACvC,MAAM,QAAQ,EACd,OAAO,EACP,cAAc,EACgB;;IAC9B,MAAM,YAAY,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE7B,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO,gPAAA,CAAA,0BAAuB;YAChC,KAAK;gBACH,OAAO,gPAAA,CAAA,0BAAuB;YAChC,KAAK;gBACH,OAAO,oNAAA,CAAA,YAAS;YAClB,KAAK;gBACH,OAAO,gOAAA,CAAA,kBAAe;YACxB;gBACE,OAAO,oNAAA,CAAA,YAAS;QACpB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,eAAe,SAAS,YAAY;IACxD,MAAM,WAAW,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,eAAe;IAChE,MAAM,SAAS,SAAS,IAAI,CAC1B,CAAC,MAA4B,IAAI,MAAM,KAAK,SAAS,EAAE;IAGzD,qBACE,6LAAC,qKAAA,CAAA,OAAW;QAAC,IAAI;QAAC,cAAc;kBAC9B,cAAA,6LAAC,qKAAA,CAAA,SAAa;;8BACZ,6LAAC,qKAAA,CAAA,UAAc;oBAAC,WAAU;;;;;;8BAC1B,6LAAC,qKAAA,CAAA,UAAc;oBAAC,WAAU;;sCACxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qKAAA,CAAA,QAAY;oCAAC,WAAU;8CAAyD;;;;;;8CAGjF,6LAAC,qKAAA,CAAA,QAAY;oCAAC,OAAO;8CACnB,cAAA,6LAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAK3B,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,SAAS,KAAK;;;;;;sEAEjB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EACb,SAAS,IAAI;;;;;;8EAEhB,6LAAC;oEACC,WAAW,CAAC,oEAAoE,EAAE,gBAChF,SAAS,YAAY,GACpB;;sFAEH,6LAAC;4EAAY,WAAU;;;;;;wEAAiB;wEAC9B,eAAe,SAAS,YAAY;;;;;;;;;;;;;;;;;;;8DAIpD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,SAAS,WAAW;;;;;;sEAEvB,6LAAC;4DAAI,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;sDAO9D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,sNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDACrB,SAAS,cAAc;;;;;;;8DAE1B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,4NAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDACxB,SAAS,SAAS;wDAAC;;;;;;;8DAEtB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,0NAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;wDACvB,YACG,CAAA,GAAA,qJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,SAAS,QAAQ,GAAG;4DAC/C,WAAW;4DACX,QAAQ,8IAAA,CAAA,KAAE;wDACZ,KACA;;;;;;;;;;;;;;;;;;;8CAMV,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,kOAAA,CAAA,mBAAgB;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAG/C,6LAAC;4CAAE,WAAU;sDACV,SAAS,WAAW;;;;;;;;;;;;8CAKzB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,kNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAuD;;;;;;0EAGvE,6LAAC;gEAAE,WAAU;0EACV,SAAS,UAAU;;;;;;;;;;;;kEAGxB,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAuD;;;;;;0EAGvE,6LAAC;gEAAE,WAAU;0EACV,SAAS,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQjC,SAAS,OAAO,kBACf,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA8D;;;;;;sDAG5E,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACZ,OAAO,OAAO,CAAC,SAAS,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACjD,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;;oEACb,IAAI,OAAO,CAAC,YAAY,OAAO,WAAW;oEAAG;;;;;;;0EAEhD,6LAAC;gEAAE,WAAU;0EACV,OAAO,UAAU,YACb,QAAQ,OAAO,OAChB,OAAO,UAAU,WACjB,KAAK,SAAS,CAAC,SACf,OAAO;;;;;;;uDATL;;;;;;;;;;;;;;;;;;;;;8CAmBpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,UAAU;4CACV,WAAW,CAAC,kEAAkE,EAC5E,SACI,qFACA,2MACJ;sDAED,SAAS,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD;GA9NgB;;QAKI,mIAAA,CAAA,eAAY;;;KALhB", "debugId": null}}, {"offset": {"line": 2602, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/cases/AvailableCases.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  MagnifyingGlassIcon,\r\n  FunnelIcon,\r\n  ClockIcon,\r\n  MapPinIcon,\r\n  CurrencyDollarIcon,\r\n  UserGroupIcon,\r\n  ExclamationTriangleIcon,\r\n  CheckCircleIcon,\r\n} from \"@heroicons/react/24/outline\";\r\nimport { formatDistanceToNow } from \"date-fns\";\r\nimport { es } from \"date-fns/locale\";\r\nimport { AvailableCase, CaseFilters } from \"../../_lib/types\";\r\nimport { CaseFiltersPanel } from \"./CaseFiltersPanel\";\r\nimport { AvailableCaseDetailModal } from \"../modals/AvailableCaseDetailModal\";\r\nimport { useIsMounted } from \"../../_lib/useIsomorphicDate\";\r\n\r\nexport function AvailableCases() {\r\n  const router = useRouter();\r\n  const [availableCases, setAvailableCases] = useState<AvailableCase[]>([]);\r\n  const [filteredCases, setFilteredCases] = useState<AvailableCase[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [showFilters, setShowFilters] = useState(false);\r\n  const [selectedCase, setSelectedCase] = useState<AvailableCase | null>(null);\r\n  const [showDetailModal, setShowDetailModal] = useState(false);\r\n  const [filters, setFilters] = useState<CaseFilters>({\r\n    type: \"all\",\r\n    location: \"all\",\r\n    budgetRange: \"all\",\r\n    urgencyLevel: \"all\",\r\n    sortBy: \"newest\",\r\n  });\r\n  const isMounted = useIsMounted();\r\n\r\n  // Load available cases\r\n  useEffect(() => {\r\n    const loadCases = async () => {\r\n      try {\r\n        const response = await fetch(\"/data/available-cases.json\");\r\n        const casesData = await response.json();\r\n        setAvailableCases(casesData);\r\n        setFilteredCases(casesData);\r\n      } catch (error) {\r\n        console.error(\"Error loading available cases:\", error);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadCases();\r\n  }, []);\r\n\r\n  // Apply filters and search\r\n  useEffect(() => {\r\n    let filtered = [...availableCases];\r\n\r\n    // Search filter\r\n    if (searchTerm) {\r\n      filtered = filtered.filter(\r\n        (case_) =>\r\n          case_.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n          case_.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n          case_.type.toLowerCase().includes(searchTerm.toLowerCase())\r\n      );\r\n    }\r\n\r\n    // Type filter\r\n    if (filters.type !== \"all\") {\r\n      filtered = filtered.filter((case_) => case_.type === filters.type);\r\n    }\r\n\r\n    // Location filter\r\n    if (filters.location !== \"all\") {\r\n      filtered = filtered.filter((case_) =>\r\n        case_.clientLocation\r\n          .toLowerCase()\r\n          .includes(filters.location.toLowerCase())\r\n      );\r\n    }\r\n\r\n    // Budget range filter\r\n    if (filters.budgetRange !== \"all\") {\r\n      const [min, max] = filters.budgetRange.split(\"-\").map(Number);\r\n      filtered = filtered.filter(\r\n        (case_) => case_.estimatedValue >= min && case_.estimatedValue <= max\r\n      );\r\n    }\r\n\r\n    // Urgency filter\r\n    if (filters.urgencyLevel !== \"all\") {\r\n      filtered = filtered.filter(\r\n        (case_) => case_.urgencyLevel === filters.urgencyLevel\r\n      );\r\n    }\r\n\r\n    // Sorting\r\n    switch (filters.sortBy) {\r\n      case \"newest\":\r\n        filtered.sort(\r\n          (a, b) =>\r\n            new Date(b.postedAt).getTime() - new Date(a.postedAt).getTime()\r\n        );\r\n        break;\r\n      case \"budget_high\":\r\n        filtered.sort((a, b) => b.estimatedValue - a.estimatedValue);\r\n        break;\r\n      case \"budget_low\":\r\n        filtered.sort((a, b) => a.estimatedValue - b.estimatedValue);\r\n        break;\r\n      case \"bids_count\":\r\n        filtered.sort((a, b) => a.bidsCount - b.bidsCount);\r\n        break;\r\n    }\r\n\r\n    setFilteredCases(filtered);\r\n  }, [availableCases, searchTerm, filters]);\r\n\r\n  const handleSendProposal = (caseId: string) => {\r\n    router.push(`/dashboard/bid/${caseId}`);\r\n  };\r\n\r\n  const getUrgencyColor = (urgency: string) => {\r\n    switch (urgency) {\r\n      case \"urgent\":\r\n        return \"text-red-600 bg-red-100\";\r\n      case \"high\":\r\n        return \"text-orange-600 bg-orange-100\";\r\n      case \"medium\":\r\n        return \"text-yellow-600 bg-yellow-100\";\r\n      case \"low\":\r\n        return \"text-green-600 bg-green-100\";\r\n      default:\r\n        return \"text-gray-600 bg-gray-100\";\r\n    }\r\n  };\r\n\r\n  const getUrgencyIcon = (urgency: string) => {\r\n    switch (urgency) {\r\n      case \"urgent\":\r\n        return ExclamationTriangleIcon;\r\n      case \"high\":\r\n        return ClockIcon;\r\n      case \"medium\":\r\n        return ClockIcon;\r\n      case \"low\":\r\n        return CheckCircleIcon;\r\n      default:\r\n        return ClockIcon;\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex justify-center items-center h-64\">\r\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div>\r\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\r\n            Casos Disponibles\r\n          </h2>\r\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\r\n            {filteredCases.length} casos disponibles para aplicar\r\n          </p>\r\n        </div>\r\n\r\n        {/* Quick stats */}\r\n        <div className=\"flex items-center space-x-4 text-sm\">\r\n          <div className=\"flex items-center space-x-1 text-red-600 dark:text-red-400\">\r\n            <ExclamationTriangleIcon className=\"h-4 w-4\" />\r\n            <span>\r\n              {availableCases.filter((c) => c.urgencyLevel === \"urgent\").length}{\" \"}\r\n              urgentes\r\n            </span>\r\n          </div>\r\n          <div className=\"flex items-center space-x-1 text-blue-600 dark:text-blue-400\">\r\n            <CurrencyDollarIcon className=\"h-4 w-4\" />\r\n            <span>\r\n              ARS{\" \"}\r\n              {Math.round(\r\n                availableCases.reduce((sum, c) => sum + c.estimatedValue, 0) /\r\n                  1000\r\n              )}\r\n              K total\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Search and Filters */}\r\n      <div className=\"flex flex-col lg:flex-row gap-4\">\r\n        <div className=\"flex-1\">\r\n          <div className=\"relative\">\r\n            <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500\" />\r\n            <input\r\n              type=\"text\"\r\n              value={searchTerm}\r\n              onChange={(e) => setSearchTerm(e.target.value)}\r\n              placeholder=\"Buscar casos por título, descripción o tipo...\"\r\n              className=\"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n            />\r\n          </div>\r\n        </div>\r\n        <button\r\n          onClick={() => setShowFilters(!showFilters)}\r\n          className=\"inline-flex items-center cursor-pointer px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900 transition-colors\"\r\n        >\r\n          <FunnelIcon className=\"h-4 w-4 mr-2\" />\r\n          Filtros\r\n        </button>\r\n      </div>\r\n\r\n      {/* Filters Panel */}\r\n      {showFilters && (\r\n        <CaseFiltersPanel\r\n          filters={filters}\r\n          onFiltersChange={setFilters}\r\n          onClose={() => setShowFilters(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Cases Grid */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n        {filteredCases.map((case_, index) => {\r\n          const UrgencyIcon = getUrgencyIcon(case_.urgencyLevel);\r\n          const userBids = JSON.parse(localStorage.getItem(\"userBids\") || \"[]\");\r\n          const hasBid = userBids.some(\r\n            (bid: { caseId: string }) => bid.caseId === case_.id\r\n          );\r\n\r\n          return (\r\n            <motion.div\r\n              key={case_.id}\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: index * 0.1 }}\r\n              className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow\"\r\n            >\r\n              {/* Header */}\r\n              <div className=\"flex items-start justify-between mb-4\">\r\n                <div className=\"flex-1\">\r\n                  <h3 className=\"font-semibold text-gray-900 dark:text-gray-100 mb-2\">\r\n                    {case_.title}\r\n                  </h3>\r\n                  <div className=\"flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-300\">\r\n                    <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200\">\r\n                      {case_.type}\r\n                    </span>\r\n                    <div className=\"flex items-center space-x-1\">\r\n                      <MapPinIcon className=\"h-3 w-3\" />\r\n                      <span>{case_.clientLocation}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getUrgencyColor(\r\n                    case_.urgencyLevel\r\n                  )}`}\r\n                >\r\n                  <UrgencyIcon className=\"h-3 w-3 mr-1\" />\r\n                  {case_.urgencyLevel === \"urgent\"\r\n                    ? \"Urgente\"\r\n                    : case_.urgencyLevel === \"high\"\r\n                    ? \"Alta\"\r\n                    : case_.urgencyLevel === \"medium\"\r\n                    ? \"Media\"\r\n                    : \"Baja\"}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Description */}\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-300 mb-4 line-clamp-3\">\r\n                {case_.description}\r\n              </p>\r\n\r\n              {/* Details */}\r\n              <div className=\"grid grid-cols-2 gap-4 mb-4 text-sm\">\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <CurrencyDollarIcon className=\"h-4 w-4 text-gray-400 dark:text-gray-500\" />\r\n                  <span className=\"text-gray-600 dark:text-gray-300\">\r\n                    {case_.budgetRange}\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <UserGroupIcon className=\"h-4 w-4 text-gray-400 dark:text-gray-500\" />\r\n                  <span className=\"text-gray-600 dark:text-gray-300\">\r\n                    {case_.bidsCount} propuestas\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <ClockIcon className=\"h-4 w-4 text-gray-400 dark:text-gray-500\" />\r\n                  <span className=\"text-gray-600 dark:text-gray-300\">\r\n                    {isMounted\r\n                      ? formatDistanceToNow(new Date(case_.postedAt), {\r\n                          addSuffix: true,\r\n                          locale: es,\r\n                        })\r\n                      : \"Hace poco\"}\r\n                  </span>\r\n                </div>\r\n                <div className=\"text-gray-600 dark:text-gray-300\">\r\n                  Cliente: {case_.clientName}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Actions */}\r\n              <div className=\"flex space-x-3\">\r\n                <button\r\n                  onClick={() => handleSendProposal(case_.id)}\r\n                  disabled={hasBid || case_.status !== \"open\"}\r\n                  className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${\r\n                    hasBid || case_.status !== \"open\"\r\n                      ? \"bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed\"\r\n                      : \"bg-blue-600 dark:bg-blue-700 text-white hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 cursor-pointer\"\r\n                  }`}\r\n                >\r\n                  {hasBid ? \"Propuesta Enviada\" : \"Enviar Propuesta\"}\r\n                </button>\r\n                <button\r\n                  onClick={() => {\r\n                    setSelectedCase(case_);\r\n                    setShowDetailModal(true);\r\n                  }}\r\n                  className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer\">\r\n                  Ver Detalles\r\n                </button>\r\n              </div>\r\n            </motion.div>\r\n          );\r\n        })}\r\n      </div>\r\n\r\n      {/* Empty State */}\r\n      {filteredCases.length === 0 && (\r\n        <div className=\"text-center py-12\">\r\n          <MagnifyingGlassIcon className=\"h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4\" />\r\n          <p className=\"text-gray-500 dark:text-gray-400 mb-2\">\r\n            No se encontraron casos que coincidan con los filtros\r\n          </p>\r\n          <button\r\n            onClick={() => {\r\n              setSearchTerm(\"\");\r\n              setFilters({\r\n                type: \"all\",\r\n                location: \"all\",\r\n                budgetRange: \"all\",\r\n                urgencyLevel: \"all\",\r\n                sortBy: \"newest\",\r\n              });\r\n            }}\r\n            className=\"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm font-medium\"\r\n          >\r\n            Limpiar filtros\r\n          </button>\r\n        </div>\r\n      )}\r\n\r\n      {/* Detail Modal */}\r\n      {showDetailModal && selectedCase && (\r\n        <AvailableCaseDetailModal\r\n          case={selectedCase}\r\n          onClose={() => {\r\n            setShowDetailModal(false);\r\n            setSelectedCase(null);\r\n          }}\r\n          onSendProposal={(caseData) => {\r\n            setShowDetailModal(false);\r\n            setSelectedCase(null);\r\n            handleSendProposal(caseData.id);\r\n          }}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AAEA;AACA;AACA;;;AApBA;;;;;;;;;;AAsBO,SAAS;;IACd,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACvE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAClD,MAAM;QACN,UAAU;QACV,aAAa;QACb,cAAc;QACd,QAAQ;IACV;IACA,MAAM,YAAY,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE7B,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;sDAAY;oBAChB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM;wBAC7B,MAAM,YAAY,MAAM,SAAS,IAAI;wBACrC,kBAAkB;wBAClB,iBAAiB;oBACnB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,kCAAkC;oBAClD,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;mCAAG,EAAE;IAEL,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,WAAW;mBAAI;aAAe;YAElC,gBAAgB;YAChB,IAAI,YAAY;gBACd,WAAW,SAAS,MAAM;gDACxB,CAAC,QACC,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC/D,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;;YAE9D;YAEA,cAAc;YACd,IAAI,QAAQ,IAAI,KAAK,OAAO;gBAC1B,WAAW,SAAS,MAAM;gDAAC,CAAC,QAAU,MAAM,IAAI,KAAK,QAAQ,IAAI;;YACnE;YAEA,kBAAkB;YAClB,IAAI,QAAQ,QAAQ,KAAK,OAAO;gBAC9B,WAAW,SAAS,MAAM;gDAAC,CAAC,QAC1B,MAAM,cAAc,CACjB,WAAW,GACX,QAAQ,CAAC,QAAQ,QAAQ,CAAC,WAAW;;YAE5C;YAEA,sBAAsB;YACtB,IAAI,QAAQ,WAAW,KAAK,OAAO;gBACjC,MAAM,CAAC,KAAK,IAAI,GAAG,QAAQ,WAAW,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;gBACtD,WAAW,SAAS,MAAM;gDACxB,CAAC,QAAU,MAAM,cAAc,IAAI,OAAO,MAAM,cAAc,IAAI;;YAEtE;YAEA,iBAAiB;YACjB,IAAI,QAAQ,YAAY,KAAK,OAAO;gBAClC,WAAW,SAAS,MAAM;gDACxB,CAAC,QAAU,MAAM,YAAY,KAAK,QAAQ,YAAY;;YAE1D;YAEA,UAAU;YACV,OAAQ,QAAQ,MAAM;gBACpB,KAAK;oBACH,SAAS,IAAI;oDACX,CAAC,GAAG,IACF,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO;;oBAEjE;gBACF,KAAK;oBACH,SAAS,IAAI;oDAAC,CAAC,GAAG,IAAM,EAAE,cAAc,GAAG,EAAE,cAAc;;oBAC3D;gBACF,KAAK;oBACH,SAAS,IAAI;oDAAC,CAAC,GAAG,IAAM,EAAE,cAAc,GAAG,EAAE,cAAc;;oBAC3D;gBACF,KAAK;oBACH,SAAS,IAAI;oDAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;;oBACjD;YACJ;YAEA,iBAAiB;QACnB;mCAAG;QAAC;QAAgB;QAAY;KAAQ;IAExC,MAAM,qBAAqB,CAAC;QAC1B,OAAO,IAAI,CAAC,CAAC,eAAe,EAAE,QAAQ;IACxC;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO,gPAAA,CAAA,0BAAuB;YAChC,KAAK;gBACH,OAAO,oNAAA,CAAA,YAAS;YAClB,KAAK;gBACH,OAAO,oNAAA,CAAA,YAAS;YAClB,KAAK;gBACH,OAAO,gOAAA,CAAA,kBAAe;YACxB;gBACE,OAAO,oNAAA,CAAA,YAAS;QACpB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAGvE,6LAAC;gCAAE,WAAU;;oCACV,cAAc,MAAM;oCAAC;;;;;;;;;;;;;kCAK1B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gPAAA,CAAA,0BAAuB;wCAAC,WAAU;;;;;;kDACnC,6LAAC;;4CACE,eAAe,MAAM,CAAC,CAAC,IAAM,EAAE,YAAY,KAAK,UAAU,MAAM;4CAAE;4CAAI;;;;;;;;;;;;;0CAI3E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,sOAAA,CAAA,qBAAkB;wCAAC,WAAU;;;;;;kDAC9B,6LAAC;;4CAAK;4CACA;4CACH,KAAK,KAAK,CACT,eAAe,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,cAAc,EAAE,KACxD;4CACF;;;;;;;;;;;;;;;;;;;;;;;;;0BAQV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,wOAAA,CAAA,sBAAmB;oCAAC,WAAU;;;;;;8CAC/B,6LAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,aAAY;oCACZ,WAAU;;;;;;;;;;;;;;;;;kCAIhB,6LAAC;wBACC,SAAS,IAAM,eAAe,CAAC;wBAC/B,WAAU;;0CAEV,6LAAC,sNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;YAM1C,6BACC,6LAAC,mJAAA,CAAA,mBAAgB;gBACf,SAAS;gBACT,iBAAiB;gBACjB,SAAS,IAAM,eAAe;;;;;;0BAKlC,6LAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,OAAO;oBACzB,MAAM,cAAc,eAAe,MAAM,YAAY;oBACrD,MAAM,WAAW,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,eAAe;oBAChE,MAAM,SAAS,SAAS,IAAI,CAC1B,CAAC,MAA4B,IAAI,MAAM,KAAK,MAAM,EAAE;oBAGtD,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO,QAAQ;wBAAI;wBAChD,WAAU;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,MAAM,KAAK;;;;;;0DAEd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEACb,MAAM,IAAI;;;;;;kEAEb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,sNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,6LAAC;0EAAM,MAAM,cAAc;;;;;;;;;;;;;;;;;;;;;;;;kDAIjC,6LAAC;wCACC,WAAW,CAAC,oEAAoE,EAAE,gBAChF,MAAM,YAAY,GACjB;;0DAEH,6LAAC;gDAAY,WAAU;;;;;;4CACtB,MAAM,YAAY,KAAK,WACpB,YACA,MAAM,YAAY,KAAK,SACvB,SACA,MAAM,YAAY,KAAK,WACvB,UACA;;;;;;;;;;;;;0CAKR,6LAAC;gCAAE,WAAU;0CACV,MAAM,WAAW;;;;;;0CAIpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,sOAAA,CAAA,qBAAkB;gDAAC,WAAU;;;;;;0DAC9B,6LAAC;gDAAK,WAAU;0DACb,MAAM,WAAW;;;;;;;;;;;;kDAGtB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,4NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,6LAAC;gDAAK,WAAU;;oDACb,MAAM,SAAS;oDAAC;;;;;;;;;;;;;kDAGrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;gDAAK,WAAU;0DACb,YACG,CAAA,GAAA,qJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,MAAM,QAAQ,GAAG;oDAC5C,WAAW;oDACX,QAAQ,8IAAA,CAAA,KAAE;gDACZ,KACA;;;;;;;;;;;;kDAGR,6LAAC;wCAAI,WAAU;;4CAAmC;4CACtC,MAAM,UAAU;;;;;;;;;;;;;0CAK9B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,mBAAmB,MAAM,EAAE;wCAC1C,UAAU,UAAU,MAAM,MAAM,KAAK;wCACrC,WAAW,CAAC,kEAAkE,EAC5E,UAAU,MAAM,MAAM,KAAK,SACvB,qFACA,2MACJ;kDAED,SAAS,sBAAsB;;;;;;kDAElC,6LAAC;wCACC,SAAS;4CACP,gBAAgB;4CAChB,mBAAmB;wCACrB;wCACA,WAAU;kDAAmU;;;;;;;;;;;;;uBA3F5U,MAAM,EAAE;;;;;gBAiGnB;;;;;;YAID,cAAc,MAAM,KAAK,mBACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,wOAAA,CAAA,sBAAmB;wBAAC,WAAU;;;;;;kCAC/B,6LAAC;wBAAE,WAAU;kCAAwC;;;;;;kCAGrD,6LAAC;wBACC,SAAS;4BACP,cAAc;4BACd,WAAW;gCACT,MAAM;gCACN,UAAU;gCACV,aAAa;gCACb,cAAc;gCACd,QAAQ;4BACV;wBACF;wBACA,WAAU;kCACX;;;;;;;;;;;;YAOJ,mBAAmB,8BAClB,6LAAC,4JAAA,CAAA,2BAAwB;gBACvB,MAAM;gBACN,SAAS;oBACP,mBAAmB;oBACnB,gBAAgB;gBAClB;gBACA,gBAAgB,CAAC;oBACf,mBAAmB;oBACnB,gBAAgB;oBAChB,mBAAmB,SAAS,EAAE;gBAChC;;;;;;;;;;;;AAKV;GA5WgB;;QACC,qIAAA,CAAA,YAAS;QAeN,mIAAA,CAAA,eAAY;;;KAhBhB", "debugId": null}}, {"offset": {"line": 3295, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/modals/PurchaseModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  XMarkIcon,\r\n  CreditCardIcon,\r\n  CheckCircleIcon,\r\n  CurrencyDollarIcon,\r\n} from \"@heroicons/react/24/outline\";\r\nimport * as Dialog from \"@radix-ui/react-dialog\";\r\nimport { Template } from \"../../_lib/types\";\r\n\r\ninterface PurchaseModalProps {\r\n  template: Template;\r\n  onClose: () => void;\r\n  onPurchaseComplete: () => void;\r\n}\r\n\r\nexport function PurchaseModal({\r\n  template,\r\n  onClose,\r\n  onPurchaseComplete,\r\n}: PurchaseModalProps) {\r\n  const [step, setStep] = useState<\"details\" | \"payment\" | \"success\">(\r\n    \"details\"\r\n  );\r\n  const [isProcessing, setIsProcessing] = useState(false);\r\n  const [paymentMethod, setPaymentMethod] = useState(\"credit_card\");\r\n\r\n  const handlePurchase = async () => {\r\n    setIsProcessing(true);\r\n\r\n    // Para plantillas gratuitas, proceso más rápido\r\n    const processingTime = template.price === 0 ? 1000 : 2000;\r\n\r\n    // Simulate payment processing\r\n    setTimeout(() => {\r\n      setIsProcessing(false);\r\n      setStep(\"success\");\r\n      // Removed auto-close - user must manually close\r\n    }, processingTime);\r\n  };\r\n\r\n  const renderStepContent = () => {\r\n    switch (step) {\r\n      case \"details\":\r\n        return (\r\n          <div className=\"space-y-6\">\r\n            <div className=\"text-center\">\r\n              <div className=\"text-6xl mb-4\">📄</div>\r\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2\">\r\n                {template.title}\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\r\n                {template.description}\r\n              </p>\r\n              {template.price === 0 ? (\r\n                <div className=\"flex items-center justify-center space-x-2 text-2xl font-bold text-green-600 dark:text-green-400\">\r\n                  <CheckCircleIcon className=\"h-6 w-6\" />\r\n                  <span>Gratis</span>\r\n                </div>\r\n              ) : (\r\n                <div className=\"flex items-center justify-center space-x-2 text-2xl font-bold text-blue-600 dark:text-blue-400\">\r\n                  <CurrencyDollarIcon className=\"h-6 w-6\" />\r\n                  <span>ARS {template.price.toLocaleString(\"es-AR\")}</span>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\r\n              <h4 className=\"font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                Lo que incluye esta plantilla:\r\n              </h4>\r\n              <ul className=\"text-sm text-gray-600 dark:text-gray-300 space-y-1\">\r\n                <li>• Documento en formato Word editable</li>\r\n                <li>• Cláusulas actualizadas según legislación vigente</li>\r\n                <li>• Guía de uso y personalización</li>\r\n                {template.price > 0 && (\r\n                  <>\r\n                    <li>• Soporte técnico por 30 días</li>\r\n                    <li>• Actualizaciones gratuitas por 1 año</li>\r\n                  </>\r\n                )}\r\n                {template.price === 0 && (\r\n                  <li>• Descarga inmediata sin registro adicional</li>\r\n                )}\r\n              </ul>\r\n            </div>\r\n\r\n            <div className=\"flex space-x-3\">\r\n              <button\r\n                onClick={onClose}\r\n                className=\"flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer\"\r\n              >\r\n                Cancelar\r\n              </button>\r\n              <button\r\n                onClick={() => template.price === 0 ? handlePurchase() : setStep(\"payment\")}\r\n                className={`flex-1 px-4 py-2 text-sm font-medium text-white rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer ${\r\n                  template.price === 0\r\n                    ? \"bg-green-600 hover:bg-green-700 focus:ring-green-500\"\r\n                    : \"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500\"\r\n                }`}\r\n              >\r\n                {template.price === 0 ? \"Obtener Gratis\" : \"Continuar\"}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        );\r\n\r\n      case \"payment\":\r\n        return (\r\n          <div className=\"space-y-6\">\r\n            <div className=\"text-center\">\r\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2\">\r\n                Método de Pago\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                Total a pagar:{\" \"}\r\n                <span className=\"font-semibold\">\r\n                  ARS {template.price.toLocaleString(\"es-AR\")}\r\n                </span>\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"space-y-4\">\r\n              <div className=\"space-y-3\">\r\n                <label className=\"flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                  <input\r\n                    type=\"radio\"\r\n                    name=\"payment\"\r\n                    value=\"credit_card\"\r\n                    checked={paymentMethod === \"credit_card\"}\r\n                    onChange={(e) => setPaymentMethod(e.target.value)}\r\n                    className=\"text-blue-600 focus:ring-blue-500 cursor-pointer\"\r\n                  />\r\n                  <CreditCardIcon className=\"h-5 w-5 text-gray-400 dark:text-gray-500\" />\r\n                  <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                    Tarjeta de Crédito/Débito\r\n                  </span>\r\n                </label>\r\n\r\n                <label className=\"flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                  <input\r\n                    type=\"radio\"\r\n                    name=\"payment\"\r\n                    value=\"mercadopago\"\r\n                    checked={paymentMethod === \"mercadopago\"}\r\n                    onChange={(e) => setPaymentMethod(e.target.value)}\r\n                    className=\"text-blue-600 focus:ring-blue-500 cursor-pointer\"\r\n                  />\r\n                  <div className=\"w-5 h-5 bg-blue-500 rounded\"></div>\r\n                  <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                    MercadoPago\r\n                  </span>\r\n                </label>\r\n\r\n                <label className=\"flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                  <input\r\n                    type=\"radio\"\r\n                    name=\"payment\"\r\n                    value=\"transfer\"\r\n                    checked={paymentMethod === \"transfer\"}\r\n                    onChange={(e) => setPaymentMethod(e.target.value)}\r\n                    className=\"text-blue-600 focus:ring-blue-500 cursor-pointer\"\r\n                  />\r\n                  <div className=\"w-5 h-5 bg-green-500 rounded\"></div>\r\n                  <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                    Transferencia Bancaria\r\n                  </span>\r\n                </label>\r\n              </div>\r\n\r\n              {paymentMethod === \"credit_card\" && (\r\n                <div className=\"space-y-3 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\r\n                  <input\r\n                    type=\"text\"\r\n                    placeholder=\"Número de tarjeta\"\r\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                  />\r\n                  <div className=\"grid grid-cols-2 gap-3\">\r\n                    <input\r\n                      type=\"text\"\r\n                      placeholder=\"MM/AA\"\r\n                      className=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                    />\r\n                    <input\r\n                      type=\"text\"\r\n                      placeholder=\"CVV\"\r\n                      className=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                    />\r\n                  </div>\r\n                  <input\r\n                    type=\"text\"\r\n                    placeholder=\"Nombre del titular\"\r\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                  />\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"flex space-x-3\">\r\n              <button\r\n                onClick={() => setStep(\"details\")}\r\n                className=\"flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer\"\r\n              >\r\n                Volver\r\n              </button>\r\n              <button\r\n                onClick={handlePurchase}\r\n                disabled={isProcessing}\r\n                className=\"flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors cursor-pointer\"\r\n              >\r\n                {isProcessing ? (\r\n                  <div className=\"flex items-center justify-center\">\r\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n                    Procesando...\r\n                  </div>\r\n                ) : (\r\n                  \"Confirmar Compra\"\r\n                )}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        );\r\n\r\n      case \"success\":\r\n        return (\r\n          <motion.div\r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ duration: 0.6 }}\r\n            className=\"text-center space-y-6\"\r\n          >\r\n            <div className=\"text-green-600 dark:text-green-400\">\r\n              <CheckCircleIcon className=\"h-16 w-16 mx-auto mb-4\" />\r\n            </div>\r\n            <div>\r\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2\">\r\n                {template.price === 0 ? \"¡Plantilla Obtenida!\" : \"¡Compra Exitosa!\"}\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                La plantilla &quot;{template.title}&quot; ha sido agregada a tu\r\n                biblioteca. Puedes descargarla desde la sección &quot;Mis\r\n                Plantillas&quot;.\r\n              </p>\r\n            </div>\r\n            <div className=\"bg-green-50 dark:bg-green-900/20 rounded-lg p-4\">\r\n              <p className=\"text-sm text-green-800 dark:text-green-200\">\r\n                {template.price === 0\r\n                  ? \"La plantilla está lista para descargar desde tu biblioteca.\"\r\n                  : \"Se ha enviado el recibo de compra a tu email registrado.\"\r\n                }\r\n              </p>\r\n            </div>\r\n            <button\r\n              onClick={() => {\r\n                onPurchaseComplete();\r\n                onClose();\r\n              }}\r\n              className=\"w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer\"\r\n            >\r\n              Continuar\r\n            </button>\r\n          </motion.div>\r\n        );\r\n\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog.Root open onOpenChange={onClose}>\r\n      <Dialog.Portal>\r\n        <Dialog.Overlay className=\"fixed inset-0 bg-black/50 dark:bg-black/70 z-50\" />\r\n        <Dialog.Content className=\"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-xl z-50 w-full max-w-md p-6\">\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <Dialog.Title className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n              {step === \"details\" && \"Detalles de la Plantilla\"}\r\n              {step === \"payment\" && \"Método de Pago\"}\r\n              {step === \"success\" && (template.price === 0 ? \"Plantilla Obtenida\" : \"Compra Completada\")}\r\n            </Dialog.Title>\r\n            <Dialog.Close asChild>\r\n              <button \r\n                onClick={() => {\r\n                  if (step === \"success\") {\r\n                    onPurchaseComplete();\r\n                  }\r\n                  onClose();\r\n                }}\r\n                className=\"text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 cursor-pointer\"\r\n                aria-label=\"Cerrar modal\"\r\n              >\r\n                <XMarkIcon className=\"h-5 w-5\" />\r\n              </button>\r\n            </Dialog.Close>\r\n          </div>\r\n\r\n          <Dialog.Description className=\"sr-only\">\r\n            {step === \"details\" &&\r\n              `Detalles de compra para la plantilla ${template.title}`}\r\n            {step === \"payment\" &&\r\n              \"Formulario de pago para completar la compra\"}\r\n            {step === \"success\" && \"Confirmación de compra exitosa\"}\r\n          </Dialog.Description>\r\n\r\n          {renderStepContent()}\r\n        </Dialog.Content>\r\n      </Dialog.Portal>\r\n    </Dialog.Root>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAMA;;;AAVA;;;;;AAmBO,SAAS,cAAc,EAC5B,QAAQ,EACR,OAAO,EACP,kBAAkB,EACC;;IACnB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC7B;IAEF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,iBAAiB;QACrB,gBAAgB;QAEhB,gDAAgD;QAChD,MAAM,iBAAiB,SAAS,KAAK,KAAK,IAAI,OAAO;QAErD,8BAA8B;QAC9B,WAAW;YACT,gBAAgB;YAChB,QAAQ;QACR,gDAAgD;QAClD,GAAG;IACL;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;oCAAG,WAAU;8CACX,SAAS,KAAK;;;;;;8CAEjB,6LAAC;oCAAE,WAAU;8CACV,SAAS,WAAW;;;;;;gCAEtB,SAAS,KAAK,KAAK,kBAClB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gOAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;sDAC3B,6LAAC;sDAAK;;;;;;;;;;;yDAGR,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,sOAAA,CAAA,qBAAkB;4CAAC,WAAU;;;;;;sDAC9B,6LAAC;;gDAAK;gDAAK,SAAS,KAAK,CAAC,cAAc,CAAC;;;;;;;;;;;;;;;;;;;sCAK/C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;wCACH,SAAS,KAAK,GAAG,mBAChB;;8DACE,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;wCAGP,SAAS,KAAK,KAAK,mBAClB,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAKV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,SAAS,KAAK,KAAK,IAAI,mBAAmB,QAAQ;oCACjE,WAAW,CAAC,gLAAgL,EAC1L,SAAS,KAAK,KAAK,IACf,yDACA,qDACJ;8CAED,SAAS,KAAK,KAAK,IAAI,mBAAmB;;;;;;;;;;;;;;;;;;YAMrD,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA8D;;;;;;8CAG5E,6LAAC;oCAAE,WAAU;;wCAA2C;wCACvC;sDACf,6LAAC;4CAAK,WAAU;;gDAAgB;gDACzB,SAAS,KAAK,CAAC,cAAc,CAAC;;;;;;;;;;;;;;;;;;;sCAKzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAM;oDACN,SAAS,kBAAkB;oDAC3B,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAChD,WAAU;;;;;;8DAEZ,6LAAC,8NAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;8DAC1B,6LAAC;oDAAK,WAAU;8DAAuD;;;;;;;;;;;;sDAKzE,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAM;oDACN,SAAS,kBAAkB;oDAC3B,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAChD,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAAuD;;;;;;;;;;;;sDAKzE,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAM;oDACN,SAAS,kBAAkB;oDAC3B,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAChD,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAAuD;;;;;;;;;;;;;;;;;;gCAM1E,kBAAkB,+BACjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;sDAEZ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,WAAU;;;;;;8DAEZ,6LAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,WAAU;;;;;;;;;;;;sDAGd,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;;sCAMlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,QAAQ;oCACvB,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;4CAAuE;;;;;;+CAIxF;;;;;;;;;;;;;;;;;;YAOZ,KAAK;gBACH,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gOAAA,CAAA,kBAAe;gCAAC,WAAU;;;;;;;;;;;sCAE7B,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CACX,SAAS,KAAK,KAAK,IAAI,yBAAyB;;;;;;8CAEnD,6LAAC;oCAAE,WAAU;;wCAA2C;wCAClC,SAAS,KAAK;wCAAC;;;;;;;;;;;;;sCAKvC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CACV,SAAS,KAAK,KAAK,IAChB,gEACA;;;;;;;;;;;sCAIR,6LAAC;4BACC,SAAS;gCACP;gCACA;4BACF;4BACA,WAAU;sCACX;;;;;;;;;;;;YAMP;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,qKAAA,CAAA,OAAW;QAAC,IAAI;QAAC,cAAc;kBAC9B,cAAA,6LAAC,qKAAA,CAAA,SAAa;;8BACZ,6LAAC,qKAAA,CAAA,UAAc;oBAAC,WAAU;;;;;;8BAC1B,6LAAC,qKAAA,CAAA,UAAc;oBAAC,WAAU;;sCACxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qKAAA,CAAA,QAAY;oCAAC,WAAU;;wCACrB,SAAS,aAAa;wCACtB,SAAS,aAAa;wCACtB,SAAS,aAAa,CAAC,SAAS,KAAK,KAAK,IAAI,uBAAuB,mBAAmB;;;;;;;8CAE3F,6LAAC,qKAAA,CAAA,QAAY;oCAAC,OAAO;8CACnB,cAAA,6LAAC;wCACC,SAAS;4CACP,IAAI,SAAS,WAAW;gDACtB;4CACF;4CACA;wCACF;wCACA,WAAU;wCACV,cAAW;kDAEX,cAAA,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAK3B,6LAAC,qKAAA,CAAA,cAAkB;4BAAC,WAAU;;gCAC3B,SAAS,aACR,CAAC,qCAAqC,EAAE,SAAS,KAAK,EAAE;gCACzD,SAAS,aACR;gCACD,SAAS,aAAa;;;;;;;wBAGxB;;;;;;;;;;;;;;;;;;AAKX;GAtSgB;KAAA", "debugId": null}}, {"offset": {"line": 3996, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/templates/TemplateCard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  StarIcon,\r\n  ArrowDownTrayIcon,\r\n  CurrencyDollarIcon,\r\n  EyeIcon,\r\n} from \"@heroicons/react/24/outline\";\r\nimport { StarIcon as StarSolidIcon } from \"@heroicons/react/24/solid\";\r\nimport { Template, UserTemplate } from \"../../_lib/types\";\r\nimport { PurchaseModal } from \"../modals/PurchaseModal\";\r\n\r\ninterface TemplateCardProps {\r\n  template: Template;\r\n  featured?: boolean;\r\n  onPurchase?: (template: Template) => void;\r\n}\r\n\r\nconst legalAreaColors = {\r\n  Laboral: \"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200\",\r\n  Civil:\r\n    \"bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200\",\r\n  Familia: \"bg-pink-100 dark:bg-pink-900 text-pink-800 dark:text-pink-200\",\r\n  Penal: \"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200\",\r\n  Comercial:\r\n    \"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200\",\r\n};\r\n\r\nexport function TemplateCard({\r\n  template,\r\n  featured = false,\r\n  onPurchase,\r\n}: TemplateCardProps) {\r\n  const [showPurchaseModal, setShowPurchaseModal] = useState(false);\r\n  const [isDownloading, setIsDownloading] = useState(false);\r\n\r\n  const handleDownload = async () => {\r\n    // Call onPurchase callback if provided\r\n    if (onPurchase) {\r\n      onPurchase(template);\r\n    }\r\n\r\n    // Siempre abrir el modal, tanto para plantillas gratuitas como pagadas\r\n    setShowPurchaseModal(true);\r\n  };\r\n\r\n  const handlePurchaseComplete = () => {\r\n    setShowPurchaseModal(false);\r\n    // Add to user's library after purchase/download\r\n    const userTemplates = JSON.parse(\r\n      localStorage.getItem(\"userTemplates\") || \"[]\"\r\n    );\r\n    const newUserTemplate = {\r\n      id: `user-${crypto.randomUUID()}`,\r\n      templateId: template.id,\r\n      purchasedAt: new Date().toISOString(),\r\n      downloadCount: 1,\r\n    };\r\n\r\n    if (\r\n      !userTemplates.find((ut: UserTemplate) => ut.templateId === template.id)\r\n    ) {\r\n      userTemplates.push(newUserTemplate);\r\n      localStorage.setItem(\"userTemplates\", JSON.stringify(userTemplates));\r\n    }\r\n\r\n    // Para plantillas gratuitas, simular descarga\r\n    if (template.price === 0) {\r\n      console.log(\"Downloading free template:\", template.title);\r\n    }\r\n  };\r\n\r\n  const renderStars = (rating: number) => {\r\n    const stars = [];\r\n    const fullStars = Math.floor(rating);\r\n    const hasHalfStar = rating % 1 !== 0;\r\n\r\n    for (let i = 0; i < fullStars; i++) {\r\n      stars.push(\r\n        <StarSolidIcon key={i} className=\"h-4 w-4 text-yellow-400\" role=\"img\" />\r\n      );\r\n    }\r\n\r\n    if (hasHalfStar) {\r\n      stars.push(\r\n        <div key=\"half\" className=\"relative\" role=\"img\">\r\n          <StarIcon className=\"h-4 w-4 text-yellow-400\" />\r\n          <StarSolidIcon\r\n            className=\"h-4 w-4 text-yellow-400 absolute top-0 left-0\"\r\n            style={{ clipPath: \"inset(0 50% 0 0)\" }}\r\n          />\r\n        </div>\r\n      );\r\n    }\r\n\r\n    const emptyStars = 5 - Math.ceil(rating);\r\n    for (let i = 0; i < emptyStars; i++) {\r\n      stars.push(\r\n        <StarIcon\r\n          key={`empty-${i}`}\r\n          className=\"h-4 w-4 text-gray-300 dark:text-gray-600\"\r\n          role=\"img\"\r\n        />\r\n      );\r\n    }\r\n\r\n    return stars;\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <motion.div\r\n        whileHover={{ y: -4 }}\r\n        transition={{ duration: 0.2 }}\r\n        className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow overflow-hidden ${\r\n          featured ? \"ring-2 ring-yellow-400\" : \"\"\r\n        }`}\r\n      >\r\n        {/* Template Preview */}\r\n        <div className=\"relative h-48 bg-gray-100 dark:bg-gray-700 flex items-center justify-center\">\r\n          <div className=\"text-6xl text-gray-300 dark:text-gray-500\">📄</div>\r\n          {featured && (\r\n            <div className=\"absolute top-2 left-2\">\r\n              <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200\">\r\n                <StarSolidIcon className=\"h-3 w-3 mr-1\" />\r\n                Destacado\r\n              </span>\r\n            </div>\r\n          )}\r\n          <div className=\"absolute top-2 right-2\">\r\n            <button\r\n              className=\"p-1 bg-white dark:bg-gray-800 rounded-full shadow-sm hover:shadow-md transition-shadow cursor-pointer\"\r\n              aria-label=\"Vista previa\"\r\n            >\r\n              <EyeIcon className=\"h-4 w-4 text-gray-600 dark:text-gray-400\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"p-4 space-y-3\">\r\n          {/* Header */}\r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex items-start justify-between\">\r\n              <h3 className=\"font-semibold text-gray-900 dark:text-gray-100 text-sm leading-tight line-clamp-2\">\r\n                {template.title}\r\n              </h3>\r\n              <div className=\"flex items-center space-x-1 ml-2\">\r\n                {template.price === 0 ? (\r\n                  <span className=\"text-green-600 dark:text-green-400 font-semibold text-sm\">\r\n                    Gratis\r\n                  </span>\r\n                ) : (\r\n                  <div className=\"flex items-center text-blue-600 dark:text-blue-400\">\r\n                    <CurrencyDollarIcon className=\"h-3 w-3\" />\r\n                    <span className=\"font-semibold text-sm\">\r\n                      {template.price.toLocaleString(\"es-AR\")}\r\n                    </span>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            <p className=\"text-xs text-gray-600 dark:text-gray-400 line-clamp-2\">\r\n              {template.description}\r\n            </p>\r\n          </div>\r\n\r\n          {/* Tags */}\r\n          <div className=\"flex items-center space-x-2\">\r\n            <span\r\n              className={`text-xs font-medium px-2 py-1 rounded-full ${\r\n                legalAreaColors[\r\n                  template.legalArea as keyof typeof legalAreaColors\r\n                ] ||\r\n                \"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200\"\r\n              }`}\r\n            >\r\n              {template.legalArea}\r\n            </span>\r\n            <span className=\"text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full\">\r\n              {template.documentType}\r\n            </span>\r\n          </div>\r\n\r\n          {/* Rating and Downloads */}\r\n          <div className=\"flex items-center justify-between text-xs\">\r\n            <div className=\"flex items-center space-x-1\">\r\n              <div className=\"flex items-center\">\r\n                {renderStars(template.rating)}\r\n              </div>\r\n              <span className=\"text-gray-600 dark:text-gray-400\">\r\n                ({template.rating})\r\n              </span>\r\n            </div>\r\n            <div className=\"flex items-center space-x-1 text-gray-500 dark:text-gray-400\">\r\n              <ArrowDownTrayIcon className=\"h-3 w-3\" />\r\n              <span>{template.downloadCount.toLocaleString(\"en-US\")}</span>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Author */}\r\n          <div className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n            Por {template.author}\r\n          </div>\r\n\r\n          {/* Action Button */}\r\n          <button\r\n            onClick={handleDownload}\r\n            disabled={isDownloading}\r\n            className={`w-full py-2 px-3 text-sm font-medium rounded-md transition-colors cursor-pointer ${\r\n              template.price === 0\r\n                ? \"bg-green-600 text-white hover:bg-green-700 focus:ring-green-500\"\r\n                : \"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500\"\r\n            } focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed`}\r\n          >\r\n            {isDownloading ? (\r\n              <div className=\"flex items-center justify-center\">\r\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n                Descargando...\r\n              </div>\r\n            ) : template.price === 0 ? (\r\n              <div className=\"flex items-center justify-center\">\r\n                <ArrowDownTrayIcon className=\"h-4 w-4 mr-2\" />\r\n                Obtener Gratis\r\n              </div>\r\n            ) : (\r\n              <div className=\"flex items-center justify-center\">\r\n                <CurrencyDollarIcon className=\"h-4 w-4 mr-2\" />\r\n                Comprar ARS {template.price.toLocaleString(\"es-AR\")}\r\n              </div>\r\n            )}\r\n          </button>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* Purchase Modal */}\r\n      {showPurchaseModal && (\r\n        <PurchaseModal\r\n          template={template}\r\n          onClose={() => setShowPurchaseModal(false)}\r\n          onPurchaseComplete={handlePurchaseComplete}\r\n        />\r\n      )}\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAMA;AAEA;;;AAZA;;;;;;AAoBA,MAAM,kBAAkB;IACtB,SAAS;IACT,OACE;IACF,SAAS;IACT,OAAO;IACP,WACE;AACJ;AAEO,SAAS,aAAa,EAC3B,QAAQ,EACR,WAAW,KAAK,EAChB,UAAU,EACQ;;IAClB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,iBAAiB;QACrB,uCAAuC;QACvC,IAAI,YAAY;YACd,WAAW;QACb;QAEA,uEAAuE;QACvE,qBAAqB;IACvB;IAEA,MAAM,yBAAyB;QAC7B,qBAAqB;QACrB,gDAAgD;QAChD,MAAM,gBAAgB,KAAK,KAAK,CAC9B,aAAa,OAAO,CAAC,oBAAoB;QAE3C,MAAM,kBAAkB;YACtB,IAAI,CAAC,KAAK,EAAE,OAAO,UAAU,IAAI;YACjC,YAAY,SAAS,EAAE;YACvB,aAAa,IAAI,OAAO,WAAW;YACnC,eAAe;QACjB;QAEA,IACE,CAAC,cAAc,IAAI,CAAC,CAAC,KAAqB,GAAG,UAAU,KAAK,SAAS,EAAE,GACvE;YACA,cAAc,IAAI,CAAC;YACnB,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;QACvD;QAEA,8CAA8C;QAC9C,IAAI,SAAS,KAAK,KAAK,GAAG;YACxB,QAAQ,GAAG,CAAC,8BAA8B,SAAS,KAAK;QAC1D;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAQ,EAAE;QAChB,MAAM,YAAY,KAAK,KAAK,CAAC;QAC7B,MAAM,cAAc,SAAS,MAAM;QAEnC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;YAClC,MAAM,IAAI,eACR,6LAAC,gNAAA,CAAA,WAAa;gBAAS,WAAU;gBAA0B,MAAK;eAA5C;;;;;QAExB;QAEA,IAAI,aAAa;YACf,MAAM,IAAI,eACR,6LAAC;gBAAe,WAAU;gBAAW,MAAK;;kCACxC,6LAAC,kNAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC,gNAAA,CAAA,WAAa;wBACZ,WAAU;wBACV,OAAO;4BAAE,UAAU;wBAAmB;;;;;;;eAJjC;;;;;QAQb;QAEA,MAAM,aAAa,IAAI,KAAK,IAAI,CAAC;QACjC,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;YACnC,MAAM,IAAI,eACR,6LAAC,kNAAA,CAAA,WAAQ;gBAEP,WAAU;gBACV,MAAK;eAFA,CAAC,MAAM,EAAE,GAAG;;;;;QAKvB;QAEA,OAAO;IACT;IAEA,qBACE;;0BACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,YAAY;oBAAE,GAAG,CAAC;gBAAE;gBACpB,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAW,CAAC,6IAA6I,EACvJ,WAAW,2BAA2B,IACtC;;kCAGF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAA4C;;;;;;4BAC1D,0BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;;sDACd,6LAAC,gNAAA,CAAA,WAAa;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAKhD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC,gNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKzB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,SAAS,KAAK;;;;;;0DAEjB,6LAAC;gDAAI,WAAU;0DACZ,SAAS,KAAK,KAAK,kBAClB,6LAAC;oDAAK,WAAU;8DAA2D;;;;;yEAI3E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,sOAAA,CAAA,qBAAkB;4DAAC,WAAU;;;;;;sEAC9B,6LAAC;4DAAK,WAAU;sEACb,SAAS,KAAK,CAAC,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;kDAOzC,6LAAC;wCAAE,WAAU;kDACV,SAAS,WAAW;;;;;;;;;;;;0CAKzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAW,CAAC,2CAA2C,EACrD,eAAe,CACb,SAAS,SAAS,CACnB,IACD,iEACA;kDAED,SAAS,SAAS;;;;;;kDAErB,6LAAC;wCAAK,WAAU;kDACb,SAAS,YAAY;;;;;;;;;;;;0CAK1B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,YAAY,SAAS,MAAM;;;;;;0DAE9B,6LAAC;gDAAK,WAAU;;oDAAmC;oDAC/C,SAAS,MAAM;oDAAC;;;;;;;;;;;;;kDAGtB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oOAAA,CAAA,oBAAiB;gDAAC,WAAU;;;;;;0DAC7B,6LAAC;0DAAM,SAAS,aAAa,CAAC,cAAc,CAAC;;;;;;;;;;;;;;;;;;0CAKjD,6LAAC;gCAAI,WAAU;;oCAA2C;oCACnD,SAAS,MAAM;;;;;;;0CAItB,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAW,CAAC,iFAAiF,EAC3F,SAAS,KAAK,KAAK,IACf,oEACA,+DACL,oGAAoG,CAAC;0CAErG,8BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;wCAAuE;;;;;;2CAGtF,SAAS,KAAK,KAAK,kBACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oOAAA,CAAA,oBAAiB;4CAAC,WAAU;;;;;;wCAAiB;;;;;;yDAIhD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,sOAAA,CAAA,qBAAkB;4CAAC,WAAU;;;;;;wCAAiB;wCAClC,SAAS,KAAK,CAAC,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;YAQpD,mCACC,6LAAC,iJAAA,CAAA,gBAAa;gBACZ,UAAU;gBACV,SAAS,IAAM,qBAAqB;gBACpC,oBAAoB;;;;;;;;AAK9B;GAzNgB;KAAA", "debugId": null}}, {"offset": {"line": 4452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/templates/TemplateFilters.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { FunnelIcon, XMarkIcon } from \"@heroicons/react/24/outline\";\r\nimport * as Dialog from \"@radix-ui/react-dialog\";\r\nimport { TemplateFilters as TemplateFiltersType } from \"../../_lib/types\";\r\n\r\ninterface TemplateFiltersProps {\r\n  filters: TemplateFiltersType;\r\n  onFiltersChange: (filters: TemplateFiltersType) => void;\r\n}\r\n\r\nconst legalAreas = [\r\n  { value: \"all\", label: \"Todas las áreas\" },\r\n  { value: \"Laboral\", label: \"Laboral\" },\r\n  { value: \"Civil\", label: \"Civil\" },\r\n  { value: \"Familia\", label: \"Familia\" },\r\n  { value: \"Penal\", label: \"Penal\" },\r\n  { value: \"Comercial\", label: \"Comercial\" },\r\n];\r\n\r\nconst documentTypes = [\r\n  { value: \"all\", label: \"Todos los tipos\" },\r\n  { value: \"Contratos\", label: \"Contratos\" },\r\n  { value: \"Demandas\", label: \"Demandas\" },\r\n  { value: \"Escritos\", label: \"Escritos\" },\r\n  { value: \"Cartas Documento\", label: \"Cartas Documento\" },\r\n];\r\n\r\nconst priceRanges = [\r\n  { value: \"all\", label: \"Todos los precios\" },\r\n  { value: \"free\", label: \"Gratis\" },\r\n  { value: \"paid\", label: \"De pago\" },\r\n];\r\n\r\nconst sortOptions = [\r\n  { value: \"popularity\", label: \"Más populares\" },\r\n  { value: \"rating\", label: \"Mejor valorados\" },\r\n  { value: \"newest\", label: \"Más recientes\" },\r\n  { value: \"price_low\", label: \"Precio: menor a mayor\" },\r\n  { value: \"price_high\", label: \"Precio: mayor a menor\" },\r\n];\r\n\r\nexport function TemplateFilters({\r\n  filters,\r\n  onFiltersChange,\r\n}: TemplateFiltersProps) {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n\r\n  const handleFilterChange = (\r\n    key: keyof TemplateFiltersType,\r\n    value: string\r\n  ) => {\r\n    onFiltersChange({\r\n      ...filters,\r\n      [key]: value,\r\n    });\r\n  };\r\n\r\n  const clearFilters = () => {\r\n    onFiltersChange({\r\n      legalArea: \"all\",\r\n      documentType: \"all\",\r\n      priceRange: \"all\",\r\n      sortBy: \"popularity\",\r\n      searchTerm: \"\",\r\n    });\r\n  };\r\n\r\n  const hasActiveFilters =\r\n    filters.legalArea !== \"all\" ||\r\n    filters.documentType !== \"all\" ||\r\n    filters.priceRange !== \"all\" ||\r\n    filters.sortBy !== \"popularity\";\r\n\r\n  return (\r\n    <Dialog.Root open={isOpen} onOpenChange={setIsOpen}>\r\n      <Dialog.Trigger asChild>\r\n        <button className=\"inline-flex cursor-pointer items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900 transition-colors\">\r\n          <FunnelIcon className=\"h-4 w-4 mr-2\" />\r\n          Filtros\r\n          {hasActiveFilters && (\r\n            <span className=\"ml-2 inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-white bg-blue-600 dark:bg-blue-700 rounded-full\">\r\n              !\r\n            </span>\r\n          )}\r\n        </button>\r\n      </Dialog.Trigger>\r\n\r\n      <Dialog.Portal>\r\n        <Dialog.Overlay className=\"fixed inset-0 bg-black/50 z-50\" />\r\n        <Dialog.Content className=\"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-xl z-50 w-full max-w-md p-6\">\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <Dialog.Title className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n              Filtros de Búsqueda\r\n            </Dialog.Title>\r\n            <Dialog.Close asChild>\r\n              <button className=\"text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-400 cursor-pointer\">\r\n                <XMarkIcon className=\"h-5 w-5\" />\r\n              </button>\r\n            </Dialog.Close>\r\n          </div>\r\n\r\n          <div className=\"space-y-6\">\r\n            {/* Legal Area */}\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                Área Legal\r\n              </label>\r\n              <select\r\n                value={filters.legalArea}\r\n                onChange={(e) =>\r\n                  handleFilterChange(\"legalArea\", e.target.value)\r\n                }\r\n                className=\"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer\"\r\n              >\r\n                {legalAreas.map((area) => (\r\n                  <option key={area.value} value={area.value}>\r\n                    {area.label}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n\r\n            {/* Document Type */}\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                Tipo de Documento\r\n              </label>\r\n              <select\r\n                value={filters.documentType}\r\n                onChange={(e) =>\r\n                  handleFilterChange(\"documentType\", e.target.value)\r\n                }\r\n                className=\"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer\"\r\n              >\r\n                {documentTypes.map((type) => (\r\n                  <option key={type.value} value={type.value}>\r\n                    {type.label}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n\r\n            {/* Price Range */}\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                Rango de Precio\r\n              </label>\r\n              <select\r\n                value={filters.priceRange}\r\n                onChange={(e) =>\r\n                  handleFilterChange(\"priceRange\", e.target.value)\r\n                }\r\n                className=\"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer\"\r\n              >\r\n                {priceRanges.map((range) => (\r\n                  <option key={range.value} value={range.value}>\r\n                    {range.label}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n\r\n            {/* Sort By */}\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                Ordenar por\r\n              </label>\r\n              <select\r\n                value={filters.sortBy}\r\n                onChange={(e) => handleFilterChange(\"sortBy\", e.target.value)}\r\n                className=\"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer\"\r\n              >\r\n                {sortOptions.map((option) => (\r\n                  <option key={option.value} value={option.value}>\r\n                    {option.label}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex justify-between mt-8\">\r\n            <button\r\n              onClick={clearFilters}\r\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer\"\r\n            >\r\n              Limpiar Filtros\r\n            </button>\r\n            <Dialog.Close asChild>\r\n              <button className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 dark:bg-blue-700 rounded-md hover:bg-blue-700 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer\">\r\n                Aplicar Filtros\r\n              </button>\r\n            </Dialog.Close>\r\n          </div>\r\n        </Dialog.Content>\r\n      </Dialog.Portal>\r\n    </Dialog.Root>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAYA,MAAM,aAAa;IACjB;QAAE,OAAO;QAAO,OAAO;IAAkB;IACzC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C;AAED,MAAM,gBAAgB;IACpB;QAAE,OAAO;QAAO,OAAO;IAAkB;IACzC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAoB,OAAO;IAAmB;CACxD;AAED,MAAM,cAAc;IAClB;QAAE,OAAO;QAAO,OAAO;IAAoB;IAC3C;QAAE,OAAO;QAAQ,OAAO;IAAS;IACjC;QAAE,OAAO;QAAQ,OAAO;IAAU;CACnC;AAED,MAAM,cAAc;IAClB;QAAE,OAAO;QAAc,OAAO;IAAgB;IAC9C;QAAE,OAAO;QAAU,OAAO;IAAkB;IAC5C;QAAE,OAAO;QAAU,OAAO;IAAgB;IAC1C;QAAE,OAAO;QAAa,OAAO;IAAwB;IACrD;QAAE,OAAO;QAAc,OAAO;IAAwB;CACvD;AAEM,SAAS,gBAAgB,EAC9B,OAAO,EACP,eAAe,EACM;;IACrB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,qBAAqB,CACzB,KACA;QAEA,gBAAgB;YACd,GAAG,OAAO;YACV,CAAC,IAAI,EAAE;QACT;IACF;IAEA,MAAM,eAAe;QACnB,gBAAgB;YACd,WAAW;YACX,cAAc;YACd,YAAY;YACZ,QAAQ;YACR,YAAY;QACd;IACF;IAEA,MAAM,mBACJ,QAAQ,SAAS,KAAK,SACtB,QAAQ,YAAY,KAAK,SACzB,QAAQ,UAAU,KAAK,SACvB,QAAQ,MAAM,KAAK;IAErB,qBACE,6LAAC,qKAAA,CAAA,OAAW;QAAC,MAAM;QAAQ,cAAc;;0BACvC,6LAAC,qKAAA,CAAA,UAAc;gBAAC,OAAO;0BACrB,cAAA,6LAAC;oBAAO,WAAU;;sCAChB,6LAAC,sNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;wBAAiB;wBAEtC,kCACC,6LAAC;4BAAK,WAAU;sCAAgI;;;;;;;;;;;;;;;;;0BAOtJ,6LAAC,qKAAA,CAAA,SAAa;;kCACZ,6LAAC,qKAAA,CAAA,UAAc;wBAAC,WAAU;;;;;;kCAC1B,6LAAC,qKAAA,CAAA,UAAc;wBAAC,WAAU;;0CACxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qKAAA,CAAA,QAAY;wCAAC,WAAU;kDAAyD;;;;;;kDAGjF,6LAAC,qKAAA,CAAA,QAAY;wCAAC,OAAO;kDACnB,cAAA,6LAAC;4CAAO,WAAU;sDAChB,cAAA,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAK3B,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,6LAAC;gDACC,OAAO,QAAQ,SAAS;gDACxB,UAAU,CAAC,IACT,mBAAmB,aAAa,EAAE,MAAM,CAAC,KAAK;gDAEhD,WAAU;0DAET,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;wDAAwB,OAAO,KAAK,KAAK;kEACvC,KAAK,KAAK;uDADA,KAAK,KAAK;;;;;;;;;;;;;;;;kDAQ7B,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,6LAAC;gDACC,OAAO,QAAQ,YAAY;gDAC3B,UAAU,CAAC,IACT,mBAAmB,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAEnD,WAAU;0DAET,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;wDAAwB,OAAO,KAAK,KAAK;kEACvC,KAAK,KAAK;uDADA,KAAK,KAAK;;;;;;;;;;;;;;;;kDAQ7B,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,6LAAC;gDACC,OAAO,QAAQ,UAAU;gDACzB,UAAU,CAAC,IACT,mBAAmB,cAAc,EAAE,MAAM,CAAC,KAAK;gDAEjD,WAAU;0DAET,YAAY,GAAG,CAAC,CAAC,sBAChB,6LAAC;wDAAyB,OAAO,MAAM,KAAK;kEACzC,MAAM,KAAK;uDADD,MAAM,KAAK;;;;;;;;;;;;;;;;kDAQ9B,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,6LAAC;gDACC,OAAO,QAAQ,MAAM;gDACrB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;gDAC5D,WAAU;0DAET,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC;wDAA0B,OAAO,OAAO,KAAK;kEAC3C,OAAO,KAAK;uDADF,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;0CAQjC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,6LAAC,qKAAA,CAAA,QAAY;wCAAC,OAAO;kDACnB,cAAA,6LAAC;4CAAO,WAAU;sDAAqQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrS;GA7JgB;KAAA", "debugId": null}}, {"offset": {"line": 4861, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/modals/UploadTemplateModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useCallback } from \"react\";\r\nimport { useDropzone } from \"react-dropzone\";\r\nimport {\r\n  XMarkIcon,\r\n  CloudArrowUpIcon,\r\n  DocumentIcon,\r\n  CheckCircleIcon,\r\n} from \"@heroicons/react/24/outline\";\r\nimport * as Dialog from \"@radix-ui/react-dialog\";\r\nimport { Template } from \"../../_lib/types\";\r\n\r\ninterface UploadTemplateModalProps {\r\n  onClose: () => void;\r\n  onUpload: (template: Template) => void;\r\n}\r\n\r\nconst legalAreas = [\"Laboral\", \"Civil\", \"Familia\", \"Penal\", \"Comercial\"];\r\nconst documentTypes = [\"Contratos\", \"Demandas\", \"Escritos\", \"Cartas Documento\"];\r\n\r\nexport function UploadTemplateModal({\r\n  onClose,\r\n  onUpload,\r\n}: UploadTemplateModalProps) {\r\n  const [step, setStep] = useState<\r\n    \"upload\" | \"details\" | \"preview\" | \"success\"\r\n  >(\"upload\");\r\n  const [uploadedFile, setUploadedFile] = useState<File | null>(null);\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [formData, setFormData] = useState({\r\n    title: \"\",\r\n    description: \"\",\r\n    legalArea: \"Laboral\",\r\n    documentType: \"Contratos\",\r\n    price: 0,\r\n    tags: \"\",\r\n    isFree: true,\r\n  });\r\n\r\n  const onDrop = useCallback((acceptedFiles: File[]) => {\r\n    if (acceptedFiles.length > 0) {\r\n      setUploadedFile(acceptedFiles[0]);\r\n      setStep(\"details\");\r\n    }\r\n  }, []);\r\n\r\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\r\n    onDrop,\r\n    accept: {\r\n      \"application/pdf\": [\".pdf\"],\r\n      \"application/msword\": [\".doc\"],\r\n      \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\":\r\n        [\".docx\"],\r\n    },\r\n    maxSize: 10 * 1024 * 1024, // 10MB\r\n    multiple: false,\r\n  });\r\n\r\n  const handleFormSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setStep(\"preview\");\r\n  };\r\n\r\n  const handlePublish = async () => {\r\n    setIsUploading(true);\r\n\r\n    // Simulate upload process\r\n    setTimeout(() => {\r\n      const newTemplate: Template = {\r\n        id: `tpl-${crypto.randomUUID()}`,\r\n        title: formData.title,\r\n        description: formData.description,\r\n        legalArea: formData.legalArea,\r\n        documentType: formData.documentType,\r\n        price: formData.isFree ? 0 : formData.price,\r\n        rating: 0,\r\n        downloadCount: 0,\r\n        author: \"Dr. Ana García\", // Mock current user\r\n        authorId: \"current-user\",\r\n        createdAt: new Date().toISOString(),\r\n        updatedAt: new Date().toISOString(),\r\n        tags: formData.tags\r\n          .split(\",\")\r\n          .map((tag) => tag.trim())\r\n          .filter(Boolean),\r\n        preview: \"/templates/previews/default.jpg\",\r\n        fileUrl: `/templates/files/${uploadedFile?.name}`,\r\n        featured: false,\r\n        status: \"under_review\",\r\n      };\r\n\r\n      onUpload(newTemplate);\r\n      setIsUploading(false);\r\n      setStep(\"success\");\r\n\r\n      // Auto-close after success\r\n      setTimeout(() => {\r\n        onClose();\r\n      }, 2000);\r\n    }, 2000);\r\n  };\r\n\r\n  const renderStepContent = () => {\r\n    switch (step) {\r\n      case \"upload\":\r\n        return (\r\n          <div className=\"space-y-6\">\r\n            <div\r\n              {...getRootProps()}\r\n              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${\r\n                isDragActive\r\n                  ? \"border-blue-400 bg-blue-50 dark:bg-blue-900/20\"\r\n                  : \"border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500\"\r\n              }`}\r\n            >\r\n              <input {...getInputProps()} />\r\n              <CloudArrowUpIcon className=\"h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-4\" />\r\n              {isDragActive ? (\r\n                <p className=\"text-sm text-blue-600 dark:text-blue-400\">\r\n                  Suelta el archivo aquí...\r\n                </p>\r\n              ) : (\r\n                <div className=\"space-y-2\">\r\n                  <p className=\"text-sm text-gray-600 dark:text-gray-300\">\r\n                    Arrastra tu plantilla aquí o haz clic para seleccionar\r\n                  </p>\r\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                    PDF, DOC, DOCX (máx. 10MB)\r\n                  </p>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4\">\r\n              <h4 className=\"font-medium text-blue-900 dark:text-blue-200 mb-2\">\r\n                Requisitos para publicar:\r\n              </h4>\r\n              <ul className=\"text-sm text-blue-800 dark:text-blue-300 space-y-1\">\r\n                <li>• El documento debe ser original o de tu autoría</li>\r\n                <li>• Debe cumplir con la legislación vigente</li>\r\n                <li>• Formato profesional y bien estructurado</li>\r\n                <li>• Sin errores ortográficos o gramaticales</li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        );\r\n\r\n      case \"details\":\r\n        return (\r\n          <form onSubmit={handleFormSubmit} className=\"space-y-4\">\r\n            <div className=\"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\r\n              <DocumentIcon className=\"h-5 w-5 text-gray-400 dark:text-gray-500\" />\r\n              <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                {uploadedFile?.name}\r\n              </span>\r\n            </div>\r\n\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                Título de la plantilla *\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                required\r\n                value={formData.title}\r\n                onChange={(e) =>\r\n                  setFormData((prev) => ({ ...prev, title: e.target.value }))\r\n                }\r\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                placeholder=\"Ej: Contrato de Trabajo Indefinido\"\r\n              />\r\n            </div>\r\n\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                Descripción *\r\n              </label>\r\n              <textarea\r\n                required\r\n                rows={3}\r\n                value={formData.description}\r\n                onChange={(e) =>\r\n                  setFormData((prev) => ({\r\n                    ...prev,\r\n                    description: e.target.value,\r\n                  }))\r\n                }\r\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                placeholder=\"Describe qué incluye esta plantilla y para qué casos es útil...\"\r\n              />\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-2 gap-3\">\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                  Área Legal *\r\n                </label>\r\n                <select\r\n                  value={formData.legalArea}\r\n                  onChange={(e) =>\r\n                    setFormData((prev) => ({\r\n                      ...prev,\r\n                      legalArea: e.target.value,\r\n                    }))\r\n                  }\r\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer\"\r\n                >\r\n                  {legalAreas.map((area) => (\r\n                    <option key={area} value={area}>\r\n                      {area}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                  Tipo de Documento *\r\n                </label>\r\n                <select\r\n                  value={formData.documentType}\r\n                  onChange={(e) =>\r\n                    setFormData((prev) => ({\r\n                      ...prev,\r\n                      documentType: e.target.value,\r\n                    }))\r\n                  }\r\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer\"\r\n                >\r\n                  {documentTypes.map((type) => (\r\n                    <option key={type} value={type}>\r\n                      {type}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <label className=\"flex items-center space-x-2 cursor-pointer\">\r\n                <input\r\n                  type=\"checkbox\"\r\n                  checked={formData.isFree}\r\n                  onChange={(e) =>\r\n                    setFormData((prev) => ({\r\n                      ...prev,\r\n                      isFree: e.target.checked,\r\n                    }))\r\n                  }\r\n                  className=\"text-blue-600 focus:ring-blue-500 cursor-pointer\"\r\n                />\r\n                <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Plantilla gratuita\r\n                </span>\r\n              </label>\r\n            </div>\r\n\r\n            {!formData.isFree && (\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                  Precio (ARS)\r\n                </label>\r\n                <input\r\n                  type=\"number\"\r\n                  min=\"100\"\r\n                  step=\"100\"\r\n                  value={formData.price}\r\n                  onChange={(e) =>\r\n                    setFormData((prev) => ({\r\n                      ...prev,\r\n                      price: parseInt(e.target.value) || 0,\r\n                    }))\r\n                  }\r\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                  placeholder=\"1000\"\r\n                />\r\n              </div>\r\n            )}\r\n\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                Etiquetas (separadas por comas)\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                value={formData.tags}\r\n                onChange={(e) =>\r\n                  setFormData((prev) => ({ ...prev, tags: e.target.value }))\r\n                }\r\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                placeholder=\"contrato, laboral, empleado\"\r\n              />\r\n            </div>\r\n\r\n            <div className=\"flex space-x-3 pt-4\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => setStep(\"upload\")}\r\n                className=\"flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer\"\r\n              >\r\n                Volver\r\n              </button>\r\n              <button\r\n                type=\"submit\"\r\n                className=\"flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer\"\r\n              >\r\n                Vista Previa\r\n              </button>\r\n            </div>\r\n          </form>\r\n        );\r\n\r\n      case \"preview\":\r\n        return (\r\n          <div className=\"space-y-6\">\r\n            <div className=\"text-center\">\r\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2\">\r\n                Vista Previa de la Plantilla\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                Revisa cómo se verá tu plantilla en el marketplace\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-700\">\r\n              <div className=\"space-y-3\">\r\n                <div className=\"flex items-start justify-between\">\r\n                  <h4 className=\"font-semibold text-gray-900 dark:text-gray-100\">\r\n                    {formData.title}\r\n                  </h4>\r\n                  <span className=\"text-green-600 dark:text-green-400 font-semibold text-sm\">\r\n                    {formData.isFree\r\n                      ? \"Gratis\"\r\n                      : `ARS ${formData.price.toLocaleString(\"es-AR\")}`}\r\n                  </span>\r\n                </div>\r\n                <p className=\"text-sm text-gray-600 dark:text-gray-300\">\r\n                  {formData.description}\r\n                </p>\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <span className=\"text-xs font-medium px-2 py-1 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200\">\r\n                    {formData.legalArea}\r\n                  </span>\r\n                  <span className=\"text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-600 px-2 py-1 rounded-full\">\r\n                    {formData.documentType}\r\n                  </span>\r\n                </div>\r\n                {formData.tags && (\r\n                  <div className=\"flex flex-wrap gap-1\">\r\n                    {formData.tags.split(\",\").map((tag, index) => (\r\n                      <span\r\n                        key={index}\r\n                        className=\"text-xs px-2 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded\"\r\n                      >\r\n                        {tag.trim()}\r\n                      </span>\r\n                    ))}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4\">\r\n              <p className=\"text-sm text-yellow-800 dark:text-yellow-200\">\r\n                <strong>Nota:</strong> Tu plantilla será revisada por nuestro\r\n                equipo antes de ser publicada. Este proceso puede tomar hasta 24\r\n                horas.\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"flex space-x-3\">\r\n              <button\r\n                onClick={() => setStep(\"details\")}\r\n                className=\"flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer\"\r\n              >\r\n                Editar\r\n              </button>\r\n              <button\r\n                onClick={handlePublish}\r\n                disabled={isUploading}\r\n                className=\"flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors cursor-pointer\"\r\n              >\r\n                {isUploading ? (\r\n                  <div className=\"flex items-center justify-center\">\r\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n                    Publicando...\r\n                  </div>\r\n                ) : (\r\n                  \"Publicar Plantilla\"\r\n                )}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        );\r\n\r\n      case \"success\":\r\n        return (\r\n          <div className=\"text-center space-y-6\">\r\n            <div className=\"text-green-600 dark:text-green-400\">\r\n              <CheckCircleIcon className=\"h-16 w-16 mx-auto mb-4\" />\r\n            </div>\r\n            <div>\r\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2\">\r\n                ¡Plantilla Enviada!\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                Tu plantilla &quot;{formData.title}&quot; ha sido enviada para\r\n                revisión. Te notificaremos cuando esté disponible en el\r\n                marketplace.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        );\r\n\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog.Root open onOpenChange={onClose}>\r\n      <Dialog.Portal>\r\n        <Dialog.Overlay className=\"fixed inset-0 bg-black/50 dark:bg-black/70 z-50\" />\r\n        <Dialog.Content className=\"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-xl z-50 w-full max-w-lg p-6 max-h-[90vh] overflow-y-auto\">\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <Dialog.Title className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n              Subir Nueva Plantilla\r\n            </Dialog.Title>\r\n            {step !== \"success\" && (\r\n              <Dialog.Close asChild>\r\n                <button className=\"text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 cursor-pointer\">\r\n                  <XMarkIcon className=\"h-5 w-5\" />\r\n                </button>\r\n              </Dialog.Close>\r\n            )}\r\n          </div>\r\n\r\n          {renderStepContent()}\r\n        </Dialog.Content>\r\n      </Dialog.Portal>\r\n    </Dialog.Root>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAMA;;;AAVA;;;;;AAkBA,MAAM,aAAa;IAAC;IAAW;IAAS;IAAW;IAAS;CAAY;AACxE,MAAM,gBAAgB;IAAC;IAAa;IAAY;IAAY;CAAmB;AAExE,SAAS,oBAAoB,EAClC,OAAO,EACP,QAAQ,EACiB;;IACzB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAE7B;IACF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,aAAa;QACb,WAAW;QACX,cAAc;QACd,OAAO;QACP,MAAM;QACN,QAAQ;IACV;IAEA,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC;YAC1B,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,gBAAgB,aAAa,CAAC,EAAE;gBAChC,QAAQ;YACV;QACF;kDAAG,EAAE;IAEL,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ;YACN,mBAAmB;gBAAC;aAAO;YAC3B,sBAAsB;gBAAC;aAAO;YAC9B,2EACE;gBAAC;aAAQ;QACb;QACA,SAAS,KAAK,OAAO;QACrB,UAAU;IACZ;IAEA,MAAM,mBAAmB,CAAC;QACxB,EAAE,cAAc;QAChB,QAAQ;IACV;IAEA,MAAM,gBAAgB;QACpB,eAAe;QAEf,0BAA0B;QAC1B,WAAW;YACT,MAAM,cAAwB;gBAC5B,IAAI,CAAC,IAAI,EAAE,OAAO,UAAU,IAAI;gBAChC,OAAO,SAAS,KAAK;gBACrB,aAAa,SAAS,WAAW;gBACjC,WAAW,SAAS,SAAS;gBAC7B,cAAc,SAAS,YAAY;gBACnC,OAAO,SAAS,MAAM,GAAG,IAAI,SAAS,KAAK;gBAC3C,QAAQ;gBACR,eAAe;gBACf,QAAQ;gBACR,UAAU;gBACV,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;gBACjC,MAAM,SAAS,IAAI,CAChB,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,MAAQ,IAAI,IAAI,IACrB,MAAM,CAAC;gBACV,SAAS;gBACT,SAAS,CAAC,iBAAiB,EAAE,cAAc,MAAM;gBACjD,UAAU;gBACV,QAAQ;YACV;YAEA,SAAS;YACT,eAAe;YACf,QAAQ;YAER,2BAA2B;YAC3B,WAAW;gBACT;YACF,GAAG;QACL,GAAG;IACL;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACE,GAAG,cAAc;4BAClB,WAAW,CAAC,mFAAmF,EAC7F,eACI,mDACA,yFACJ;;8CAEF,6LAAC;oCAAO,GAAG,eAAe;;;;;;8CAC1B,6LAAC,kOAAA,CAAA,mBAAgB;oCAAC,WAAU;;;;;;gCAC3B,6BACC,6LAAC;oCAAE,WAAU;8CAA2C;;;;;yDAIxD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;sDAGxD,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;;;;;;;sCAO9D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;YAMd,KAAK;gBACH,qBACE,6LAAC;oBAAK,UAAU;oBAAkB,WAAU;;sCAC1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,0NAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,6LAAC;oCAAK,WAAU;8CACb,cAAc;;;;;;;;;;;;sCAInB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,OAAO,SAAS,KAAK;oCACrB,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;gDAAE,GAAG,IAAI;gDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCAE3D,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,QAAQ;oCACR,MAAM;oCACN,OAAO,SAAS,WAAW;oCAC3B,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;gDACrB,GAAG,IAAI;gDACP,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC7B,CAAC;oCAEH,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO,SAAS,SAAS;4CACzB,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;wDACrB,GAAG,IAAI;wDACP,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC3B,CAAC;4CAEH,WAAU;sDAET,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;oDAAkB,OAAO;8DACvB;mDADU;;;;;;;;;;;;;;;;8CAOnB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO,SAAS,YAAY;4CAC5B,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;wDACrB,GAAG,IAAI;wDACP,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC9B,CAAC;4CAEH,WAAU;sDAET,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;oDAAkB,OAAO;8DACvB;mDADU;;;;;;;;;;;;;;;;;;;;;;sCAQrB,6LAAC;sCACC,cAAA,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCACC,MAAK;wCACL,SAAS,SAAS,MAAM;wCACxB,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;oDACrB,GAAG,IAAI;oDACP,QAAQ,EAAE,MAAM,CAAC,OAAO;gDAC1B,CAAC;wCAEH,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAU;kDAAuD;;;;;;;;;;;;;;;;;wBAM1E,CAAC,SAAS,MAAM,kBACf,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;gDACrB,GAAG,IAAI;gDACP,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4CACrC,CAAC;oCAEH,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKlB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,MAAK;oCACL,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;gDAAE,GAAG,IAAI;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCAE1D,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS,IAAM,QAAQ;oCACvB,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;YAOT,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA8D;;;;;;8CAG5E,6LAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;sCAK1D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,SAAS,KAAK;;;;;;0DAEjB,6LAAC;gDAAK,WAAU;0DACb,SAAS,MAAM,GACZ,WACA,CAAC,IAAI,EAAE,SAAS,KAAK,CAAC,cAAc,CAAC,UAAU;;;;;;;;;;;;kDAGvD,6LAAC;wCAAE,WAAU;kDACV,SAAS,WAAW;;;;;;kDAEvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DACb,SAAS,SAAS;;;;;;0DAErB,6LAAC;gDAAK,WAAU;0DACb,SAAS,YAAY;;;;;;;;;;;;oCAGzB,SAAS,IAAI,kBACZ,6LAAC;wCAAI,WAAU;kDACZ,SAAS,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,sBAClC,6LAAC;gDAEC,WAAU;0DAET,IAAI,IAAI;+CAHJ;;;;;;;;;;;;;;;;;;;;;sCAWjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;kDACX,6LAAC;kDAAO;;;;;;oCAAc;;;;;;;;;;;;sCAM1B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,QAAQ;oCACvB,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,4BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;4CAAuE;;;;;;+CAIxF;;;;;;;;;;;;;;;;;;YAOZ,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gOAAA,CAAA,kBAAe;gCAAC,WAAU;;;;;;;;;;;sCAE7B,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA8D;;;;;;8CAG5E,6LAAC;oCAAE,WAAU;;wCAA2C;wCAClC,SAAS,KAAK;wCAAC;;;;;;;;;;;;;;;;;;;YAQ7C;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,qKAAA,CAAA,OAAW;QAAC,IAAI;QAAC,cAAc;kBAC9B,cAAA,6LAAC,qKAAA,CAAA,SAAa;;8BACZ,6LAAC,qKAAA,CAAA,UAAc;oBAAC,WAAU;;;;;;8BAC1B,6LAAC,qKAAA,CAAA,UAAc;oBAAC,WAAU;;sCACxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qKAAA,CAAA,QAAY;oCAAC,WAAU;8CAAyD;;;;;;gCAGhF,SAAS,2BACR,6LAAC,qKAAA,CAAA,QAAY;oCAAC,OAAO;8CACnB,cAAA,6LAAC;wCAAO,WAAU;kDAChB,cAAA,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;wBAM5B;;;;;;;;;;;;;;;;;;AAKX;GAtagB;;QA0BwC,2KAAA,CAAA,cAAW;;;KA1BnD", "debugId": null}}, {"offset": {"line": 5730, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/templates/MyTemplatesSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  ArrowDownTrayIcon,\r\n  DocumentIcon,\r\n  TrashIcon,\r\n  PencilIcon,\r\n  EyeIcon,\r\n  ChartBarIcon,\r\n} from \"@heroicons/react/24/outline\";\r\nimport { format } from \"date-fns\";\r\nimport { es } from \"date-fns/locale\";\r\nimport * as Tabs from \"@radix-ui/react-tabs\";\r\nimport { Template, UserTemplate } from \"../../_lib/types\";\r\n\r\nexport function MyTemplatesSection() {\r\n  const [userTemplates, setUserTemplates] = useState<UserTemplate[]>([]);\r\n  const [allTemplates, setAllTemplates] = useState<Template[]>([]);\r\n  const [activeTab, setActiveTab] = useState(\"purchased\");\r\n\r\n  useEffect(() => {\r\n    // Load user's templates from localStorage\r\n    const savedUserTemplates = JSON.parse(\r\n      localStorage.getItem(\"userTemplates\") || \"[]\"\r\n    );\r\n    setUserTemplates(savedUserTemplates);\r\n\r\n    // Load all templates to get details\r\n    const loadTemplates = async () => {\r\n      try {\r\n        const response = await fetch(\"/data/templates.json\");\r\n        const templatesData = await response.json();\r\n        setAllTemplates(templatesData);\r\n      } catch (error) {\r\n        console.error(\"Error loading templates:\", error);\r\n      }\r\n    };\r\n\r\n    loadTemplates();\r\n  }, []);\r\n\r\n  const purchasedTemplates = userTemplates\r\n    .map((ut) => {\r\n      const template = allTemplates.find((t) => t.id === ut.templateId);\r\n      return template ? { ...template, userTemplate: ut } : null;\r\n    })\r\n    .filter(Boolean) as (Template & { userTemplate: UserTemplate })[];\r\n\r\n  const uploadedTemplates = allTemplates.filter(\r\n    (t) => t.authorId === \"current-user\"\r\n  );\r\n\r\n  const handleDownload = (template: Template) => {\r\n    // Simulate download\r\n    console.log(\"Downloading template:\", template.title);\r\n\r\n    // Update download count\r\n    const updatedUserTemplates = userTemplates.map((ut) =>\r\n      ut.templateId === template.id\r\n        ? { ...ut, downloadCount: ut.downloadCount + 1 }\r\n        : ut\r\n    );\r\n    setUserTemplates(updatedUserTemplates);\r\n    localStorage.setItem(\"userTemplates\", JSON.stringify(updatedUserTemplates));\r\n  };\r\n\r\n  const handleDelete = (templateId: string) => {\r\n    const updatedUserTemplates = userTemplates.filter(\r\n      (ut) => ut.templateId !== templateId\r\n    );\r\n    setUserTemplates(updatedUserTemplates);\r\n    localStorage.setItem(\"userTemplates\", JSON.stringify(updatedUserTemplates));\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <Tabs.Root value={activeTab} onValueChange={setActiveTab}>\r\n        <Tabs.List className=\"flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg\">\r\n          <Tabs.Trigger\r\n            value=\"purchased\"\r\n            className=\"flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 text-gray-600 dark:text-gray-300\"\r\n          >\r\n            <ArrowDownTrayIcon className=\"h-4 w-4 mr-2\" />\r\n            Mis Plantillas ({purchasedTemplates.length})\r\n          </Tabs.Trigger>\r\n          <Tabs.Trigger\r\n            value=\"uploaded\"\r\n            className=\"flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 text-gray-600 dark:text-gray-300\"\r\n          >\r\n            <DocumentIcon className=\"h-4 w-4 mr-2\" />\r\n            Subidas ({uploadedTemplates.length})\r\n          </Tabs.Trigger>\r\n        </Tabs.List>\r\n\r\n        <div className=\"mt-6\">\r\n          <Tabs.Content value=\"purchased\">\r\n            {purchasedTemplates.length > 0 ? (\r\n              <div className=\"space-y-4\">\r\n                {purchasedTemplates.map((template, index) => (\r\n                  <motion.div\r\n                    key={template.id}\r\n                    initial={{ opacity: 0, y: 20 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    transition={{ duration: 0.6, delay: index * 0.1 }}\r\n                    className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow\"\r\n                  >\r\n                    <div className=\"flex items-start justify-between\">\r\n                      <div className=\"flex-1\">\r\n                        <div className=\"flex items-center space-x-3 mb-2\">\r\n                          <h3 className=\"font-semibold text-gray-900 dark:text-gray-100\">\r\n                            {template.title}\r\n                          </h3>\r\n                          <span className=\"text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full\">\r\n                            {template.legalArea}\r\n                          </span>\r\n                          {template.price === 0 && (\r\n                            <span className=\"text-xs bg-green-500 text-white px-2 py-1 rounded-full font-bold\">\r\n                              FREE\r\n                            </span>\r\n                          )}\r\n                        </div>\r\n                        <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-3\">\r\n                          {template.description}\r\n                        </p>\r\n                        <div className=\"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400\">\r\n                          <span>\r\n                            {template.price === 0 ? \"Descargado\" : \"Comprado\"}:{\" \"}\r\n                            {format(\r\n                              new Date(template.userTemplate.purchasedAt),\r\n                              \"dd MMM yyyy\",\r\n                              { locale: es }\r\n                            )}\r\n                          </span>\r\n                          <span>\r\n                            Descargas: {template.userTemplate.downloadCount}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"flex items-center space-x-2 ml-4\">\r\n                        <button\r\n                          onClick={() => handleDownload(template)}\r\n                          className=\"p-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-md transition-colors cursor-pointer\"\r\n                          title=\"Descargar\"\r\n                        >\r\n                          <ArrowDownTrayIcon className=\"h-4 w-4\" />\r\n                        </button>\r\n                        <button\r\n                          className=\"p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md transition-colors cursor-pointer\"\r\n                          title=\"Vista previa\"\r\n                        >\r\n                          <EyeIcon className=\"h-4 w-4\" />\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handleDelete(template.id)}\r\n                          className=\"p-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-colors cursor-pointer\"\r\n                          title=\"Eliminar de biblioteca\"\r\n                        >\r\n                          <TrashIcon className=\"h-4 w-4\" />\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n                  </motion.div>\r\n                ))}\r\n              </div>\r\n            ) : (\r\n              <div className=\"text-center py-12\">\r\n                <ArrowDownTrayIcon className=\"h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4\" />\r\n                <p className=\"text-gray-500 dark:text-gray-400 mb-2\">\r\n                  No tienes plantillas en tu biblioteca\r\n                </p>\r\n                <p className=\"text-sm text-gray-400 dark:text-gray-500\">\r\n                  Explora el marketplace para encontrar plantillas útiles para\r\n                  tu práctica\r\n                </p>\r\n              </div>\r\n            )}\r\n          </Tabs.Content>\r\n\r\n          <Tabs.Content value=\"uploaded\">\r\n            {uploadedTemplates.length > 0 ? (\r\n              <div className=\"space-y-4\">\r\n                {uploadedTemplates.map((template, index) => (\r\n                  <motion.div\r\n                    key={template.id}\r\n                    initial={{ opacity: 0, y: 20 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    transition={{ duration: 0.6, delay: index * 0.1 }}\r\n                    className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow\"\r\n                  >\r\n                    <div className=\"flex items-start justify-between\">\r\n                      <div className=\"flex-1\">\r\n                        <div className=\"flex items-center space-x-3 mb-2\">\r\n                          <h3 className=\"font-semibold text-gray-900 dark:text-gray-100\">\r\n                            {template.title}\r\n                          </h3>\r\n                          <span\r\n                            className={`text-xs px-2 py-1 rounded-full ${\r\n                              template.status === \"published\"\r\n                                ? \"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200\"\r\n                                : template.status === \"under_review\"\r\n                                ? \"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200\"\r\n                                : \"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200\"\r\n                            }`}\r\n                          >\r\n                            {template.status === \"published\" && \"Publicado\"}\r\n                            {template.status === \"under_review\" &&\r\n                              \"En Revisión\"}\r\n                            {template.status === \"draft\" && \"Borrador\"}\r\n                          </span>\r\n                        </div>\r\n                        <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-3\">\r\n                          {template.description}\r\n                        </p>\r\n                        <div className=\"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400\">\r\n                          <span>\r\n                            Creado:{\" \"}\r\n                            {format(\r\n                              new Date(template.createdAt),\r\n                              \"dd MMM yyyy\",\r\n                              { locale: es }\r\n                            )}\r\n                          </span>\r\n                          <span>Descargas: {template.downloadCount}</span>\r\n                          <span>\r\n                            Rating:{\" \"}\r\n                            {template.rating > 0\r\n                              ? template.rating.toFixed(1)\r\n                              : \"Sin calificar\"}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"flex items-center space-x-2 ml-4\">\r\n                        <button\r\n                          className=\"p-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-md transition-colors cursor-pointer\"\r\n                          title=\"Ver estadísticas\"\r\n                        >\r\n                          <ChartBarIcon className=\"h-4 w-4\" />\r\n                        </button>\r\n                        <button\r\n                          className=\"p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md transition-colors cursor-pointer\"\r\n                          title=\"Editar\"\r\n                        >\r\n                          <PencilIcon className=\"h-4 w-4\" />\r\n                        </button>\r\n                        <button\r\n                          className=\"p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md transition-colors cursor-pointer\"\r\n                          title=\"Vista previa\"\r\n                        >\r\n                          <EyeIcon className=\"h-4 w-4\" />\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Analytics for published templates */}\r\n                    {template.status === \"published\" &&\r\n                      template.downloadCount > 0 && (\r\n                        <div className=\"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\r\n                          <div className=\"grid grid-cols-3 gap-4 text-center\">\r\n                            <div>\r\n                              <div className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n                                {template.downloadCount}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                                Descargas\r\n                              </div>\r\n                            </div>\r\n                            <div>\r\n                              <div className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n                                ARS{\" \"}\r\n                                {(\r\n                                  template.price * template.downloadCount\r\n                                ).toLocaleString(\"es-AR\")}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                                Ingresos\r\n                              </div>\r\n                            </div>\r\n                            <div>\r\n                              <div className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n                                {template.rating > 0\r\n                                  ? template.rating.toFixed(1)\r\n                                  : \"-\"}\r\n                              </div>\r\n                              <div className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                                Rating\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n                  </motion.div>\r\n                ))}\r\n              </div>\r\n            ) : (\r\n              <div className=\"text-center py-12\">\r\n                <DocumentIcon className=\"h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4\" />\r\n                <p className=\"text-gray-500 dark:text-gray-400 mb-2\">No has subido plantillas</p>\r\n                <p className=\"text-sm text-gray-400 dark:text-gray-500\">\r\n                  Comparte tus plantillas con otros abogados y genera ingresos\r\n                  adicionales\r\n                </p>\r\n              </div>\r\n            )}\r\n          </Tabs.Content>\r\n        </div>\r\n      </Tabs.Root>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;;;AAdA;;;;;;;AAiBO,SAAS;;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,0CAA0C;YAC1C,MAAM,qBAAqB,KAAK,KAAK,CACnC,aAAa,OAAO,CAAC,oBAAoB;YAE3C,iBAAiB;YAEjB,oCAAoC;YACpC,MAAM;8DAAgB;oBACpB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM;wBAC7B,MAAM,gBAAgB,MAAM,SAAS,IAAI;wBACzC,gBAAgB;oBAClB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,4BAA4B;oBAC5C;gBACF;;YAEA;QACF;uCAAG,EAAE;IAEL,MAAM,qBAAqB,cACxB,GAAG,CAAC,CAAC;QACJ,MAAM,WAAW,aAAa,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,GAAG,UAAU;QAChE,OAAO,WAAW;YAAE,GAAG,QAAQ;YAAE,cAAc;QAAG,IAAI;IACxD,GACC,MAAM,CAAC;IAEV,MAAM,oBAAoB,aAAa,MAAM,CAC3C,CAAC,IAAM,EAAE,QAAQ,KAAK;IAGxB,MAAM,iBAAiB,CAAC;QACtB,oBAAoB;QACpB,QAAQ,GAAG,CAAC,yBAAyB,SAAS,KAAK;QAEnD,wBAAwB;QACxB,MAAM,uBAAuB,cAAc,GAAG,CAAC,CAAC,KAC9C,GAAG,UAAU,KAAK,SAAS,EAAE,GACzB;gBAAE,GAAG,EAAE;gBAAE,eAAe,GAAG,aAAa,GAAG;YAAE,IAC7C;QAEN,iBAAiB;QACjB,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;IACvD;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,uBAAuB,cAAc,MAAM,CAC/C,CAAC,KAAO,GAAG,UAAU,KAAK;QAE5B,iBAAiB;QACjB,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;IACvD;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mKAAA,CAAA,OAAS;YAAC,OAAO;YAAW,eAAe;;8BAC1C,6LAAC,mKAAA,CAAA,OAAS;oBAAC,WAAU;;sCACnB,6LAAC,mKAAA,CAAA,UAAY;4BACX,OAAM;4BACN,WAAU;;8CAEV,6LAAC,oOAAA,CAAA,oBAAiB;oCAAC,WAAU;;;;;;gCAAiB;gCAC7B,mBAAmB,MAAM;gCAAC;;;;;;;sCAE7C,6LAAC,mKAAA,CAAA,UAAY;4BACX,OAAM;4BACN,WAAU;;8CAEV,6LAAC,0NAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;gCAAiB;gCAC/B,kBAAkB,MAAM;gCAAC;;;;;;;;;;;;;8BAIvC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mKAAA,CAAA,UAAY;4BAAC,OAAM;sCACjB,mBAAmB,MAAM,GAAG,kBAC3B,6LAAC;gCAAI,WAAU;0CACZ,mBAAmB,GAAG,CAAC,CAAC,UAAU,sBACjC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;wCAChD,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EACX,SAAS,KAAK;;;;;;8EAEjB,6LAAC;oEAAK,WAAU;8EACb,SAAS,SAAS;;;;;;gEAEpB,SAAS,KAAK,KAAK,mBAClB,6LAAC;oEAAK,WAAU;8EAAmE;;;;;;;;;;;;sEAKvF,6LAAC;4DAAE,WAAU;sEACV,SAAS,WAAW;;;;;;sEAEvB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;wEACE,SAAS,KAAK,KAAK,IAAI,eAAe;wEAAW;wEAAE;wEACnD,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EACJ,IAAI,KAAK,SAAS,YAAY,CAAC,WAAW,GAC1C,eACA;4EAAE,QAAQ,8IAAA,CAAA,KAAE;wEAAC;;;;;;;8EAGjB,6LAAC;;wEAAK;wEACQ,SAAS,YAAY,CAAC,aAAa;;;;;;;;;;;;;;;;;;;8DAIrD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,SAAS,IAAM,eAAe;4DAC9B,WAAU;4DACV,OAAM;sEAEN,cAAA,6LAAC,oOAAA,CAAA,oBAAiB;gEAAC,WAAU;;;;;;;;;;;sEAE/B,6LAAC;4DACC,WAAU;4DACV,OAAM;sEAEN,cAAA,6LAAC,gNAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;sEAErB,6LAAC;4DACC,SAAS,IAAM,aAAa,SAAS,EAAE;4DACvC,WAAU;4DACV,OAAM;sEAEN,cAAA,6LAAC,oNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCAzDtB,SAAS,EAAE;;;;;;;;;qDAiEtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oOAAA,CAAA,oBAAiB;wCAAC,WAAU;;;;;;kDAC7B,6LAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAGrD,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;;;;;;sCAQ9D,6LAAC,mKAAA,CAAA,UAAY;4BAAC,OAAM;sCACjB,kBAAkB,MAAM,GAAG,kBAC1B,6LAAC;gCAAI,WAAU;0CACZ,kBAAkB,GAAG,CAAC,CAAC,UAAU,sBAChC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;wCAChD,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFACX,SAAS,KAAK;;;;;;kFAEjB,6LAAC;wEACC,WAAW,CAAC,+BAA+B,EACzC,SAAS,MAAM,KAAK,cAChB,sEACA,SAAS,MAAM,KAAK,iBACpB,0EACA,iEACJ;;4EAED,SAAS,MAAM,KAAK,eAAe;4EACnC,SAAS,MAAM,KAAK,kBACnB;4EACD,SAAS,MAAM,KAAK,WAAW;;;;;;;;;;;;;0EAGpC,6LAAC;gEAAE,WAAU;0EACV,SAAS,WAAW;;;;;;0EAEvB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;4EAAK;4EACI;4EACP,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EACJ,IAAI,KAAK,SAAS,SAAS,GAC3B,eACA;gFAAE,QAAQ,8IAAA,CAAA,KAAE;4EAAC;;;;;;;kFAGjB,6LAAC;;4EAAK;4EAAY,SAAS,aAAa;;;;;;;kFACxC,6LAAC;;4EAAK;4EACI;4EACP,SAAS,MAAM,GAAG,IACf,SAAS,MAAM,CAAC,OAAO,CAAC,KACxB;;;;;;;;;;;;;;;;;;;kEAIV,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,WAAU;gEACV,OAAM;0EAEN,cAAA,6LAAC,0NAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;0EAE1B,6LAAC;gEACC,WAAU;gEACV,OAAM;0EAEN,cAAA,6LAAC,sNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;0EAExB,6LAAC;gEACC,WAAU;gEACV,OAAM;0EAEN,cAAA,6LAAC,gNAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;4CAMxB,SAAS,MAAM,KAAK,eACnB,SAAS,aAAa,GAAG,mBACvB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EACZ,SAAS,aAAa;;;;;;8EAEzB,6LAAC;oEAAI,WAAU;8EAA2C;;;;;;;;;;;;sEAI5D,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;;wEAAyD;wEAClE;wEACH,CACC,SAAS,KAAK,GAAG,SAAS,aAAa,AACzC,EAAE,cAAc,CAAC;;;;;;;8EAEnB,6LAAC;oEAAI,WAAU;8EAA2C;;;;;;;;;;;;sEAI5D,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EACZ,SAAS,MAAM,GAAG,IACf,SAAS,MAAM,CAAC,OAAO,CAAC,KACxB;;;;;;8EAEN,6LAAC;oEAAI,WAAU;8EAA2C;;;;;;;;;;;;;;;;;;;;;;;;uCApG/D,SAAS,EAAE;;;;;;;;;qDA+GtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,0NAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,6LAAC;wCAAE,WAAU;kDAAwC;;;;;;kDACrD,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWxE;GArSgB;KAAA", "debugId": null}}, {"offset": {"line": 6414, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/templates/TemplatesMarketplace.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  MagnifyingGlassIcon,\r\n  PlusIcon,\r\n  ArrowDownTrayIcon,\r\n} from \"@heroicons/react/24/outline\";\r\nimport { StarIcon as StarSolidIcon } from \"@heroicons/react/24/solid\";\r\nimport * as Tabs from \"@radix-ui/react-tabs\";\r\nimport { Template, TemplateFilters } from \"../../_lib/types\";\r\nimport { TemplateCard } from \"./TemplateCard\";\r\nimport { TemplateFilters as TemplateFiltersComponent } from \"./TemplateFilters\";\r\nimport { UploadTemplateModal } from \"../modals/UploadTemplateModal\";\r\nimport { MyTemplatesSection } from \"./MyTemplatesSection\";\r\n\r\nexport function TemplatesMarketplace() {\r\n  const [templates, setTemplates] = useState<Template[]>([]);\r\n  const [filteredTemplates, setFilteredTemplates] = useState<Template[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [activeTab, setActiveTab] = useState(\"marketplace\");\r\n  const [showUploadModal, setShowUploadModal] = useState(false);\r\n  const [filters, setFilters] = useState<TemplateFilters>({\r\n    legalArea: \"all\",\r\n    documentType: \"all\",\r\n    priceRange: \"all\",\r\n    sortBy: \"popularity\",\r\n    searchTerm: \"\",\r\n  });\r\n\r\n  // Load templates from JSON\r\n  useEffect(() => {\r\n    const loadTemplates = async () => {\r\n      try {\r\n        const response = await fetch(\"/data/templates.json\");\r\n        const templatesData = await response.json();\r\n        setTemplates(templatesData);\r\n        setFilteredTemplates(templatesData);\r\n      } catch (error) {\r\n        console.error(\"Error loading templates:\", error);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadTemplates();\r\n  }, []);\r\n\r\n  // Apply filters and sorting\r\n  useEffect(() => {\r\n    let filtered = [...templates];\r\n\r\n    // Search filter\r\n    if (filters.searchTerm) {\r\n      filtered = filtered.filter(\r\n        (template) =>\r\n          template.title\r\n            .toLowerCase()\r\n            .includes(filters.searchTerm.toLowerCase()) ||\r\n          template.description\r\n            .toLowerCase()\r\n            .includes(filters.searchTerm.toLowerCase()) ||\r\n          template.tags.some((tag) =>\r\n            tag.toLowerCase().includes(filters.searchTerm.toLowerCase())\r\n          )\r\n      );\r\n    }\r\n\r\n    // Legal area filter\r\n    if (filters.legalArea !== \"all\") {\r\n      filtered = filtered.filter(\r\n        (template) => template.legalArea === filters.legalArea\r\n      );\r\n    }\r\n\r\n    // Document type filter\r\n    if (filters.documentType !== \"all\") {\r\n      filtered = filtered.filter(\r\n        (template) => template.documentType === filters.documentType\r\n      );\r\n    }\r\n\r\n    // Price range filter\r\n    if (filters.priceRange === \"free\") {\r\n      filtered = filtered.filter((template) => template.price === 0);\r\n    } else if (filters.priceRange === \"paid\") {\r\n      filtered = filtered.filter((template) => template.price > 0);\r\n    }\r\n\r\n    // Sorting\r\n    switch (filters.sortBy) {\r\n      case \"popularity\":\r\n        filtered.sort((a, b) => b.downloadCount - a.downloadCount);\r\n        break;\r\n      case \"rating\":\r\n        filtered.sort((a, b) => b.rating - a.rating);\r\n        break;\r\n      case \"newest\":\r\n        filtered.sort(\r\n          (a, b) =>\r\n            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()\r\n        );\r\n        break;\r\n      case \"price_low\":\r\n        filtered.sort((a, b) => a.price - b.price);\r\n        break;\r\n      case \"price_high\":\r\n        filtered.sort((a, b) => b.price - a.price);\r\n        break;\r\n    }\r\n\r\n    setFilteredTemplates(filtered);\r\n  }, [templates, filters]);\r\n\r\n  const featuredTemplates = filteredTemplates.filter(\r\n    (template) => template.featured\r\n  );\r\n  const regularTemplates = filteredTemplates.filter(\r\n    (template) => !template.featured\r\n  );\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex justify-center items-center h-64\">\r\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <Tabs.Root value={activeTab} onValueChange={setActiveTab}>\r\n        <Tabs.List className=\"flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg\">\r\n          <Tabs.Trigger\r\n            value=\"marketplace\"\r\n            className=\"flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 text-gray-600 dark:text-gray-300\"\r\n          >\r\n            <MagnifyingGlassIcon className=\"h-4 w-4 mr-2\" />\r\n            Marketplace\r\n          </Tabs.Trigger>\r\n          <Tabs.Trigger\r\n            value=\"my-templates\"\r\n            className=\"flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 text-gray-600 dark:text-gray-300\"\r\n          >\r\n            <ArrowDownTrayIcon className=\"h-4 w-4 mr-2\" />\r\n            Mis Plantillas\r\n          </Tabs.Trigger>\r\n        </Tabs.List>\r\n\r\n        <div className=\"mt-6\">\r\n          <Tabs.Content value=\"marketplace\">\r\n            <div className=\"space-y-6\">\r\n              {/* Search and Filters */}\r\n              <div className=\"flex flex-col lg:flex-row gap-4\">\r\n                <div className=\"flex-1\">\r\n                  <div className=\"relative\">\r\n                    <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500\" />\r\n                    <input\r\n                      type=\"text\"\r\n                      value={filters.searchTerm}\r\n                      onChange={(e) =>\r\n                        setFilters((prev) => ({\r\n                          ...prev,\r\n                          searchTerm: e.target.value,\r\n                        }))\r\n                      }\r\n                      placeholder=\"Buscar plantillas...\"\r\n                      className=\"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex gap-2\">\r\n                  <TemplateFiltersComponent\r\n                    filters={filters}\r\n                    onFiltersChange={setFilters}\r\n                  />\r\n                  <button\r\n                    onClick={() => setShowUploadModal(true)}\r\n                    className=\"inline-flex items-center px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white text-sm font-medium rounded-md hover:bg-blue-700 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900 transition-colors cursor-pointer\"\r\n                  >\r\n                    <PlusIcon className=\"h-4 w-4 mr-2\" />\r\n                    Subir Plantilla\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Featured Templates */}\r\n              {featuredTemplates.length > 0 && (\r\n                <div>\r\n                  <h2 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center\">\r\n                    <StarSolidIcon className=\"h-5 w-5 text-yellow-400 mr-2\" />\r\n                    Plantillas Destacadas\r\n                  </h2>\r\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n                    {featuredTemplates.map((template, index) => (\r\n                      <motion.div\r\n                        key={template.id}\r\n                        initial={{ opacity: 0, y: 20 }}\r\n                        animate={{ opacity: 1, y: 0 }}\r\n                        transition={{ duration: 0.6, delay: index * 0.1 }}\r\n                      >\r\n                        <TemplateCard template={template} featured />\r\n                      </motion.div>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Regular Templates */}\r\n              <div>\r\n                <div className=\"flex items-center justify-between mb-4\">\r\n                  <h2 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n                    Todas las Plantillas\r\n                  </h2>\r\n                  <span className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                    {filteredTemplates.length} plantillas encontradas\r\n                  </span>\r\n                </div>\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n                  {regularTemplates.map((template, index) => (\r\n                    <motion.div\r\n                      key={template.id}\r\n                      initial={{ opacity: 0, y: 20 }}\r\n                      animate={{ opacity: 1, y: 0 }}\r\n                      transition={{ duration: 0.6, delay: index * 0.1 }}\r\n                    >\r\n                      <TemplateCard template={template} />\r\n                    </motion.div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n\r\n              {filteredTemplates.length === 0 && (\r\n                <div className=\"text-center py-12\">\r\n                  <MagnifyingGlassIcon className=\"h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4\" />\r\n                  <p className=\"text-gray-500 dark:text-gray-400\">\r\n                    No se encontraron plantillas que coincidan con los filtros\r\n                    seleccionados.\r\n                  </p>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </Tabs.Content>\r\n\r\n          <Tabs.Content value=\"my-templates\">\r\n            <MyTemplatesSection />\r\n          </Tabs.Content>\r\n        </div>\r\n      </Tabs.Root>\r\n\r\n      {/* Upload Modal */}\r\n      {showUploadModal && (\r\n        <UploadTemplateModal\r\n          onClose={() => setShowUploadModal(false)}\r\n          onUpload={(template) => {\r\n            setTemplates((prev) => [...prev, template]);\r\n            setShowUploadModal(false);\r\n          }}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAKA;AACA;AAEA;AACA;AACA;AACA;;;AAfA;;;;;;;;;;AAiBO,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;QACtD,WAAW;QACX,cAAc;QACd,YAAY;QACZ,QAAQ;QACR,YAAY;IACd;IAEA,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,MAAM;gEAAgB;oBACpB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM;wBAC7B,MAAM,gBAAgB,MAAM,SAAS,IAAI;wBACzC,aAAa;wBACb,qBAAqB;oBACvB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,4BAA4B;oBAC5C,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;yCAAG,EAAE;IAEL,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,WAAW;mBAAI;aAAU;YAE7B,gBAAgB;YAChB,IAAI,QAAQ,UAAU,EAAE;gBACtB,WAAW,SAAS,MAAM;sDACxB,CAAC,WACC,SAAS,KAAK,CACX,WAAW,GACX,QAAQ,CAAC,QAAQ,UAAU,CAAC,WAAW,OAC1C,SAAS,WAAW,CACjB,WAAW,GACX,QAAQ,CAAC,QAAQ,UAAU,CAAC,WAAW,OAC1C,SAAS,IAAI,CAAC,IAAI;8DAAC,CAAC,MAClB,IAAI,WAAW,GAAG,QAAQ,CAAC,QAAQ,UAAU,CAAC,WAAW;;;YAGjE;YAEA,oBAAoB;YACpB,IAAI,QAAQ,SAAS,KAAK,OAAO;gBAC/B,WAAW,SAAS,MAAM;sDACxB,CAAC,WAAa,SAAS,SAAS,KAAK,QAAQ,SAAS;;YAE1D;YAEA,uBAAuB;YACvB,IAAI,QAAQ,YAAY,KAAK,OAAO;gBAClC,WAAW,SAAS,MAAM;sDACxB,CAAC,WAAa,SAAS,YAAY,KAAK,QAAQ,YAAY;;YAEhE;YAEA,qBAAqB;YACrB,IAAI,QAAQ,UAAU,KAAK,QAAQ;gBACjC,WAAW,SAAS,MAAM;sDAAC,CAAC,WAAa,SAAS,KAAK,KAAK;;YAC9D,OAAO,IAAI,QAAQ,UAAU,KAAK,QAAQ;gBACxC,WAAW,SAAS,MAAM;sDAAC,CAAC,WAAa,SAAS,KAAK,GAAG;;YAC5D;YAEA,UAAU;YACV,OAAQ,QAAQ,MAAM;gBACpB,KAAK;oBACH,SAAS,IAAI;0DAAC,CAAC,GAAG,IAAM,EAAE,aAAa,GAAG,EAAE,aAAa;;oBACzD;gBACF,KAAK;oBACH,SAAS,IAAI;0DAAC,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,MAAM;;oBAC3C;gBACF,KAAK;oBACH,SAAS,IAAI;0DACX,CAAC,GAAG,IACF,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;;oBAEnE;gBACF,KAAK;oBACH,SAAS,IAAI;0DAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;;oBACzC;gBACF,KAAK;oBACH,SAAS,IAAI;0DAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;;oBACzC;YACJ;YAEA,qBAAqB;QACvB;yCAAG;QAAC;QAAW;KAAQ;IAEvB,MAAM,oBAAoB,kBAAkB,MAAM,CAChD,CAAC,WAAa,SAAS,QAAQ;IAEjC,MAAM,mBAAmB,kBAAkB,MAAM,CAC/C,CAAC,WAAa,CAAC,SAAS,QAAQ;IAGlC,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mKAAA,CAAA,OAAS;gBAAC,OAAO;gBAAW,eAAe;;kCAC1C,6LAAC,mKAAA,CAAA,OAAS;wBAAC,WAAU;;0CACnB,6LAAC,mKAAA,CAAA,UAAY;gCACX,OAAM;gCACN,WAAU;;kDAEV,6LAAC,wOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGlD,6LAAC,mKAAA,CAAA,UAAY;gCACX,OAAM;gCACN,WAAU;;kDAEV,6LAAC,oOAAA,CAAA,oBAAiB;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAKlD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mKAAA,CAAA,UAAY;gCAAC,OAAM;0CAClB,cAAA,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,wOAAA,CAAA,sBAAmB;gEAAC,WAAU;;;;;;0EAC/B,6LAAC;gEACC,MAAK;gEACL,OAAO,QAAQ,UAAU;gEACzB,UAAU,CAAC,IACT,WAAW,CAAC,OAAS,CAAC;4EACpB,GAAG,IAAI;4EACP,YAAY,EAAE,MAAM,CAAC,KAAK;wEAC5B,CAAC;gEAEH,aAAY;gEACZ,WAAU;;;;;;;;;;;;;;;;;8DAIhB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,sJAAA,CAAA,kBAAwB;4DACvB,SAAS;4DACT,iBAAiB;;;;;;sEAEnB,6LAAC;4DACC,SAAS,IAAM,mBAAmB;4DAClC,WAAU;;8EAEV,6LAAC,kNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;wCAO1C,kBAAkB,MAAM,GAAG,mBAC1B,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC,gNAAA,CAAA,WAAa;4DAAC,WAAU;;;;;;wDAAiC;;;;;;;8DAG5D,6LAAC;oDAAI,WAAU;8DACZ,kBAAkB,GAAG,CAAC,CAAC,UAAU,sBAChC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DAET,SAAS;gEAAE,SAAS;gEAAG,GAAG;4DAAG;4DAC7B,SAAS;gEAAE,SAAS;gEAAG,GAAG;4DAAE;4DAC5B,YAAY;gEAAE,UAAU;gEAAK,OAAO,QAAQ;4DAAI;sEAEhD,cAAA,6LAAC,mJAAA,CAAA,eAAY;gEAAC,UAAU;gEAAU,QAAQ;;;;;;2DALrC,SAAS,EAAE;;;;;;;;;;;;;;;;sDAa1B,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAyD;;;;;;sEAGvE,6LAAC;4DAAK,WAAU;;gEACb,kBAAkB,MAAM;gEAAC;;;;;;;;;;;;;8DAG9B,6LAAC;oDAAI,WAAU;8DACZ,iBAAiB,GAAG,CAAC,CAAC,UAAU,sBAC/B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DAET,SAAS;gEAAE,SAAS;gEAAG,GAAG;4DAAG;4DAC7B,SAAS;gEAAE,SAAS;gEAAG,GAAG;4DAAE;4DAC5B,YAAY;gEAAE,UAAU;gEAAK,OAAO,QAAQ;4DAAI;sEAEhD,cAAA,6LAAC,mJAAA,CAAA,eAAY;gEAAC,UAAU;;;;;;2DALnB,SAAS,EAAE;;;;;;;;;;;;;;;;wCAWvB,kBAAkB,MAAM,KAAK,mBAC5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,wOAAA,CAAA,sBAAmB;oDAAC,WAAU;;;;;;8DAC/B,6LAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;;;;;;;;;;;;0CASxD,6LAAC,mKAAA,CAAA,UAAY;gCAAC,OAAM;0CAClB,cAAA,6LAAC,yJAAA,CAAA,qBAAkB;;;;;;;;;;;;;;;;;;;;;;YAMxB,iCACC,6LAAC,uJAAA,CAAA,sBAAmB;gBAClB,SAAS,IAAM,mBAAmB;gBAClC,UAAU,CAAC;oBACT,aAAa,CAAC,OAAS;+BAAI;4BAAM;yBAAS;oBAC1C,mBAAmB;gBACrB;;;;;;;;;;;;AAKV;GAtPgB;KAAA", "debugId": null}}, {"offset": {"line": 6923, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/layout/DashboardTabs.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { useSearchParams, useRouter, usePathname } from \"next/navigation\";\r\nimport * as Tabs from \"@radix-ui/react-tabs\";\r\nimport {\r\n  BriefcaseIcon,\r\n  MagnifyingGlassIcon,\r\n  DocumentTextIcon,\r\n} from \"@heroicons/react/24/outline\";\r\n\r\ninterface DashboardTabsProps {\r\n  children?: React.ReactNode;\r\n  defaultTab?: string;\r\n}\r\n\r\nexport function DashboardTabs({ children, defaultTab = \"my-cases\" }: DashboardTabsProps) {\r\n  const searchParams = useSearchParams();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const [activeTab, setActiveTab] = useState(defaultTab);\r\n\r\n  // Determinar el tab activo basado en la ruta actual\r\n  useEffect(() => {\r\n    if (pathname.includes(\"/dashboard/\") && pathname !== \"/dashboard\") {\r\n      // Si estamos en una página de detalle, mantener el tab \"my-cases\"\r\n      setActiveTab(\"my-cases\");\r\n    } else {\r\n      // Si estamos en la página principal del dashboard, usar el tab de la URL\r\n      const tabFromUrl = searchParams.get(\"tab\");\r\n      if (tabFromUrl && (tabFromUrl === \"my-cases\" || tabFromUrl === \"available-cases\" || tabFromUrl === \"templates\")) {\r\n        setActiveTab(tabFromUrl);\r\n      } else {\r\n        setActiveTab(\"my-cases\");\r\n      }\r\n    }\r\n  }, [searchParams, pathname]);\r\n\r\n  // Manejar cambio de tab y actualizar URL\r\n  const handleTabChange = (newTab: string) => {\r\n    setActiveTab(newTab);\r\n    // Navegar a la página principal del dashboard con el tab seleccionado\r\n    const newUrl = newTab === \"my-cases\" ? \"/dashboard\" : `/dashboard?tab=${newTab}`;\r\n    router.push(newUrl);\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-4 sm:space-y-6\">\r\n      <Tabs.Root value={activeTab} onValueChange={handleTabChange}>\r\n        {/* Tabs list with responsive design */}\r\n        <div className=\"w-full overflow-x-auto scrollbar-hide\">\r\n          <Tabs.List className=\"flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg min-w-fit w-full sm:w-auto\">\r\n            <Tabs.Trigger\r\n              value=\"my-cases\"\r\n              className=\"flex items-center cursor-pointer whitespace-nowrap px-2 sm:px-4 py-2 text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md transition-colors hover:bg-gray-200 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 min-w-0 flex-shrink-0\"\r\n            >\r\n              <BriefcaseIcon className=\"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 flex-shrink-0\" />\r\n              <span className=\"hidden sm:inline\">Mis Casos</span>\r\n              <span className=\"sm:hidden\">Casos</span>\r\n            </Tabs.Trigger>\r\n            <Tabs.Trigger\r\n              value=\"available-cases\"\r\n              className=\"flex items-center cursor-pointer whitespace-nowrap px-2 sm:px-4 py-2 text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md transition-colors hover:bg-gray-200 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 min-w-0 flex-shrink-0\"\r\n            >\r\n              <MagnifyingGlassIcon className=\"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 flex-shrink-0\" />\r\n              <span className=\"hidden sm:inline\">Casos Disponibles</span>\r\n              <span className=\"sm:hidden\">Disponibles</span>\r\n            </Tabs.Trigger>\r\n            <Tabs.Trigger\r\n              value=\"templates\"\r\n              className=\"flex items-center cursor-pointer whitespace-nowrap px-2 sm:px-4 py-2 text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md transition-colors hover:bg-gray-200 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 min-w-0 flex-shrink-0\"\r\n            >\r\n              <DocumentTextIcon className=\"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 flex-shrink-0\" />\r\n              <span className=\"hidden sm:inline\">Plantillas</span>\r\n              <span className=\"sm:hidden\">Docs</span>\r\n            </Tabs.Trigger>\r\n          </Tabs.List>\r\n        </div>\r\n\r\n        <div className=\"mt-4 sm:mt-6\">\r\n          {children}\r\n        </div>\r\n      </Tabs.Root>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;;;AALA;;;;;AAgBO,SAAS,cAAc,EAAE,QAAQ,EAAE,aAAa,UAAU,EAAsB;;IACrF,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,oDAAoD;IACpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,SAAS,QAAQ,CAAC,kBAAkB,aAAa,cAAc;gBACjE,kEAAkE;gBAClE,aAAa;YACf,OAAO;gBACL,yEAAyE;gBACzE,MAAM,aAAa,aAAa,GAAG,CAAC;gBACpC,IAAI,cAAc,CAAC,eAAe,cAAc,eAAe,qBAAqB,eAAe,WAAW,GAAG;oBAC/G,aAAa;gBACf,OAAO;oBACL,aAAa;gBACf;YACF;QACF;kCAAG;QAAC;QAAc;KAAS;IAE3B,yCAAyC;IACzC,MAAM,kBAAkB,CAAC;QACvB,aAAa;QACb,sEAAsE;QACtE,MAAM,SAAS,WAAW,aAAa,eAAe,CAAC,eAAe,EAAE,QAAQ;QAChF,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mKAAA,CAAA,OAAS;YAAC,OAAO;YAAW,eAAe;;8BAE1C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,mKAAA,CAAA,OAAS;wBAAC,WAAU;;0CACnB,6LAAC,mKAAA,CAAA,UAAY;gCACX,OAAM;gCACN,WAAU;;kDAEV,6LAAC,4NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,6LAAC;wCAAK,WAAU;kDAAmB;;;;;;kDACnC,6LAAC;wCAAK,WAAU;kDAAY;;;;;;;;;;;;0CAE9B,6LAAC,mKAAA,CAAA,UAAY;gCACX,OAAM;gCACN,WAAU;;kDAEV,6LAAC,wOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;kDAC/B,6LAAC;wCAAK,WAAU;kDAAmB;;;;;;kDACnC,6LAAC;wCAAK,WAAU;kDAAY;;;;;;;;;;;;0CAE9B,6LAAC,mKAAA,CAAA,UAAY;gCACX,OAAM;gCACN,WAAU;;kDAEV,6LAAC,kOAAA,CAAA,mBAAgB;wCAAC,WAAU;;;;;;kDAC5B,6LAAC;wCAAK,WAAU;kDAAmB;;;;;;kDACnC,6LAAC;wCAAK,WAAU;kDAAY;;;;;;;;;;;;;;;;;;;;;;;8BAKlC,6LAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX;GArEgB;;QACO,qIAAA,CAAA,kBAAe;QACrB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAHd", "debugId": null}}, {"offset": {"line": 7133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { CaseList } from \"../_components/cases/CaseList\";\r\nimport { AvailableCases } from \"../_components/cases/AvailableCases\";\r\nimport { TemplatesMarketplace } from \"../_components/templates/TemplatesMarketplace\";\r\nimport { DashboardTabs } from \"../_components/layout/DashboardTabs\";\r\nimport * as Tabs from \"@radix-ui/react-tabs\";\r\n\r\nexport default function DashboardPage() {\r\n  return (\r\n    <DashboardTabs>\r\n      <Tabs.Content value=\"my-cases\">\r\n        <CaseList />\r\n      </Tabs.Content>\r\n      <Tabs.Content value=\"available-cases\">\r\n        <AvailableCases />\r\n      </Tabs.Content>\r\n      <Tabs.Content value=\"templates\">\r\n        <TemplatesMarketplace />\r\n      </Tabs.Content>\r\n    </DashboardTabs>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,qBACE,6LAAC,iJAAA,CAAA,gBAAa;;0BACZ,6LAAC,mKAAA,CAAA,UAAY;gBAAC,OAAM;0BAClB,cAAA,6LAAC,2IAAA,CAAA,WAAQ;;;;;;;;;;0BAEX,6LAAC,mKAAA,CAAA,UAAY;gBAAC,OAAM;0BAClB,cAAA,6LAAC,iJAAA,CAAA,iBAAc;;;;;;;;;;0BAEjB,6LAAC,mKAAA,CAAA,UAAY;gBAAC,OAAM;0BAClB,cAAA,6LAAC,2JAAA,CAAA,uBAAoB;;;;;;;;;;;;;;;;AAI7B;KAdwB", "debugId": null}}]}