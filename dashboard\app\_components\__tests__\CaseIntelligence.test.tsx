import { describe, it, expect, vi } from "vitest";
import { render, screen } from "@testing-library/react";
import { CaseIntelligence } from "../cases/CaseIntelligence";
import type { Case } from "../../_lib/types";

// Mock framer-motion to avoid animation issues in tests
vi.mock("framer-motion", () => ({
  motion: {
    div: ({ children, ...props }: React.ComponentProps<"div">) => (
      <div {...props}>{children}</div>
    ),
  },
}));

const mockCaseWithRecommendations: Case = {
  id: "c-001",
  title: "Test Case with Recommendations",
  type: "Laboral",
  client: "Test Client",
  status: "new",
  progress: 0.2,
  createdAt: "2025-06-14T10:30:00-03:00",
  similarCount: 2,
  description: "Test case description",
  priority: "high",
  estimatedCost: "ARS 150,000 - 200,000",
  complexityScore: 7,
  riskAssessment: "medium",
  successProbability: 85,
  aiSummary: "Test AI summary",
  keyFacts: ["Test fact 1", "Test fact 2"],
  nextActions: [
    "Revisar cálculo de indemnización",
    "Preparar carta documento",
    "Evaluar negociación extrajudicial",
    "Solicitar certificado de trabajo",
  ],
  timeTracking: {
    totalHours: 12.5,
    billableHours: 10,
    lastActivity: "2025-06-16T14:30:00-03:00",
  },
  milestones: [],
  messages: [],
  documents: [],
  activities: [],
};

const mockCaseWithoutRecommendations: Case = {
  id: "c-002",
  title: "Test Case without Recommendations",
  type: "Civil",
  client: "Test Client 2",
  status: "in_progress",
  progress: 0.6,
  createdAt: "2025-06-10T15:00:00-03:00",
  similarCount: 0,
  description: "Test case without recommendations",
  priority: "medium",
  estimatedCost: "ARS 60,000 - 90,000",
  complexityScore: 4,
  riskAssessment: "low",
  successProbability: 85,
  aiSummary: "Test AI summary without recommendations",
  keyFacts: ["Test fact 1", "Test fact 2"],
  // nextActions is missing - this should fail the test
  milestones: [],
  messages: [],
  documents: [],
  activities: [],
};

describe("CaseIntelligence", () => {
  it("should display AI recommendations when nextActions are provided", () => {
    render(<CaseIntelligence case={mockCaseWithRecommendations} />);

    // Check that the AI recommendations section is displayed
    expect(screen.getByText("Recomendaciones IA")).toBeInTheDocument();

    // Check that specific recommendations are displayed (only first 3)
    expect(
      screen.getByText("Revisar cálculo de indemnización")
    ).toBeInTheDocument();
    expect(screen.getByText("Preparar carta documento")).toBeInTheDocument();
    expect(
      screen.getByText("Evaluar negociación extrajudicial")
    ).toBeInTheDocument();

    // Fourth recommendation should not be displayed (component shows only first 3)
    expect(
      screen.queryByText("Solicitar certificado de trabajo")
    ).not.toBeInTheDocument();
  });

  it("should display AI recommendations for Civil cases", () => {
    const civilCase: Case = {
      ...mockCaseWithoutRecommendations,
      nextActions: [
        "Solicitar peritaje técnico de daños",
        "Enviar carta documento al consorcio",
        "Recopilar presupuestos de reparación",
      ],
    };

    render(<CaseIntelligence case={civilCase} />);

    expect(screen.getByText("Recomendaciones IA")).toBeInTheDocument();
    expect(
      screen.getByText("Solicitar peritaje técnico de daños")
    ).toBeInTheDocument();
    expect(
      screen.getByText("Enviar carta documento al consorcio")
    ).toBeInTheDocument();
    expect(
      screen.getByText("Recopilar presupuestos de reparación")
    ).toBeInTheDocument();
  });

  it("should display AI recommendations for Familia cases", () => {
    const familiaCase: Case = {
      ...mockCaseWithoutRecommendations,
      type: "Familia",
      nextActions: [
        "Completar inventario de bienes",
        "Programar audiencia de conciliación",
        "Revisar documentación de menores",
      ],
    };

    render(<CaseIntelligence case={familiaCase} />);

    expect(screen.getByText("Recomendaciones IA")).toBeInTheDocument();
    expect(
      screen.getByText("Completar inventario de bienes")
    ).toBeInTheDocument();
    expect(
      screen.getByText("Programar audiencia de conciliación")
    ).toBeInTheDocument();
    expect(
      screen.getByText("Revisar documentación de menores")
    ).toBeInTheDocument();
  });

  it("should display AI recommendations for Penal cases", () => {
    const penalCase: Case = {
      ...mockCaseWithoutRecommendations,
      type: "Penal",
      nextActions: [
        "Solicitar embargo preventivo de bienes",
        "Constituirse como querellante",
        "Recopilar pruebas documentales",
      ],
    };

    render(<CaseIntelligence case={penalCase} />);

    expect(screen.getByText("Recomendaciones IA")).toBeInTheDocument();
    expect(
      screen.getByText("Solicitar embargo preventivo de bienes")
    ).toBeInTheDocument();
    expect(
      screen.getByText("Constituirse como querellante")
    ).toBeInTheDocument();
    expect(
      screen.getByText("Recopilar pruebas documentales")
    ).toBeInTheDocument();
  });

  it("should fail when case lacks AI recommendations", () => {
    // This test should fail until we add nextActions to all cases
    expect(() => {
      render(<CaseIntelligence case={mockCaseWithoutRecommendations} />);

      // This should fail because the case doesn't have nextActions
      expect(screen.getByText("Recomendaciones IA")).toBeInTheDocument();
      expect(
        screen.getByText("Solicitar peritaje técnico de daños")
      ).toBeInTheDocument();
    }).toThrow();
  });

  it("should display other case intelligence metrics", () => {
    render(<CaseIntelligence case={mockCaseWithRecommendations} />);

    // Check that other intelligence metrics are displayed
    expect(screen.getByText("Inteligencia del Caso")).toBeInTheDocument();
    expect(screen.getByText("Tiempo Invertido")).toBeInTheDocument();
    expect(screen.getByText("12.5h")).toBeInTheDocument();
    expect(screen.getByText("Prob. Éxito")).toBeInTheDocument();
    expect(screen.getByText("85%")).toBeInTheDocument();
  });
});
