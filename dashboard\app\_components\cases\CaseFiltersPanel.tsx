"use client";

import { XMarkIcon } from "@heroicons/react/24/outline";
import { CaseFilters } from "../../_lib/types";

interface CaseFiltersPanelProps {
  filters: CaseFilters;
  onFiltersChange: (filters: CaseFilters) => void;
  onClose: () => void;
}

const caseTypes = [
  { value: "all", label: "Todos los tipos" },
  { value: "Laboral", label: "Laboral" },
  { value: "Civil", label: "Civil" },
  { value: "Familia", label: "Familia" },
  { value: "Penal", label: "Penal" },
  { value: "Comercial", label: "Comercial" },
];

const locations = [
  { value: "all", label: "Todas las ubicaciones" },
  { value: "CABA", label: "CABA" },
  { value: "Buenos Aires", label: "Buenos Aires" },
  { value: "Córdoba", label: "Córdoba" },
  { value: "Santa Fe", label: "Santa Fe" },
  { value: "Mendoza", label: "Mendoza" },
  { value: "Tucum<PERSON>", label: "Tucumán" },
  { value: "Salta", label: "Salta" },
];

const budgetRanges = [
  { value: "all", label: "Todos los presupuestos" },
  { value: "0-100000", label: "Hasta ARS 100,000" },
  { value: "100000-250000", label: "ARS 100,000 - 250,000" },
  { value: "250000-500000", label: "ARS 250,000 - 500,000" },
  { value: "500000-999999999", label: "Más de ARS 500,000" },
];

const urgencyLevels = [
  { value: "all", label: "Todas las urgencias" },
  { value: "urgent", label: "Urgente" },
  { value: "high", label: "Alta" },
  { value: "medium", label: "Media" },
  { value: "low", label: "Baja" },
];

const sortOptions = [
  { value: "newest", label: "Más recientes" },
  { value: "budget_high", label: "Mayor presupuesto" },
  { value: "budget_low", label: "Menor presupuesto" },
  { value: "bids_count", label: "Menos propuestas" },
];

export function CaseFiltersPanel({
  filters,
  onFiltersChange,
  onClose,
}: CaseFiltersPanelProps) {
  const handleFilterChange = (key: keyof CaseFilters, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  const clearFilters = () => {
    onFiltersChange({
      type: "all",
      location: "all",
      budgetRange: "all",
      urgencyLevel: "all",
      sortBy: "newest",
    });
  };

  return (
    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Filtros de Búsqueda
        </h3>
        <button
          onClick={onClose}
          className="text-gray-400 cursor-pointer dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
        >
          <XMarkIcon className="h-5 w-5" />
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        {/* Case Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Tipo de Caso
          </label>
          <select
            value={filters.type}
            onChange={(e) => handleFilterChange("type", e.target.value)}
            className="w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer"
          >
            {caseTypes.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>

        {/* Location */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Ubicación
          </label>
          <select
            value={filters.location}
            onChange={(e) => handleFilterChange("location", e.target.value)}
            className="w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer"
          >
            {locations.map((location) => (
              <option key={location.value} value={location.value}>
                {location.label}
              </option>
            ))}
          </select>
        </div>

        {/* Budget Range */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Presupuesto
          </label>
          <select
            value={filters.budgetRange}
            onChange={(e) => handleFilterChange("budgetRange", e.target.value)}
            className="w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer"
          >
            {budgetRanges.map((range) => (
              <option key={range.value} value={range.value}>
                {range.label}
              </option>
            ))}
          </select>
        </div>

        {/* Urgency Level */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Urgencia
          </label>
          <select
            value={filters.urgencyLevel}
            onChange={(e) => handleFilterChange("urgencyLevel", e.target.value)}
            className="w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer"
          >
            {urgencyLevels.map((level) => (
              <option key={level.value} value={level.value}>
                {level.label}
              </option>
            ))}
          </select>
        </div>

        {/* Sort By */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Ordenar por
          </label>
          <select
            value={filters.sortBy}
            onChange={(e) => handleFilterChange("sortBy", e.target.value)}
            className="w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer"
          >
            {sortOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="flex justify-between mt-6">
        <button
          onClick={clearFilters}
          className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer"
        >
          Limpiar Filtros
        </button>
        <button
          onClick={onClose}
          className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer"
        >
          Aplicar Filtros
        </button>
      </div>
    </div>
  );
}
