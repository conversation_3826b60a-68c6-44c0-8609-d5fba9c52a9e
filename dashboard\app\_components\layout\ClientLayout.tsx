"use client";

import { TopBar } from "./TopBar";

interface ClientLayoutProps {
  children: React.ReactNode;
}

export function ClientLayout({ children }: ClientLayoutProps) {
  return (
    <div className="flex flex-col h-screen">
      {/* Header principal */}
      <TopBar />
      
      {/* Contenido principal */}
      <main className="flex-1 overflow-auto p-6 bg-gray-50 dark:bg-gray-900">
        {children}
      </main>
    </div>
  );
}
