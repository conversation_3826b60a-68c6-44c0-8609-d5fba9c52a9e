'use client';

import { motion } from 'framer-motion';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

interface RevenueData {
  month: string;
  revenue: number;
  expenses: number;
  profit: number;
}

interface RevenueChartProps {
  data: RevenueData[];
  detailed?: boolean;
}

export function RevenueChart({ data, detailed = false }: RevenueChartProps) {
  const maxValue = Math.max(...data.map(d => Math.max(d.revenue, d.expenses, d.profit)));
  const chartHeight = detailed ? 400 : 300;

  const getBarHeight = (value: number) => {
    return (value / maxValue) * (chartHeight - 100);
  };

  const formatCurrency = (value: number) => {
    return `ARS ${(value / 1000).toFixed(0)}K`;
  };

  const formatMonth = (monthStr: string) => {
    const date = new Date(monthStr + '-01');
    return format(date, 'MMM yyyy', { locale: es });
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">
          {detailed ? 'Análisis Financiero Detallado' : 'Ingresos Mensuales'}
        </h3>
        {detailed && (
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded"></div>
              <span className="text-gray-600">Ingresos</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded"></div>
              <span className="text-gray-600">Gastos</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded"></div>
              <span className="text-gray-600">Ganancia</span>
            </div>
          </div>
        )}
      </div>

      <div className="relative" style={{ height: chartHeight }}>
        {/* Y-axis labels */}
        <div className="absolute left-0 top-0 bottom-0 w-16 flex flex-col justify-between text-xs text-gray-500">
          {[0, 0.25, 0.5, 0.75, 1].map((ratio) => (
            <div key={ratio} className="text-right pr-2">
              {formatCurrency(maxValue * ratio)}
            </div>
          ))}
        </div>

        {/* Chart area */}
        <div className="ml-16 mr-4 h-full relative">
          {/* Grid lines */}
          {[0, 0.25, 0.5, 0.75, 1].map((ratio) => (
            <div
              key={ratio}
              className="absolute left-0 right-0 border-t border-gray-100"
              style={{ bottom: `${ratio * (chartHeight - 100)}px` }}
            />
          ))}

          {/* Bars */}
          <div className="flex items-end justify-between h-full pb-12">
            {data.map((item, index) => (
              <motion.div
                key={item.month}
                initial={{ height: 0 }}
                animate={{ height: 'auto' }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="flex-1 flex items-end justify-center space-x-1 max-w-20"
              >
                {detailed ? (
                  <>
                    {/* Revenue bar */}
                    <div
                      className="bg-blue-500 rounded-t w-4 relative group"
                      style={{ height: getBarHeight(item.revenue) }}
                    >
                      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        {formatCurrency(item.revenue)}
                      </div>
                    </div>
                    {/* Expenses bar */}
                    <div
                      className="bg-red-500 rounded-t w-4 relative group"
                      style={{ height: getBarHeight(item.expenses) }}
                    >
                      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        {formatCurrency(item.expenses)}
                      </div>
                    </div>
                    {/* Profit bar */}
                    <div
                      className="bg-green-500 rounded-t w-4 relative group"
                      style={{ height: getBarHeight(item.profit) }}
                    >
                      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        {formatCurrency(item.profit)}
                      </div>
                    </div>
                  </>
                ) : (
                  <div
                    className="bg-blue-500 rounded-t w-8 relative group"
                    style={{ height: getBarHeight(item.revenue) }}
                  >
                    <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                      {formatCurrency(item.revenue)}
                    </div>
                  </div>
                )}
              </motion.div>
            ))}
          </div>

          {/* X-axis labels */}
          <div className="absolute bottom-0 left-0 right-0 flex justify-between text-xs text-gray-500">
            {data.map((item) => (
              <div key={item.month} className="flex-1 text-center">
                {formatMonth(item.month)}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Summary stats for detailed view */}
      {detailed && (
        <div className="mt-6 grid grid-cols-3 gap-4 pt-6 border-t border-gray-200">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {formatCurrency(data.reduce((sum, item) => sum + item.revenue, 0))}
            </div>
            <div className="text-sm text-gray-600">Ingresos Totales</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {formatCurrency(data.reduce((sum, item) => sum + item.expenses, 0))}
            </div>
            <div className="text-sm text-gray-600">Gastos Totales</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(data.reduce((sum, item) => sum + item.profit, 0))}
            </div>
            <div className="text-sm text-gray-600">Ganancia Total</div>
          </div>
        </div>
      )}
    </div>
  );
}
