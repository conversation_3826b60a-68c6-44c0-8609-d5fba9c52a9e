"use client";

import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  UserIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  SunIcon,
  MoonIcon,
  ComputerDesktopIcon,
  BellIcon,
  DocumentTextIcon,
  ChartBarIcon,
} from "@heroicons/react/24/outline";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { useTheme } from "../../_contexts/ThemeContext";
import config from "../../../public/data/config.json";

interface User {
  name: string;
  email: string;
  avatar?: string;
  role: string;
}

interface AvatarDropdownProps {
  user: User;
}

export function AvatarDropdown({ user }: AvatarDropdownProps) {
  const router = useRouter();
  const { theme, setTheme, resolvedTheme } = useTheme();

  // Navigation handlers
  const handleNavigation = (path: string) => {
    router.push(path);
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const themeOptions = [
    { value: "light", label: "Claro", icon: SunIcon },
    { value: "dark", label: "Oscuro", icon: MoonIcon },
    { value: "system", label: "Sistema", icon: ComputerDesktopIcon },
  ];

  const handleLogout = () => {
    // In a real app, this would handle logout logic
    console.log("Logging out...");
    // Clear any stored auth tokens
    localStorage.removeItem("authToken");
    // Redirect to login page
    window.location.href = "/login";
  };

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger asChild>
        <button className="flex items-center cursor-pointer space-x-3 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900">
          {/* Avatar */}
          <div className="relative">
            {user.avatar ? (
              <Image
                src={user.avatar}
                alt={user.name}
                width={32}
                height={32}
                className="w-8 h-8 rounded-full object-cover"
              />
            ) : (
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                {getInitials(user.name)}
              </div>
            )}
            {/* Online indicator */}
            <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white dark:border-gray-900 rounded-full"></div>
          </div>

          {/* User info */}
          <div className="hidden md:block text-left">
            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {user.name}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {user.role}
            </div>
          </div>
        </button>
      </DropdownMenu.Trigger>

      <DropdownMenu.Portal>
        <DropdownMenu.Content
          className="min-w-56 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-1 z-50"
          sideOffset={5}
          align="end"
        >
          {/* User Info Header */}
          <div className="px-3 py-2 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              {user.avatar ? (
                <Image
                  src={user.avatar}
                  alt={user.name}
                  width={40}
                  height={40}
                  className="w-10 h-10 rounded-full object-cover"
                />
              ) : (
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                  {getInitials(user.name)}
                </div>
              )}
              <div>
                <div className="font-medium text-gray-900 dark:text-gray-100">
                  {user.name}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {user.email}
                </div>
              </div>
            </div>
          </div>

          {/* Menu Items */}
          <div className="py-1">
            <DropdownMenu.Item
              className="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md cursor-pointer focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700"
              onClick={() => handleNavigation("/dashboard/profile")}
            >
              <UserIcon className="h-4 w-4 mr-3" />
              Mi Perfil
            </DropdownMenu.Item>

            <DropdownMenu.Item className="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md cursor-pointer focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700">
              <BellIcon className="h-4 w-4 mr-3" />
              Notificaciones
            </DropdownMenu.Item>

            <DropdownMenu.Item
              className="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md cursor-pointer focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700"
              onClick={() => handleNavigation("/dashboard/templates")}
            >
              <DocumentTextIcon className="h-4 w-4 mr-3" />
              Plantillas
            </DropdownMenu.Item>

            {/* Analíticas - Solo mostrar si el feature flag está habilitado */}
            {config.featureFlags.navigation.analyticsMenu.enabled && (
              <DropdownMenu.Item
                className="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md cursor-pointer focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700"
                onClick={() => handleNavigation("/dashboard/analytics")}
              >
                <ChartBarIcon className="h-4 w-4 mr-3" />
                Analíticas
              </DropdownMenu.Item>
            )}
          </div>

          <DropdownMenu.Separator className="h-px bg-gray-200 dark:bg-gray-700 my-1" />

          {/* Theme Selector */}
          <DropdownMenu.Sub>
            <DropdownMenu.SubTrigger
              className="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md cursor-pointer focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700"
              data-testid="theme-submenu-trigger"
            >
              {resolvedTheme === "dark" ? (
                <MoonIcon className="h-4 w-4 mr-3" />
              ) : (
                <SunIcon className="h-4 w-4 mr-3" />
              )}
              Tema
              <div className="ml-auto text-xs text-gray-500 dark:text-gray-400">
                {theme === "system"
                  ? "Sistema"
                  : theme === "dark"
                  ? "Oscuro"
                  : "Claro"}
              </div>
            </DropdownMenu.SubTrigger>
            <DropdownMenu.Portal>
              <DropdownMenu.SubContent
                className="min-w-32 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-1 z-50"
                sideOffset={2}
                alignOffset={-5}
              >
                {themeOptions.map((option) => (
                  <DropdownMenu.Item
                    key={option.value}
                    className="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md cursor-pointer focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700"
                    onClick={() => {
                      setTheme(option.value as "light" | "dark" | "system");
                    }}
                    data-testid={`theme-option-${option.value}`}
                  >
                    <option.icon className="h-4 w-4 mr-3" />
                    {option.label}
                    {theme === option.value && (
                      <div className="ml-auto w-2 h-2 bg-blue-500 rounded-full"></div>
                    )}
                  </DropdownMenu.Item>
                ))}
              </DropdownMenu.SubContent>
            </DropdownMenu.Portal>
          </DropdownMenu.Sub>

          <DropdownMenu.Item
            className="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md cursor-pointer focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700"
            onClick={() => handleNavigation("/dashboard/settings")}
          >
            <Cog6ToothIcon className="h-4 w-4 mr-3" />
            Configuración
          </DropdownMenu.Item>

          <DropdownMenu.Separator className="h-px bg-gray-200 dark:bg-gray-700 my-1" />

          <DropdownMenu.Item
            className="flex items-center px-3 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md cursor-pointer focus:outline-none focus:bg-red-50 dark:focus:bg-red-900/20"
            onClick={handleLogout}
          >
            <ArrowRightOnRectangleIcon className="h-4 w-4 mr-3" />
            Cerrar Sesión
          </DropdownMenu.Item>
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
}
