"use client";

import { useState, useEffect } from "react";

import * as Tabs from "@radix-ui/react-tabs";
import {
  ChartBarIcon,
  ClockIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  ArrowTrendingUpIcon,
  DocumentChartBarIcon,
} from "@heroicons/react/24/outline";
import { AnalyticsData } from "../../_lib/types";
import { OverviewCards } from "./OverviewCards";
import { RevenueChart } from "./RevenueChart";
import { CasesChart } from "./CasesChart";
import { TimeTrackingChart } from "./TimeTrackingChart";
import { ClientAnalytics } from "./ClientAnalytics";
import { PerformanceMetrics } from "./PerformanceMetrics";

export function AnalyticsDashboard() {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");
  const [dateRange, setDateRange] = useState("last_6_months");

  useEffect(() => {
    const loadAnalytics = async () => {
      try {
        const response = await fetch("/data/analytics.json");
        const data = await response.json();
        setAnalyticsData(data);
      } catch (error) {
        console.error("Error loading analytics:", error);
      } finally {
        setLoading(false);
      }
    };

    loadAnalytics();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 dark:text-gray-400">
          Error al cargar los datos de analytics
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Date Range Selector */}
      <div className="flex justify-between items-center">
        <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
          {[
            { value: "last_30_days", label: "Últimos 30 días" },
            { value: "last_3_months", label: "Últimos 3 meses" },
            { value: "last_6_months", label: "Últimos 6 meses" },
            { value: "last_year", label: "Último año" },
          ].map((range) => (
            <button
              key={range.value}
              onClick={() => setDateRange(range.value)}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-colors cursor-pointer ${
                dateRange === range.value
                  ? "bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow-sm"
                  : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-600"
              }`}
            >
              {range.label}
            </button>
          ))}
        </div>

        <button className="inline-flex items-center px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white text-sm font-medium rounded-md hover:bg-blue-700 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900 transition-colors cursor-pointer">
          <DocumentChartBarIcon className="h-4 w-4 mr-2" />
          Exportar Reporte
        </button>
      </div>

      {/* Overview Cards */}
      <OverviewCards overview={analyticsData.overview} />

      {/* Tabs */}
      <Tabs.Root value={activeTab} onValueChange={setActiveTab}>
        <Tabs.List className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg overflow-x-auto">
          <Tabs.Trigger
            value="overview"
            className="flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 text-gray-600 dark:text-gray-300 whitespace-nowrap"
          >
            <ChartBarIcon className="h-4 w-4 mr-2" />
            Resumen
          </Tabs.Trigger>
          <Tabs.Trigger
            value="revenue"
            className="flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 text-gray-600 dark:text-gray-300 whitespace-nowrap"
          >
            <CurrencyDollarIcon className="h-4 w-4 mr-2" />
            Ingresos
          </Tabs.Trigger>
          <Tabs.Trigger
            value="cases"
            className="flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 text-gray-600 dark:text-gray-300 whitespace-nowrap"
          >
            <ChartBarIcon className="h-4 w-4 mr-2" />
            Casos
          </Tabs.Trigger>
          <Tabs.Trigger
            value="time"
            className="flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 text-gray-600 dark:text-gray-300 whitespace-nowrap"
          >
            <ClockIcon className="h-4 w-4 mr-2" />
            Tiempo
          </Tabs.Trigger>
          <Tabs.Trigger
            value="clients"
            className="flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 text-gray-600 dark:text-gray-300 whitespace-nowrap"
          >
            <UserGroupIcon className="h-4 w-4 mr-2" />
            Clientes
          </Tabs.Trigger>
          <Tabs.Trigger
            value="performance"
            className="flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 text-gray-600 dark:text-gray-300 whitespace-nowrap"
          >
            <ArrowTrendingUpIcon className="h-4 w-4 mr-2" />
            Rendimiento
          </Tabs.Trigger>
        </Tabs.List>

        <div className="mt-6">
          <Tabs.Content value="overview">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <RevenueChart
                data={analyticsData.financialMetrics.monthlyRevenue}
              />
              <CasesChart data={analyticsData.monthlyStats} />
            </div>
          </Tabs.Content>

          <Tabs.Content value="revenue">
            <div className="space-y-6">
              <RevenueChart
                data={analyticsData.financialMetrics.monthlyRevenue}
                detailed
              />
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                    Facturas Pendientes
                  </h3>
                  <div className="text-3xl font-bold text-orange-600 dark:text-orange-400">
                    ARS{" "}
                    {analyticsData.financialMetrics.outstandingInvoices.toLocaleString(
                      "es-AR"
                    )}
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                    Tiempo promedio de cobro:{" "}
                    {analyticsData.financialMetrics.averageCollectionTime} días
                  </p>
                </div>
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                    Margen de Ganancia
                  </h3>
                  <div className="text-3xl font-bold text-green-600 dark:text-green-400">
                    {analyticsData.financialMetrics.profitMargin}%
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                    Promedio de la industria: 65%
                  </p>
                </div>
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                    Valor Promedio por Caso
                  </h3>
                  <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                    ARS{" "}
                    {analyticsData.overview.averageCaseValue.toLocaleString(
                      "es-AR"
                    )}
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                    +12% vs mes anterior
                  </p>
                </div>
              </div>
            </div>
          </Tabs.Content>

          <Tabs.Content value="cases">
            <CasesChart
              data={analyticsData.monthlyStats}
              detailed
              casesByArea={analyticsData.casesByArea}
            />
          </Tabs.Content>

          <Tabs.Content value="time">
            <TimeTrackingChart data={analyticsData.timeTracking} />
          </Tabs.Content>

          <Tabs.Content value="clients">
            <ClientAnalytics data={analyticsData.clientMetrics} />
          </Tabs.Content>

          <Tabs.Content value="performance">
            <PerformanceMetrics data={analyticsData.performanceMetrics} />
          </Tabs.Content>
        </div>
      </Tabs.Root>
    </div>
  );
}
