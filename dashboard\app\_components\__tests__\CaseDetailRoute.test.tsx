import { describe, it, expect, vi, beforeEach } from "vitest";
import CaseDetail from "../../dashboard/[caseId]/page";
import { notFound } from "next/navigation";
import { readFile } from "fs/promises";
import { join } from "path";
import type { Case } from "../../_lib/types";

// Mock the notFound function
vi.mock("next/navigation", () => ({
  notFound: vi.fn(),
}));

// Mock the CaseDetailPage component
vi.mock("../../_components/cases/CaseDetailPage", () => ({
  CaseDetailPage: ({ case: caseData }: { case: Case }) => (
    <div data-testid="case-detail-page">
      <h1>{caseData.title}</h1>
      <p>{caseData.client}</p>
      <p>{caseData.description}</p>
    </div>
  ),
}));

// Mock the CaseDetailWrapper component
vi.mock("../../_components/cases/CaseDetailWrapper", () => ({
  CaseDetailWrapper: ({ caseId }: { caseId: string }) => (
    <div data-testid="case-detail-wrapper">
      <p>Case ID: {caseId}</p>
    </div>
  ),
}));

// Mock the DashboardTabs component
vi.mock("../../_components/layout/DashboardTabs", () => ({
  DashboardTabs: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="dashboard-tabs">
      {children}
    </div>
  ),
}));

// Mock fs/promises and path modules
vi.mock("fs/promises");
vi.mock("path");

describe("CaseDetail Dynamic Route", () => {
  const mockReadFile = vi.mocked(readFile);
  const mockJoin = vi.mocked(join);

  beforeEach(() => {
    vi.clearAllMocks();
    mockJoin.mockImplementation((...args) => args.join("/"));
  });

  it("should render case details when case exists", async () => {
    const mockCaseData = [
      {
        id: "c-001",
        title: "Despido sin causa – ACME S.A.",
        client: "Juan Pérez",
        description:
          "Caso de despido sin causa justa. El empleado trabajó durante 5 años en la empresa.",
        type: "Laboral",
        status: "new",
        progress: 0.2,
        createdAt: "2025-06-14T10:30:00-03:00",
        milestones: [],
        messages: [],
        documents: [],
        activities: [],
      },
    ];

    mockReadFile.mockResolvedValueOnce(JSON.stringify(mockCaseData));

    const params = Promise.resolve({ caseId: "c-001" });
    await CaseDetail({ params });

    expect(mockJoin).toHaveBeenCalledWith(
      process.cwd(),
      "public",
      "data",
      "cases.json"
    );
    expect(mockReadFile).toHaveBeenCalledWith(
      expect.stringContaining("public/data/cases.json"),
      "utf8"
    );
    expect(notFound).not.toHaveBeenCalled();
  });

  it("should render CaseDetailWrapper when case does not exist in static data", async () => {
    const mockCaseData = [
      {
        id: "c-002",
        title: "Other case",
        client: "Other client",
        description: "Other description",
        type: "Civil",
        status: "new",
        progress: 0.1,
        createdAt: "2025-06-14T10:30:00-03:00",
        milestones: [],
        messages: [],
        documents: [],
        activities: [],
      },
    ];

    mockReadFile.mockResolvedValueOnce(JSON.stringify(mockCaseData));

    const params = Promise.resolve({ caseId: "c-001" });
    const result = await CaseDetail({ params });

    // Should render DashboardTabs wrapping CaseDetailWrapper
    expect(result.type.name).toBe("DashboardTabs");
    expect(result.props.children.type.name).toBe("CaseDetailWrapper");
    expect(result.props.children.props.caseId).toBe("c-001");
    expect(notFound).not.toHaveBeenCalled();
  });

  it("should call notFound when file read fails", async () => {
    mockReadFile.mockRejectedValueOnce(new Error("File read failed"));

    const params = Promise.resolve({ caseId: "c-001" });
    await CaseDetail({ params });

    expect(notFound).toHaveBeenCalled();
  });

  it("should call notFound when JSON parsing fails", async () => {
    mockReadFile.mockResolvedValueOnce("invalid json");

    const params = Promise.resolve({ caseId: "c-001" });
    await CaseDetail({ params });

    expect(notFound).toHaveBeenCalled();
  });

  it("should read from correct file path", async () => {
    const mockCaseData = [
      {
        id: "c-001",
        title: "Test case",
        client: "Test client",
        description: "Test description",
        type: "Test",
        status: "new",
        progress: 0.1,
        createdAt: "2025-06-14T10:30:00-03:00",
        milestones: [],
        messages: [],
        documents: [],
        activities: [],
      },
    ];

    mockReadFile.mockResolvedValueOnce(JSON.stringify(mockCaseData));

    const params = Promise.resolve({ caseId: "c-001" });
    await CaseDetail({ params });

    expect(mockJoin).toHaveBeenCalledWith(
      process.cwd(),
      "public",
      "data",
      "cases.json"
    );
    expect(mockReadFile).toHaveBeenCalledWith(
      expect.stringContaining("public/data/cases.json"),
      "utf8"
    );
  });
});
