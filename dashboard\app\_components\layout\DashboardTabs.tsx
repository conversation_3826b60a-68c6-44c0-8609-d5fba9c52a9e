"use client";

import { useState, useEffect } from "react";
import { useSearchParams, useRouter, usePathname } from "next/navigation";
import * as Tabs from "@radix-ui/react-tabs";
import {
  BriefcaseIcon,
  MagnifyingGlassIcon,
  DocumentTextIcon,
} from "@heroicons/react/24/outline";

interface DashboardTabsProps {
  children?: React.ReactNode;
  defaultTab?: string;
}

export function DashboardTabs({ children, defaultTab = "my-cases" }: DashboardTabsProps) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const [activeTab, setActiveTab] = useState(defaultTab);

  // Determinar el tab activo basado en la ruta actual
  useEffect(() => {
    if (pathname.includes("/dashboard/") && pathname !== "/dashboard") {
      // Si estamos en una página de detalle, mantener el tab "my-cases"
      setActiveTab("my-cases");
    } else {
      // Si estamos en la página principal del dashboard, usar el tab de la URL
      const tabFromUrl = searchParams.get("tab");
      if (tabFromUrl && (tabFromUrl === "my-cases" || tabFromUrl === "available-cases" || tabFromUrl === "templates")) {
        setActiveTab(tabFromUrl);
      } else {
        setActiveTab("my-cases");
      }
    }
  }, [searchParams, pathname]);

  // Manejar cambio de tab y actualizar URL
  const handleTabChange = (newTab: string) => {
    setActiveTab(newTab);
    // Navegar a la página principal del dashboard con el tab seleccionado
    const newUrl = newTab === "my-cases" ? "/dashboard" : `/dashboard?tab=${newTab}`;
    router.push(newUrl);
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      <Tabs.Root value={activeTab} onValueChange={handleTabChange}>
        {/* Tabs list with responsive design */}
        <div className="w-full overflow-x-auto scrollbar-hide">
          <Tabs.List className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg min-w-fit w-full sm:w-auto">
            <Tabs.Trigger
              value="my-cases"
              className="flex items-center cursor-pointer whitespace-nowrap px-2 sm:px-4 py-2 text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md transition-colors hover:bg-gray-200 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 min-w-0 flex-shrink-0"
            >
              <BriefcaseIcon className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 flex-shrink-0" />
              <span className="hidden sm:inline">Mis Casos</span>
              <span className="sm:hidden">Casos</span>
            </Tabs.Trigger>
            <Tabs.Trigger
              value="available-cases"
              className="flex items-center cursor-pointer whitespace-nowrap px-2 sm:px-4 py-2 text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md transition-colors hover:bg-gray-200 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 min-w-0 flex-shrink-0"
            >
              <MagnifyingGlassIcon className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 flex-shrink-0" />
              <span className="hidden sm:inline">Casos Disponibles</span>
              <span className="sm:hidden">Disponibles</span>
            </Tabs.Trigger>
            <Tabs.Trigger
              value="templates"
              className="flex items-center cursor-pointer whitespace-nowrap px-2 sm:px-4 py-2 text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md transition-colors hover:bg-gray-200 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 min-w-0 flex-shrink-0"
            >
              <DocumentTextIcon className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 flex-shrink-0" />
              <span className="hidden sm:inline">Plantillas</span>
              <span className="sm:hidden">Docs</span>
            </Tabs.Trigger>
          </Tabs.List>
        </div>

        <div className="mt-4 sm:mt-6">
          {children}
        </div>
      </Tabs.Root>
    </div>
  );
}
