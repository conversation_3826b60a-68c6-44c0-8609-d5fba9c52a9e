"use client";

import { useState, useCallback } from "react";
import { useDropzone } from "react-dropzone";
import {
  XMarkIcon,
  CloudArrowUpIcon,
  DocumentIcon,
  CheckCircleIcon,
} from "@heroicons/react/24/outline";
import * as Dialog from "@radix-ui/react-dialog";
import { Template } from "../../_lib/types";

interface UploadTemplateModalProps {
  onClose: () => void;
  onUpload: (template: Template) => void;
}

const legalAreas = ["Laboral", "Civil", "Familia", "Penal", "Comercial"];
const documentTypes = ["Contratos", "Demandas", "Escritos", "Cartas Documento"];

export function UploadTemplateModal({
  onClose,
  onUpload,
}: UploadTemplateModalProps) {
  const [step, setStep] = useState<
    "upload" | "details" | "preview" | "success"
  >("upload");
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    legalArea: "Laboral",
    documentType: "Contratos",
    price: 0,
    tags: "",
    isFree: true,
  });

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      setUploadedFile(acceptedFiles[0]);
      setStep("details");
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "application/pdf": [".pdf"],
      "application/msword": [".doc"],
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        [".docx"],
    },
    maxSize: 10 * 1024 * 1024, // 10MB
    multiple: false,
  });

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setStep("preview");
  };

  const handlePublish = async () => {
    setIsUploading(true);

    // Simulate upload process
    setTimeout(() => {
      const newTemplate: Template = {
        id: `tpl-${crypto.randomUUID()}`,
        title: formData.title,
        description: formData.description,
        legalArea: formData.legalArea,
        documentType: formData.documentType,
        price: formData.isFree ? 0 : formData.price,
        rating: 0,
        downloadCount: 0,
        author: "Dr. Ana García", // Mock current user
        authorId: "current-user",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        tags: formData.tags
          .split(",")
          .map((tag) => tag.trim())
          .filter(Boolean),
        preview: "/templates/previews/default.jpg",
        fileUrl: `/templates/files/${uploadedFile?.name}`,
        featured: false,
        status: "under_review",
      };

      onUpload(newTemplate);
      setIsUploading(false);
      setStep("success");

      // Auto-close after success
      setTimeout(() => {
        onClose();
      }, 2000);
    }, 2000);
  };

  const renderStepContent = () => {
    switch (step) {
      case "upload":
        return (
          <div className="space-y-6">
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                isDragActive
                  ? "border-blue-400 bg-blue-50 dark:bg-blue-900/20"
                  : "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
              }`}
            >
              <input {...getInputProps()} />
              <CloudArrowUpIcon className="h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
              {isDragActive ? (
                <p className="text-sm text-blue-600 dark:text-blue-400">
                  Suelta el archivo aquí...
                </p>
              ) : (
                <div className="space-y-2">
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Arrastra tu plantilla aquí o haz clic para seleccionar
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    PDF, DOC, DOCX (máx. 10MB)
                  </p>
                </div>
              )}
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 dark:text-blue-200 mb-2">
                Requisitos para publicar:
              </h4>
              <ul className="text-sm text-blue-800 dark:text-blue-300 space-y-1">
                <li>• El documento debe ser original o de tu autoría</li>
                <li>• Debe cumplir con la legislación vigente</li>
                <li>• Formato profesional y bien estructurado</li>
                <li>• Sin errores ortográficos o gramaticales</li>
              </ul>
            </div>
          </div>
        );

      case "details":
        return (
          <form onSubmit={handleFormSubmit} className="space-y-4">
            <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <DocumentIcon className="h-5 w-5 text-gray-400 dark:text-gray-500" />
              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                {uploadedFile?.name}
              </span>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Título de la plantilla *
              </label>
              <input
                type="text"
                required
                value={formData.title}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, title: e.target.value }))
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Ej: Contrato de Trabajo Indefinido"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Descripción *
              </label>
              <textarea
                required
                rows={3}
                value={formData.description}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Describe qué incluye esta plantilla y para qué casos es útil..."
              />
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Área Legal *
                </label>
                <select
                  value={formData.legalArea}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      legalArea: e.target.value,
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer"
                >
                  {legalAreas.map((area) => (
                    <option key={area} value={area}>
                      {area}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Tipo de Documento *
                </label>
                <select
                  value={formData.documentType}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      documentType: e.target.value,
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent cursor-pointer"
                >
                  {documentTypes.map((type) => (
                    <option key={type} value={type}>
                      {type}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.isFree}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      isFree: e.target.checked,
                    }))
                  }
                  className="text-blue-600 focus:ring-blue-500 cursor-pointer"
                />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Plantilla gratuita
                </span>
              </label>
            </div>

            {!formData.isFree && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Precio (ARS)
                </label>
                <input
                  type="number"
                  min="100"
                  step="100"
                  value={formData.price}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      price: parseInt(e.target.value) || 0,
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="1000"
                />
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Etiquetas (separadas por comas)
              </label>
              <input
                type="text"
                value={formData.tags}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, tags: e.target.value }))
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="contrato, laboral, empleado"
              />
            </div>

            <div className="flex space-x-3 pt-4">
              <button
                type="button"
                onClick={() => setStep("upload")}
                className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer"
              >
                Volver
              </button>
              <button
                type="submit"
                className="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer"
              >
                Vista Previa
              </button>
            </div>
          </form>
        );

      case "preview":
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                Vista Previa de la Plantilla
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Revisa cómo se verá tu plantilla en el marketplace
              </p>
            </div>

            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-700">
              <div className="space-y-3">
                <div className="flex items-start justify-between">
                  <h4 className="font-semibold text-gray-900 dark:text-gray-100">
                    {formData.title}
                  </h4>
                  <span className="text-green-600 dark:text-green-400 font-semibold text-sm">
                    {formData.isFree
                      ? "Gratis"
                      : `ARS ${formData.price.toLocaleString("es-AR")}`}
                  </span>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  {formData.description}
                </p>
                <div className="flex items-center space-x-2">
                  <span className="text-xs font-medium px-2 py-1 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                    {formData.legalArea}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-600 px-2 py-1 rounded-full">
                    {formData.documentType}
                  </span>
                </div>
                {formData.tags && (
                  <div className="flex flex-wrap gap-1">
                    {formData.tags.split(",").map((tag, index) => (
                      <span
                        key={index}
                        className="text-xs px-2 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded"
                      >
                        {tag.trim()}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>

            <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                <strong>Nota:</strong> Tu plantilla será revisada por nuestro
                equipo antes de ser publicada. Este proceso puede tomar hasta 24
                horas.
              </p>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => setStep("details")}
                className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer"
              >
                Editar
              </button>
              <button
                onClick={handlePublish}
                disabled={isUploading}
                className="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors cursor-pointer"
              >
                {isUploading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Publicando...
                  </div>
                ) : (
                  "Publicar Plantilla"
                )}
              </button>
            </div>
          </div>
        );

      case "success":
        return (
          <div className="text-center space-y-6">
            <div className="text-green-600 dark:text-green-400">
              <CheckCircleIcon className="h-16 w-16 mx-auto mb-4" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                ¡Plantilla Enviada!
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Tu plantilla &quot;{formData.title}&quot; ha sido enviada para
                revisión. Te notificaremos cuando esté disponible en el
                marketplace.
              </p>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog.Root open onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 dark:bg-black/70 z-50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-xl z-50 w-full max-w-lg p-6 max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between mb-6">
            <Dialog.Title className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Subir Nueva Plantilla
            </Dialog.Title>
            {step !== "success" && (
              <Dialog.Close asChild>
                <button className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 cursor-pointer">
                  <XMarkIcon className="h-5 w-5" />
                </button>
              </Dialog.Close>
            )}
          </div>

          {renderStepContent()}
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
