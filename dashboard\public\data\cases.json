[{"id": "c-001", "title": "Despido sin causa – ACME S.A.", "type": "Laboral", "client": "<PERSON>", "status": "new", "progress": 0.2, "createdAt": "2025-06-14T10:30:00-03:00", "similarCount": 2, "description": "Caso de despido sin causa justa. El empleado trabajó durante 5 años en la empresa.", "priority": "high", "estimatedCost": "ARS 150,000 - 200,000", "complexityScore": 7, "riskAssessment": "medium", "successProbability": 85, "aiSummary": "Caso de despido sin causa con alta probabilidad de éxito. El empleado cuenta con 5 años de antigüedad y documentación completa. Se recomienda negociación inicial antes de proceder judicialmente.", "keyFacts": ["5 años de antigüedad en la empresa", "Despido sin causa expresada", "Documentación laboral completa", "Sin antecedentes disciplinarios"], "nextActions": ["Revisar cálculo de indemnización", "Preparar carta documento", "Evaluar negociación extrajudicial", "Solicitar certificado de trabajo"], "timeTracking": {"totalHours": 12.5, "billableHours": 10, "lastActivity": "2025-06-16T14:30:00-03:00"}, "milestones": [{"id": "m-001-1", "title": "Revisión de documentación laboral", "completed": true, "dueDate": "2025-06-16T00:00:00-03:00", "dependencies": []}, {"id": "m-001-2", "title": "Presentación de demanda", "completed": false, "dueDate": "2025-06-16T00:00:00-03:00", "dependencies": ["m-001-1"]}, {"id": "m-001-3", "title": "Audiencia de conciliación", "completed": false, "dueDate": "2025-07-15T00:00:00-03:00", "dependencies": ["m-001-2"]}], "messages": [{"id": "msg-001-1", "sender": "client", "content": "<PERSON><PERSON>, me despidieron ayer sin causa. ¿Qué puedo hacer?", "timestamp": "2025-06-14T10:30:00-03:00", "status": "read", "attachments": []}, {"id": "msg-001-2", "sender": "lawyer", "content": "<PERSON><PERSON>, lamento escuchar eso. Necesito que me envíes tu recibo de sueldo y el telegrama de despido para revisar tu caso.", "timestamp": "2025-06-14T11:15:00-03:00", "status": "read", "attachments": []}, {"id": "msg-001-3", "sender": "client", "content": "<PERSON><PERSON>, te los envío ahora mismo.", "timestamp": "2025-06-14T11:20:00-03:00", "status": "read", "attachments": [{"id": "att-001-1", "name": "Recibo_sueldo_mayo_2025.pdf", "type": "pdf", "size": "245KB"}]}, {"id": "msg-001-4", "sender": "client", "content": "<PERSON><PERSON>, ¿ya pudo revisar mi documentación? Estoy preocupado por los tiempos.", "timestamp": "2025-06-17T09:15:00-03:00", "status": "delivered", "attachments": []}, {"id": "msg-001-5", "sender": "client", "content": "También quería consultarle si necesita algún documento adicional para acelerar el proceso.", "timestamp": "2025-06-17T09:18:00-03:00", "status": "sent", "attachments": []}], "documents": [{"id": "doc-001-1", "name": "Recibo_sueldo_mayo_2025.pdf", "type": "pdf", "uploadedAt": "2025-06-14T11:25:00-03:00", "uploadedBy": "client"}, {"id": "doc-001-2", "name": "Telegrama_despido.pdf", "type": "pdf", "uploadedAt": "2025-06-14T11:26:00-03:00", "uploadedBy": "client"}, {"id": "doc-001-3", "name": "Calculo_indemnizacion_actualizado.pdf", "type": "pdf", "uploadedAt": "2025-06-17T15:30:00-03:00", "uploadedBy": "lawyer"}], "activities": [{"id": "act-001-1", "type": "status_change", "description": "<PERSON><PERSON><PERSON> c<PERSON>o", "timestamp": "2025-06-14T10:30:00-03:00", "user": "system"}, {"id": "act-001-2", "type": "document_upload", "description": "Cliente subió recibo de sueldo", "timestamp": "2025-06-14T11:25:00-03:00", "user": "client"}, {"id": "act-001-3", "type": "milestone_completed", "description": "Completada revisión de documentación", "timestamp": "2025-06-16T14:30:00-03:00", "user": "lawyer"}, {"id": "act-001-4", "type": "message_sent", "description": "Cliente envió consulta sobre tiempos", "timestamp": "2025-06-17T09:15:00-03:00", "user": "client"}, {"id": "act-001-5", "type": "document_upload", "description": "Abogado revisó cálculo de indemnización", "timestamp": "2025-06-17T15:30:00-03:00", "user": "lawyer"}], "unreadMessagesCount": 2}, {"id": "c-002", "title": "Reclamo por daños en consorcio", "type": "Civil", "client": "<PERSON>", "status": "in_progress", "progress": 0.6, "createdAt": "2025-06-10T15:00:00-03:00", "similarCount": 0, "description": "Daños por filtración de agua desde el departamento superior. Afectación de muebles, pisos y pintura del living-comedor.", "priority": "medium", "estimatedCost": "ARS 60,000 - 90,000", "complexityScore": 4, "riskAssessment": "low", "successProbability": 85, "aiSummary": "Caso típico de daños por filtración en consorcio. Responsabilidad clara del propietario superior. Documentación fotográfica completa.", "keyFacts": ["Filtración desde departamento 4°B", "Daños en living-comedor de 3°B", "Propietario superior reconoce el problema", "Presupuestos de reparación obtenidos"], "nextActions": ["Solicitar peritaje técnico de daños", "Enviar carta documento al consorcio", "Recopilar presupuestos de reparación adicionales", "Evaluar mediación con propietario superior"], "milestones": [{"id": "m-002-1", "title": "Inspección técnica del daño", "completed": true, "dueDate": "2025-06-12T00:00:00-03:00"}, {"id": "m-002-2", "title": "Carta documento al consorcio", "completed": true, "dueDate": "2025-06-14T00:00:00-03:00"}, {"id": "m-002-3", "title": "Negociación extrajudicial", "completed": false, "dueDate": "2025-06-25T00:00:00-03:00"}], "messages": [{"id": "msg-002-1", "sender": "client", "content": "El agua sigue filtrando desde arriba y ya arruinó mis muebles.", "timestamp": "2025-06-10T15:00:00-03:00"}, {"id": "msg-002-2", "sender": "lawyer", "content": "María, vamos a solicitar una inspección técnica urgente. Mientras tanto, documenta todos los daños con fotos.", "timestamp": "2025-06-10T15:30:00-03:00"}], "documents": [{"id": "doc-002-1", "name": "Fotos_daños_living.zip", "type": "zip", "uploadedAt": "2025-06-10T16:00:00-03:00"}, {"id": "doc-002-2", "name": "Presupuesto_reparacion.pdf", "type": "pdf", "uploadedAt": "2025-06-12T09:00:00-03:00"}, {"id": "doc-002-3", "name": "Informe_tecnico_filtracion.pdf", "type": "pdf", "uploadedAt": "2025-06-13T14:30:00-03:00"}], "activities": [{"id": "act-002-1", "type": "status_change", "description": "<PERSON><PERSON><PERSON> c<PERSON>o", "timestamp": "2025-06-10T15:00:00-03:00", "user": "system"}, {"id": "act-002-2", "type": "document_upload", "description": "Cliente subió fotos de los daños", "timestamp": "2025-06-10T16:00:00-03:00", "user": "client"}, {"id": "act-002-3", "type": "milestone_completed", "description": "Completada inspección técnica", "timestamp": "2025-06-12T10:00:00-03:00", "user": "lawyer"}], "unreadMessagesCount": 1}, {"id": "c-003", "title": "<PERSON><PERSON><PERSON> por mutuo acuerdo", "type": "Familia", "client": "<PERSON>", "status": "closed", "progress": 1.0, "createdAt": "2025-05-15T09:00:00-03:00", "similarCount": 1, "description": "Divorcio consensuado con acuerdo de bienes y tenencia de menores. Matrimonio de 8 años con dos hijos menores.", "priority": "low", "estimatedCost": "ARS 45,000 - 60,000", "complexityScore": 3, "riskAssessment": "low", "successProbability": 100, "aiSummary": "Divorcio por mutuo acuerdo sin conflictos. Convenio regulador completo. Proceso administrativo exitoso.", "keyFacts": ["Matrimonio de 8 años", "<PERSON>s hijos menores (6 y 4 años)", "Acuerdo de tenencia compartida", "División equitativa de bienes"], "nextActions": ["Finalizar trámites registrales", "Obtener certificado de divorcio", "Actualizar documentación de menores", "Revisar cumplimiento del convenio"], "milestones": [{"id": "m-003-1", "title": "Redacción de convenio", "completed": true, "dueDate": "2025-05-20T00:00:00-03:00"}, {"id": "m-003-2", "title": "Presentación en juzgado", "completed": true, "dueDate": "2025-05-25T00:00:00-03:00"}, {"id": "m-003-3", "title": "Sentencia de divorcio", "completed": true, "dueDate": "2025-06-10T00:00:00-03:00"}], "messages": [{"id": "msg-003-1", "sender": "client", "content": "Queremos divorciarnos de común acuerdo. ¿Qué necesitamos?", "timestamp": "2025-05-15T09:00:00-03:00"}, {"id": "msg-003-2", "sender": "lawyer", "content": "Perfecto Carlos. Al ser de mutuo acuerdo, el proceso será más rápido. Necesito que ambos vengan a firmar el convenio.", "timestamp": "2025-05-15T09:30:00-03:00"}], "documents": [{"id": "doc-003-1", "name": "Convenio_divorcio.pdf", "type": "pdf", "uploadedAt": "2025-05-20T10:00:00-03:00"}, {"id": "doc-003-2", "name": "Sentencia_divorcio.pdf", "type": "pdf", "uploadedAt": "2025-06-10T14:00:00-03:00"}, {"id": "doc-003-3", "name": "Acta_matrimonio.pdf", "type": "pdf", "uploadedAt": "2025-05-16T09:00:00-03:00"}, {"id": "doc-003-4", "name": "Partidas_nacimiento_hijos.pdf", "type": "pdf", "uploadedAt": "2025-05-16T09:15:00-03:00"}], "activities": [{"id": "act-003-1", "type": "status_change", "description": "<PERSON><PERSON><PERSON> c<PERSON>o", "timestamp": "2025-05-15T09:00:00-03:00", "user": "system"}, {"id": "act-003-2", "type": "document_upload", "description": "Cliente subió documentación matrimonial", "timestamp": "2025-05-16T09:00:00-03:00", "user": "client"}, {"id": "act-003-3", "type": "milestone_completed", "description": "Convenio regulador firmado", "timestamp": "2025-05-20T10:00:00-03:00", "user": "lawyer"}, {"id": "act-003-4", "type": "status_change", "description": "Caso cerrado - Sentencia obtenida", "timestamp": "2025-06-10T14:00:00-03:00", "user": "lawyer"}]}, {"id": "c-004", "title": "Accidente de tránsito - Reclamo de seguro", "type": "Civil", "client": "<PERSON>", "status": "new", "progress": 0.1, "createdAt": "2025-06-16T14:20:00-03:00", "similarCount": 3, "description": "Colisión múltiple en Av. Corrientes. Cliente resultó con lesiones leves. Reclamo contra seguro de tercero responsable.", "priority": "high", "estimatedCost": "ARS 80,000 - 120,000", "complexityScore": 6, "riskAssessment": "medium", "successProbability": 75, "aiSummary": "Caso de accidente de tránsito con responsabilidad clara del tercero. Lesiones menores documentadas. Recomendable proceder con reclamo extrajudicial inicial.", "keyFacts": ["Colisión en intersección con semáforo", "Tercero pasó en rojo según testigos", "Lesiones cervicales leves documentadas", "Vehículo con daños totales"], "nextActions": ["Completar pericia médica de lesiones", "Solicitar informe policial completo", "Enviar carta documento a seguro", "Recopilar testimonios de testigos"], "milestones": [{"id": "m-004-1", "title": "Recopilación de documentación", "completed": false, "dueDate": "2025-06-20T00:00:00-03:00"}, {"id": "m-004-2", "title": "Pericia médica", "completed": false, "dueDate": "2025-06-25T00:00:00-03:00"}, {"id": "m-004-3", "title": "Carta documento a seguro", "completed": false, "dueDate": "2025-07-01T00:00:00-03:00"}], "messages": [{"id": "msg-004-1", "sender": "client", "content": "<PERSON><PERSON>, tuve un accidente ayer. El otro conductor se pasó el semáforo en rojo y me chocó. Tengo dolor de cuello y mi auto quedó destruido.", "timestamp": "2025-06-16T14:20:00-03:00"}, {"id": "msg-004-2", "sender": "lawyer", "content": "<PERSON>, lamento mucho lo ocurrido. <PERSON><PERSON>, ¿ya fuiste al médico? Necesito que me traigas el parte policial, fotos del accidente y toda la documentación del seguro.", "timestamp": "2025-06-16T14:45:00-03:00"}], "documents": [{"id": "doc-004-1", "name": "Parte_policial.pdf", "type": "pdf", "uploadedAt": "2025-06-16T15:00:00-03:00"}], "unreadMessagesCount": 0}, {"id": "c-005", "title": "Despido discriminatorio por embarazo", "type": "Laboral", "client": "<PERSON><PERSON>", "status": "in_progress", "progress": 0.4, "createdAt": "2025-06-05T11:30:00-03:00", "similarCount": 2, "description": "Despido sin causa durante licencia por maternidad. Posible discriminación por embarazo seg<PERSON> Le<PERSON> 23.592.", "priority": "high", "estimatedCost": "ARS 200,000 - 300,000", "complexityScore": 8, "riskAssessment": "low", "successProbability": 90, "aiSummary": "Caso sólido de discriminación por embarazo. Despido durante licencia por maternidad constituye presunción de discriminación. Alta probabilidad de éxito.", "keyFacts": ["Despido durante licencia por maternidad", "Comunicación vía WhatsApp sin formalidad", "Antecedentes de comentarios discriminatorios", "Testigos del ambiente laboral hostil"], "nextActions": ["Presentar demanda por discriminación", "Solicitar indemnización agravada", "Recopilar testimonios de compañeras", "Documentar historial de comentarios discriminatorios"], "milestones": [{"id": "m-005-1", "title": "Recopilación de pruebas", "completed": true, "dueDate": "2025-06-08T00:00:00-03:00"}, {"id": "m-005-2", "title": "Carta documento", "completed": true, "dueDate": "2025-06-12T00:00:00-03:00"}, {"id": "m-005-3", "title": "<PERSON><PERSON>a laboral", "completed": false, "dueDate": "2025-06-20T00:00:00-03:00"}, {"id": "m-005-4", "title": "Audiencia de conciliación", "completed": false, "dueDate": "2025-07-15T00:00:00-03:00"}], "messages": [{"id": "msg-005-1", "sender": "client", "content": "<PERSON><PERSON>, no puedo creer lo que pasó. Me echaron por WhatsApp mientras estoy de licencia por maternidad. Mi jefe siempre hacía comentarios sobre que las mujeres embarazadas 'no rinden igual'.", "timestamp": "2025-06-05T11:30:00-03:00"}, {"id": "msg-005-2", "sender": "lawyer", "content": "Lucía, esto es claramente discriminación por embarazo. Tenemos un caso muy sólido. ¿Conservaste esos mensajes de WhatsApp? También necesito que me consigas testigos de esos comentarios discriminatorios.", "timestamp": "2025-06-05T12:00:00-03:00"}, {"id": "msg-005-3", "sender": "client", "content": "S<PERSON>, tengo todo guardado. Mis compañeras también escucharon los comentarios. ¿Cuánto tiempo puede llevar esto?", "timestamp": "2025-06-05T12:15:00-03:00"}], "documents": [{"id": "doc-005-1", "name": "Capturas_WhatsApp_despido.pdf", "type": "pdf", "uploadedAt": "2025-06-05T13:00:00-03:00"}, {"id": "doc-005-2", "name": "Certificado_medico_embarazo.pdf", "type": "pdf", "uploadedAt": "2025-06-06T09:00:00-03:00"}, {"id": "doc-005-3", "name": "Re<PERSON><PERSON>_sueldo_ultimos_6_meses.pdf", "type": "pdf", "uploadedAt": "2025-06-07T10:30:00-03:00"}], "unreadMessagesCount": 1}, {"id": "c-006", "title": "Estafa inmobiliaria - Recupero de seña", "type": "Penal", "client": "<PERSON>", "status": "in_progress", "progress": 0.7, "createdAt": "2025-05-20T16:45:00-03:00", "similarCount": 1, "description": "Estafa en compraventa de departamento. Vendedor desapareció con la seña de $50.000 USD. Denuncia penal y reclamo civil.", "priority": "high", "estimatedCost": "ARS 150,000 - 250,000", "complexityScore": 9, "riskAssessment": "high", "successProbability": 60, "aiSummary": "Caso complejo de estafa inmobiliaria. Denuncia penal en curso. Recupero patrimonial depende de localización de bienes del imputado.", "keyFacts": ["Seña de USD 50.000 entregada en efectivo", "Documentación falsificada del inmueble", "Vendedor con antecedentes penales", "Inmueble con inhibición general"], "nextActions": ["Solicitar embargo preventivo de bienes", "Constituirse como querellante", "Recopilar pruebas documentales", "Investigar patrimonio del imputado"], "milestones": [{"id": "m-006-1", "title": "Denuncia penal", "completed": true, "dueDate": "2025-05-22T00:00:00-03:00"}, {"id": "m-006-2", "title": "Constitución como querellante", "completed": true, "dueDate": "2025-05-30T00:00:00-03:00"}, {"id": "m-006-3", "title": "Pedido de embargo preventivo", "completed": true, "dueDate": "2025-06-05T00:00:00-03:00"}, {"id": "m-006-4", "title": "Audiencia de indagatoria", "completed": false, "dueDate": "2025-06-25T00:00:00-03:00"}], "messages": [{"id": "msg-006-1", "sender": "client", "content": "<PERSON><PERSON>, me estafaron con la compra de un departamento. Entregué 50 mil dólares de seña y ahora el tipo desapareció. Los papeles eran falsos.", "timestamp": "2025-05-20T16:45:00-03:00"}, {"id": "msg-006-2", "sender": "lawyer", "content": "<PERSON>, esto es muy grave. Vamos a hacer la denuncia penal inmediatamente y también iniciar acciones civiles para recuperar tu dinero. ¿Tenés todos los documentos que te dio?", "timestamp": "2025-05-20T17:00:00-03:00"}], "documents": [{"id": "doc-006-1", "name": "Boleto_compraventa_falso.pdf", "type": "pdf", "uploadedAt": "2025-05-20T18:00:00-03:00"}, {"id": "doc-006-2", "name": "Recibo_seña_50k_USD.pdf", "type": "pdf", "uploadedAt": "2025-05-20T18:15:00-03:00"}, {"id": "doc-006-3", "name": "Denuncia_penal.pdf", "type": "pdf", "uploadedAt": "2025-05-22T10:00:00-03:00"}], "unreadMessagesCount": 0}, {"id": "c-007", "title": "Sucesión intestada - Hermanos", "type": "Familia", "client": "<PERSON> y <PERSON>", "status": "in_progress", "progress": 0.3, "createdAt": "2025-06-01T10:15:00-03:00", "similarCount": 4, "description": "Sucesión de padre fallecido sin testamento. Dos hermanos herederos. Incluye inmueble en CABA y cuenta bancaria.", "priority": "medium", "estimatedCost": "ARS 120,000 - 180,000", "complexityScore": 5, "riskAssessment": "low", "successProbability": 95, "aiSummary": "Sucesión simple entre hermanos sin conflictos. Bienes claramente identificados. Proceso administrativo estándar.", "keyFacts": ["Padre fallecido sin testamento", "<PERSON>s hijos únicos herederos", "Inmueble libre de deudas en CABA", "Cuenta bancaria con $2.500.000"], "nextActions": ["Completar inventario de bienes", "Solicitar avalúo del inmueble", "Tramitar partición hereditaria", "Liquidar cuentas ban<PERSON>"], "milestones": [{"id": "m-007-1", "title": "Declaratoria de herederos", "completed": true, "dueDate": "2025-06-10T00:00:00-03:00"}, {"id": "m-007-2", "title": "Inventario de bienes", "completed": false, "dueDate": "2025-06-20T00:00:00-03:00"}, {"id": "m-007-3", "title": "Avalúo in<PERSON>", "completed": false, "dueDate": "2025-06-30T00:00:00-03:00"}, {"id": "m-007-4", "title": "Partición de bienes", "completed": false, "dueDate": "2025-07-15T00:00:00-03:00"}], "messages": [{"id": "msg-007-1", "sender": "client", "content": "<PERSON><PERSON>, falleció nuestro padre y queremos hacer la sucesión. Somos dos hermanos y no hay conflictos entre nosotros. Dejó la casa y algo de dinero en el banco.", "timestamp": "2025-06-01T10:15:00-03:00"}, {"id": "msg-007-2", "sender": "lawyer", "content": "<PERSON>, <PERSON>, mis condolencias. Al no haber testamento, vamos a iniciar la declaratoria de herederos. Como son solo ustedes dos, el proceso será más sencillo. Necesito partidas de nacimiento y defunción.", "timestamp": "2025-06-01T10:30:00-03:00"}], "documents": [{"id": "doc-007-1", "name": "Partida_defuncion_padre.pdf", "type": "pdf", "uploadedAt": "2025-06-01T11:00:00-03:00"}, {"id": "doc-007-2", "name": "Escritura_inmueble_CABA.pdf", "type": "pdf", "uploadedAt": "2025-06-02T14:00:00-03:00"}], "unreadMessagesCount": 0}]