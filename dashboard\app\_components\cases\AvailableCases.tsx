"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  ClockIcon,
  MapPinIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from "@heroicons/react/24/outline";
import { formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";
import { AvailableCase, CaseFilters } from "../../_lib/types";
import { CaseFiltersPanel } from "./CaseFiltersPanel";
import { AvailableCaseDetailModal } from "../modals/AvailableCaseDetailModal";
import { useIsMounted } from "../../_lib/useIsomorphicDate";

export function AvailableCases() {
  const router = useRouter();
  const [availableCases, setAvailableCases] = useState<AvailableCase[]>([]);
  const [filteredCases, setFilteredCases] = useState<AvailableCase[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [selectedCase, setSelectedCase] = useState<AvailableCase | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [filters, setFilters] = useState<CaseFilters>({
    type: "all",
    location: "all",
    budgetRange: "all",
    urgencyLevel: "all",
    sortBy: "newest",
  });
  const isMounted = useIsMounted();

  // Load available cases
  useEffect(() => {
    const loadCases = async () => {
      try {
        const response = await fetch("/data/available-cases.json");
        const casesData = await response.json();
        setAvailableCases(casesData);
        setFilteredCases(casesData);
      } catch (error) {
        console.error("Error loading available cases:", error);
      } finally {
        setLoading(false);
      }
    };

    loadCases();
  }, []);

  // Apply filters and search
  useEffect(() => {
    let filtered = [...availableCases];

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (case_) =>
          case_.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          case_.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          case_.type.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Type filter
    if (filters.type !== "all") {
      filtered = filtered.filter((case_) => case_.type === filters.type);
    }

    // Location filter
    if (filters.location !== "all") {
      filtered = filtered.filter((case_) =>
        case_.clientLocation
          .toLowerCase()
          .includes(filters.location.toLowerCase())
      );
    }

    // Budget range filter
    if (filters.budgetRange !== "all") {
      const [min, max] = filters.budgetRange.split("-").map(Number);
      filtered = filtered.filter(
        (case_) => case_.estimatedValue >= min && case_.estimatedValue <= max
      );
    }

    // Urgency filter
    if (filters.urgencyLevel !== "all") {
      filtered = filtered.filter(
        (case_) => case_.urgencyLevel === filters.urgencyLevel
      );
    }

    // Sorting
    switch (filters.sortBy) {
      case "newest":
        filtered.sort(
          (a, b) =>
            new Date(b.postedAt).getTime() - new Date(a.postedAt).getTime()
        );
        break;
      case "budget_high":
        filtered.sort((a, b) => b.estimatedValue - a.estimatedValue);
        break;
      case "budget_low":
        filtered.sort((a, b) => a.estimatedValue - b.estimatedValue);
        break;
      case "bids_count":
        filtered.sort((a, b) => a.bidsCount - b.bidsCount);
        break;
    }

    setFilteredCases(filtered);
  }, [availableCases, searchTerm, filters]);

  const handleSendProposal = (caseId: string) => {
    router.push(`/dashboard/bid/${caseId}`);
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case "urgent":
        return "text-red-600 bg-red-100";
      case "high":
        return "text-orange-600 bg-orange-100";
      case "medium":
        return "text-yellow-600 bg-yellow-100";
      case "low":
        return "text-green-600 bg-green-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case "urgent":
        return ExclamationTriangleIcon;
      case "high":
        return ClockIcon;
      case "medium":
        return ClockIcon;
      case "low":
        return CheckCircleIcon;
      default:
        return ClockIcon;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Casos Disponibles
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {filteredCases.length} casos disponibles para aplicar
          </p>
        </div>

        {/* Quick stats */}
        <div className="flex items-center space-x-4 text-sm">
          <div className="flex items-center space-x-1 text-red-600 dark:text-red-400">
            <ExclamationTriangleIcon className="h-4 w-4" />
            <span>
              {availableCases.filter((c) => c.urgencyLevel === "urgent").length}{" "}
              urgentes
            </span>
          </div>
          <div className="flex items-center space-x-1 text-blue-600 dark:text-blue-400">
            <CurrencyDollarIcon className="h-4 w-4" />
            <span>
              ARS{" "}
              {Math.round(
                availableCases.reduce((sum, c) => sum + c.estimatedValue, 0) /
                  1000
              )}
              K total
            </span>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col lg:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Buscar casos por título, descripción o tipo..."
              className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
        <button
          onClick={() => setShowFilters(!showFilters)}
          className="inline-flex items-center cursor-pointer px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900 transition-colors"
        >
          <FunnelIcon className="h-4 w-4 mr-2" />
          Filtros
        </button>
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <CaseFiltersPanel
          filters={filters}
          onFiltersChange={setFilters}
          onClose={() => setShowFilters(false)}
        />
      )}

      {/* Cases Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredCases.map((case_, index) => {
          const UrgencyIcon = getUrgencyIcon(case_.urgencyLevel);
          const userBids = JSON.parse(localStorage.getItem("userBids") || "[]");
          const hasBid = userBids.some(
            (bid: { caseId: string }) => bid.caseId === case_.id
          );

          return (
            <motion.div
              key={case_.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow"
            >
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
                    {case_.title}
                  </h3>
                  <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-300">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                      {case_.type}
                    </span>
                    <div className="flex items-center space-x-1">
                      <MapPinIcon className="h-3 w-3" />
                      <span>{case_.clientLocation}</span>
                    </div>
                  </div>
                </div>
                <div
                  className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getUrgencyColor(
                    case_.urgencyLevel
                  )}`}
                >
                  <UrgencyIcon className="h-3 w-3 mr-1" />
                  {case_.urgencyLevel === "urgent"
                    ? "Urgente"
                    : case_.urgencyLevel === "high"
                    ? "Alta"
                    : case_.urgencyLevel === "medium"
                    ? "Media"
                    : "Baja"}
                </div>
              </div>

              {/* Description */}
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                {case_.description}
              </p>

              {/* Details */}
              <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                <div className="flex items-center space-x-2">
                  <CurrencyDollarIcon className="h-4 w-4 text-gray-400 dark:text-gray-500" />
                  <span className="text-gray-600 dark:text-gray-300">
                    {case_.budgetRange}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <UserGroupIcon className="h-4 w-4 text-gray-400 dark:text-gray-500" />
                  <span className="text-gray-600 dark:text-gray-300">
                    {case_.bidsCount} propuestas
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <ClockIcon className="h-4 w-4 text-gray-400 dark:text-gray-500" />
                  <span className="text-gray-600 dark:text-gray-300">
                    {isMounted
                      ? formatDistanceToNow(new Date(case_.postedAt), {
                          addSuffix: true,
                          locale: es,
                        })
                      : "Hace poco"}
                  </span>
                </div>
                <div className="text-gray-600 dark:text-gray-300">
                  Cliente: {case_.clientName}
                </div>
              </div>

              {/* Actions */}
              <div className="flex space-x-3">
                <button
                  onClick={() => handleSendProposal(case_.id)}
                  disabled={hasBid || case_.status !== "open"}
                  className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${
                    hasBid || case_.status !== "open"
                      ? "bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed"
                      : "bg-blue-600 dark:bg-blue-700 text-white hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 cursor-pointer"
                  }`}
                >
                  {hasBid ? "Propuesta Enviada" : "Enviar Propuesta"}
                </button>
                <button
                  onClick={() => {
                    setSelectedCase(case_);
                    setShowDetailModal(true);
                  }}
                  className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer">
                  Ver Detalles
                </button>
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* Empty State */}
      {filteredCases.length === 0 && (
        <div className="text-center py-12">
          <MagnifyingGlassIcon className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400 mb-2">
            No se encontraron casos que coincidan con los filtros
          </p>
          <button
            onClick={() => {
              setSearchTerm("");
              setFilters({
                type: "all",
                location: "all",
                budgetRange: "all",
                urgencyLevel: "all",
                sortBy: "newest",
              });
            }}
            className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm font-medium"
          >
            Limpiar filtros
          </button>
        </div>
      )}

      {/* Detail Modal */}
      {showDetailModal && selectedCase && (
        <AvailableCaseDetailModal
          case={selectedCase}
          onClose={() => {
            setShowDetailModal(false);
            setSelectedCase(null);
          }}
          onSendProposal={(caseData) => {
            setShowDetailModal(false);
            setSelectedCase(null);
            handleSendProposal(caseData.id);
          }}
        />
      )}
    </div>
  );
}
