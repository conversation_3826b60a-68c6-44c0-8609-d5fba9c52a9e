"use client";

import { createContext, useContext, useState, useEffect, ReactNode } from "react";

interface User {
  name: string;
  email: string;
  phone: string;
  location: string;
  role: string;
  specialization: string;
  experience: string;
  bio: string;
  avatar?: string;
}

interface UserContextType {
  user: User;
  updateUser: (updates: Partial<User>) => void;
}

const defaultUser: User = {
  name: "<PERSON><PERSON>",
  email: "<EMAIL>",
  phone: "+54 11 4567-8900",
  location: "Buenos Aires, Argentina",
  role: "Abogada Senior",
  specialization: "Derecho Laboral y Civil",
  experience: "15 años",
  bio: "Especialista en derecho laboral con amplia experiencia en casos complejos. Graduada de la Universidad de Buenos Aires con especialización en derecho civil y comercial.",
};

const UserContext = createContext<UserContextType | undefined>(undefined);

export function UserProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User>(defaultUser);

  // Load user data from localStorage on mount
  useEffect(() => {
    const savedUser = localStorage.getItem("userProfile");
    if (savedUser) {
      try {
        const parsedUser = JSON.parse(savedUser);
        setUser({ ...defaultUser, ...parsedUser });
      } catch (error) {
        console.error("Error parsing saved user data:", error);
      }
    }
  }, []);

  const updateUser = (updates: Partial<User>) => {
    const updatedUser = { ...user, ...updates };
    setUser(updatedUser);
    
    // Save to localStorage
    localStorage.setItem("userProfile", JSON.stringify(updatedUser));
  };

  return (
    <UserContext.Provider value={{ user, updateUser }}>
      {children}
    </UserContext.Provider>
  );
}

export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
}
