# Changelog

All notable changes to this project will be documented in this file.

## [2025-06-20] - AI Recommendations for All Cases

### Added

- **AI Recommendations**: Added comprehensive AI recommendation data for all cases in the system (c-002 through c-007)
- **Case-Specific Recommendations**: Each case now displays contextually relevant AI recommendations based on case type and legal area:
  - **Civil Cases** (c-002, c-004): Recommendations for damage assessment, expert reports, and insurance claims
  - **Family Cases** (c-003, c-007): Recommendations for inheritance procedures, divorce documentation, and asset valuation
  - **Labor Cases** (c-005): Recommendations for discrimination claims, aggravated compensation, and witness testimony
  - **Criminal Cases** (c-006): Recommendations for asset seizure, criminal complaints, and evidence collection
- **Dynamic Component Integration**: Updated CaseIntelligence component to use case-specific `nextActions` data instead of hardcoded recommendations
- **Comprehensive Test Coverage**: Added 12 new tests covering AI recommendations display across all case types and components
- **Spanish Legal Terminology**: All recommendations written in proper Spanish legal terminology appropriate for Argentine legal practice

### Changed

- **CaseIntelligence Component**: Modified to dynamically display AI recommendations from case data (`nextActions` field) with fallback to generic recommendations
- **Data Structure**: Enhanced cases.json with `nextActions` field containing 3-4 relevant AI recommendations per case
- **Test Infrastructure**: Updated test mocks to enable intelligence tab functionality for comprehensive testing

### Technical Details

- **Implementation**: Added `nextActions` field to all cases (c-002 through c-007) in `public/data/cases.json`
- **Component Logic**: Updated `CaseIntelligence.tsx` to use `caseData.nextActions || fallbackRecommendations`
- **Testing**: All 87 tests pass including new AI recommendation tests for both CaseIntelligence and CaseContextPanel components
- **Quality Assurance**: Build, TypeScript compilation, and ESLint checks all pass successfully
- **Research-Based**: Recommendations based on web research of Spanish legal AI practices and Argentine legal procedures

## [2025-06-20] - Dynamic Routes Production Fix

### Fixed

- **CRITICAL BUGFIX**: Fixed dynamic routes returning 404 errors in Vercel production deployment
- Routes with pattern `/dashboard/[caseId]` (e.g., `/dashboard/c-001`, `/dashboard/c-002`) now work correctly in production
- Replaced HTTP fetch approach with direct filesystem reading using `fs/promises.readFile()`
- Updated `getCase()` function in `/dashboard/[caseId]/page.tsx` to read from `public/data/cases.json` directly
- Added comprehensive test coverage for dynamic route functionality with 5 new test cases
- Verified fix works for all case IDs and properly handles non-existent cases with 404 responses
- Confirmed behavior consistency between local development and Vercel production environments

### Technical Details

- **Root Cause**: Server-side rendering was attempting to fetch static JSON data via HTTP requests, which failed in Vercel's production environment while working in local development
- **Solution**: Changed from `fetch(baseUrl + '/data/cases.json')` to `readFile(join(process.cwd(), 'public', 'data', 'cases.json'))`
- **Testing**: All 75 tests pass including new dynamic route tests
- **Verification**: Production deployment at https://abogados-dashboard-five.vercel.app/dashboard/c-001 now returns HTTP 200 with correct case data

## [2025-06-20] - Build Error Fixes

### Fixed

- **BUGFIX**: Removed unused `riskColors` and `complexityColors` constants from CaseContextPanel.tsx
- **BUGFIX**: Removed unused `Bars3Icon` import from Sidebar.tsx
- **BUGFIX**: Fixed TypeScript `any` type usage in config.ts by adding proper interface definition
- **BUGFIX**: Fixed unused parameter `feature` in config.ts by prefixing with underscore
- **BUGFIX**: Updated ESLint configuration to properly ignore underscore-prefixed unused variables and parameters
- **BUGFIX**: All ESLint errors resolved, build now passes successfully

## [2025-06-19] - Cronograma/Gantt Chart Removal

### Removed

- **BREAKING CHANGE**: Completely removed Cronograma (Gantt Chart) functionality from the application
- Removed GanttChart component and all related files
- Removed gantt tab from case detail page
- Removed GanttChart tests and references
- Updated navigation to exclude cronograma functionality

## [2025-01-17] - System-First Theme Switcher Implementation

### Added

- Comprehensive theme switcher with system-first approach
- System theme detection that automatically follows OS preference
- Manual theme override functionality (light/dark modes)
- Proper SSR/hydration handling to prevent theme flash
- Extensive test coverage for all theme scenarios (13 tests)
- Theme persistence across page reloads and sessions
- Real-time system theme change detection
- Inline script in HTML head for immediate theme application before React hydration

### Changed

- Default theme behavior now starts with "system" mode instead of hardcoded dark
- Theme context properly detects and respects OS theme preference on initial load
- Removed hardcoded "dark" class from layout.tsx for dynamic theme application
- Enhanced theme detection logic with fallbacks for older browsers
- Improved theme state management with hydration-safe implementation

### Fixed

- Theme switcher now correctly defaults to system mode as required
- System theme changes are properly detected and applied in real-time
- Manual theme selection (dark/light) correctly overrides system preference
- Theme persistence works correctly across browser sessions
- Eliminated flash of wrong theme during page load
- All theme-related tests now pass with comprehensive coverage
- **CRITICAL FIX**: Theme switcher CSS classes now properly applied to document.documentElement
- **CRITICAL FIX**: Resolved SSR/hydration mismatch preventing visual theme changes
- **CRITICAL FIX**: Theme switching now works immediately in browser with visible changes

## [2025-01-17] - Component Organization & Logo Addition

### Added

- Professional SVG logo featuring scales of justice with X-Legal branding
- Logo displays in sidebar header with responsive design (full logo when expanded, compact when collapsed)
- Logo supports both light and dark themes with appropriate color schemes

### Changed

- Reorganized components into logical subfolders for better maintainability:
  - `layout/` - Navigation and UI structure components (Sidebar, TopBar, AvatarDropdown, NotificationDropdown)
  - `cases/` - Case management components (CaseList, CaseDetailPage, AvailableCases, etc.)
  - `templates/` - Template marketplace components (TemplatesMarketplace, TemplateCard, etc.)
  - `analytics/` - Charts and metrics components (AnalyticsDashboard, CasesChart, etc.)
  - `ui/` - Reusable UI components (Chat, DocumentUpload)
  - `modals/` - Modal dialog components (BidModal, PurchaseModal, UploadTemplateModal)
- Updated all import statements across the application to reflect new component structure
- Fixed test imports to reference components from their new organized locations

### Fixed

- Component organization improves code maintainability and developer experience
- All imports correctly updated to prevent compilation errors
- Application compiles and runs without errors after reorganization

## Previous Changes

### [2025-01-17] - Dark Theme & Testing Improvements

- Fixed dark theme implementation across all components
- Set dark theme as default application theme
- Replaced Jest with Vitest for better performance and modern testing
- Added comprehensive test coverage using @testing-library/react
- Fixed notification popup functionality
- Added realistic mock data for legal cases c-002 and c-003
- Enhanced case context panels with proper data display

### [2025-01-17] - Initial Setup

- Initial dashboard application setup
- Basic component structure
- Theme system implementation
- Case management functionality
- Template marketplace features
- Analytics dashboard
