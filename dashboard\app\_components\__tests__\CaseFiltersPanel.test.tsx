import { describe, it, expect, beforeEach, vi } from "vitest";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { CaseFiltersPanel } from "../cases/CaseFiltersPanel";
import { ThemeProvider } from "../../_contexts/ThemeContext";
import { CaseFilters } from "../../_lib/types";

// Mock matchMedia for theme detection
const mockMatchMedia = vi.fn().mockImplementation((query: string) => ({
  matches: query === "(prefers-color-scheme: dark)",
  media: query,
  onchange: null,
  addListener: vi.fn(),
  removeListener: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
}));

window.matchMedia = mockMatchMedia;

const defaultFilters: CaseFilters = {
  type: "all",
  location: "all",
  budgetRange: "all",
  urgencyLevel: "all",
  sortBy: "newest",
};

interface TestAppProps {
  filters?: CaseFilters;
  onFiltersChange?: (filters: CaseFilters) => void;
  onClose?: () => void;
  initialTheme?: "light" | "dark" | "system";
}

function TestApp({
  filters = defaultFilters,
  onFiltersChange = vi.fn(),
  onClose = vi.fn(),
  initialTheme = "light",
}: TestAppProps) {
  // Set initial theme in localStorage before rendering
  if (typeof window !== "undefined") {
    localStorage.setItem("theme", initialTheme);
  }

  return (
    <ThemeProvider>
      <CaseFiltersPanel
        filters={filters}
        onFiltersChange={onFiltersChange}
        onClose={onClose}
      />
    </ThemeProvider>
  );
}

describe("CaseFiltersPanel", () => {
  beforeEach(() => {
    localStorage.clear();
    document.documentElement.className = "";
    vi.clearAllMocks();
  });

  describe("Basic Functionality", () => {
    it("should render all filter sections", () => {
      render(<TestApp />);

      expect(screen.getByText("Filtros de Búsqueda")).toBeInTheDocument();
      expect(screen.getByText("Tipo de Caso")).toBeInTheDocument();
      expect(screen.getByText("Ubicación")).toBeInTheDocument();
      expect(screen.getByText("Presupuesto")).toBeInTheDocument();
      expect(screen.getByText("Urgencia")).toBeInTheDocument();
      expect(screen.getByText("Ordenar por")).toBeInTheDocument();
    });

    it("should call onClose when close button is clicked", async () => {
      const onClose = vi.fn();
      const user = userEvent.setup();

      render(<TestApp onClose={onClose} />);

      // Find the close button by its icon (XMarkIcon)
      const closeButton = screen.getByRole("button", { name: "" });
      await user.click(closeButton);

      expect(onClose).toHaveBeenCalledOnce();
    });

    it("should call onFiltersChange when filter values change", async () => {
      const onFiltersChange = vi.fn();
      const user = userEvent.setup();

      render(<TestApp onFiltersChange={onFiltersChange} />);

      const typeSelect = screen.getByDisplayValue("Todos los tipos");
      await user.selectOptions(typeSelect, "Laboral");

      expect(onFiltersChange).toHaveBeenCalledWith({
        ...defaultFilters,
        type: "Laboral",
      });
    });

    it("should clear all filters when clear button is clicked", async () => {
      const onFiltersChange = vi.fn();
      const user = userEvent.setup();

      const modifiedFilters: CaseFilters = {
        type: "Laboral",
        location: "CABA",
        budgetRange: "100000-250000",
        urgencyLevel: "urgent",
        sortBy: "budget_high",
      };

      render(
        <TestApp filters={modifiedFilters} onFiltersChange={onFiltersChange} />
      );

      const clearButton = screen.getByText("Limpiar Filtros");
      await user.click(clearButton);

      expect(onFiltersChange).toHaveBeenCalledWith(defaultFilters);
    });
  });

  describe("Dark Theme Support", () => {
    it("should apply dark theme classes when dark mode is active", async () => {
      // Set up dark theme
      mockMatchMedia.mockImplementation((query: string) => ({
        matches: query === "(prefers-color-scheme: dark)",
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }));

      render(<TestApp initialTheme="dark" />);

      await waitFor(() => {
        expect(document.documentElement.classList.contains("dark")).toBe(true);
      });

      // Check main container has dark theme classes
      const mainContainer = screen
        .getByText("Filtros de Búsqueda")
        .closest("div")?.parentElement; // Get the actual main container, not the header div
      expect(mainContainer).toHaveClass("dark:bg-gray-800");
      expect(mainContainer).toHaveClass("dark:border-gray-700");
    });

    it("should apply dark theme classes to title and labels", async () => {
      render(<TestApp initialTheme="dark" />);

      await waitFor(() => {
        expect(document.documentElement.classList.contains("dark")).toBe(true);
      });

      // Check title has dark theme classes
      const title = screen.getByText("Filtros de Búsqueda");
      expect(title).toHaveClass("dark:text-gray-100");

      // Check labels have dark theme classes
      const typeLabel = screen.getByText("Tipo de Caso");
      expect(typeLabel).toHaveClass("dark:text-gray-300");
    });

    it("should apply dark theme classes to select elements", async () => {
      render(<TestApp initialTheme="dark" />);

      await waitFor(() => {
        expect(document.documentElement.classList.contains("dark")).toBe(true);
      });

      // Check select elements have dark theme classes
      const typeSelect = screen.getByDisplayValue("Todos los tipos");
      expect(typeSelect).toHaveClass("dark:bg-gray-700");
      expect(typeSelect).toHaveClass("dark:border-gray-600");
      expect(typeSelect).toHaveClass("dark:text-gray-100");
    });

    it("should apply dark theme classes to buttons", async () => {
      render(<TestApp initialTheme="dark" />);

      await waitFor(() => {
        expect(document.documentElement.classList.contains("dark")).toBe(true);
      });

      // Check clear button has dark theme classes
      const clearButton = screen.getByText("Limpiar Filtros");
      expect(clearButton).toHaveClass("dark:bg-gray-700");
      expect(clearButton).toHaveClass("dark:border-gray-600");
      expect(clearButton).toHaveClass("dark:text-gray-300");
      expect(clearButton).toHaveClass("dark:hover:bg-gray-600");

      // Check apply button has dark theme classes (should remain blue but with dark focus ring offset)
      const applyButton = screen.getByText("Aplicar Filtros");
      expect(applyButton).toHaveClass("dark:focus:ring-offset-gray-800");
    });

    it("should apply dark theme classes to close button", async () => {
      render(<TestApp initialTheme="dark" />);

      await waitFor(() => {
        expect(document.documentElement.classList.contains("dark")).toBe(true);
      });

      // Check close button has dark theme classes
      const closeButton = screen.getByRole("button", { name: "" });
      expect(closeButton).toHaveClass("dark:text-gray-500");
      expect(closeButton).toHaveClass("dark:hover:text-gray-300");
    });

    it("should maintain light theme classes when light mode is active", async () => {
      // Explicitly set light theme and reset dark class
      localStorage.setItem("theme", "light");
      document.documentElement.classList.remove("dark");

      // Mock system preference for light mode
      mockMatchMedia.mockImplementation((query: string) => ({
        matches: false, // System prefers light
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }));

      render(<TestApp initialTheme="light" />);

      await waitFor(() => {
        expect(document.documentElement.classList.contains("dark")).toBe(false);
      });

      // Check main container has light theme classes
      const mainContainer = screen
        .getByText("Filtros de Búsqueda")
        .closest("div")?.parentElement;
      expect(mainContainer).toHaveClass("bg-white");
      expect(mainContainer).toHaveClass("border-gray-200");
    });
  });
});
