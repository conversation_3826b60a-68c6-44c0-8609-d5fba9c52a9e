"use client";

import { motion } from "framer-motion";
import {
  ClockIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
} from "@heroicons/react/24/outline";
import { TimeTrackingData } from "../../_lib/types";

interface TimeTrackingChartProps {
  data: TimeTrackingData;
}

export function TimeTrackingChart({ data }: TimeTrackingChartProps) {
  const billabilityPercentage = (data.billableHours / data.totalHours) * 100;

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="bg-white rounded-lg border border-gray-200 p-6"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-blue-50 rounded-lg">
              <ClockIcon className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="font-semibold text-gray-900">Horas Totales</h3>
          </div>
          <div className="text-3xl font-bold text-gray-900">
            {data.totalHours}h
          </div>
          <p className="text-sm text-gray-600 mt-1">
            {data.billableHours}h facturables
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="bg-white rounded-lg border border-gray-200 p-6"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-green-50 rounded-lg">
              <ChartBarIcon className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="font-semibold text-gray-900">Facturabilidad</h3>
          </div>
          <div className="text-3xl font-bold text-green-600">
            {data.billabilityRate}%
          </div>
          <p className="text-sm text-gray-600 mt-1">Meta: 85%</p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-white rounded-lg border border-gray-200 p-6"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-purple-50 rounded-lg">
              <ClockIcon className="h-6 w-6 text-purple-600" />
            </div>
            <h3 className="font-semibold text-gray-900">Promedio por Caso</h3>
          </div>
          <div className="text-3xl font-bold text-purple-600">
            {data.averageHoursPerCase}h
          </div>
          <p className="text-sm text-gray-600 mt-1">Por caso activo</p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="bg-white rounded-lg border border-gray-200 p-6"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-yellow-50 rounded-lg">
              <CurrencyDollarIcon className="h-6 w-6 text-yellow-600" />
            </div>
            <h3 className="font-semibold text-gray-900">Tarifa por Hora</h3>
          </div>
          <div className="text-3xl font-bold text-yellow-600">
            ARS {data.hourlyRate.toLocaleString("es-AR")}
          </div>
          <p className="text-sm text-gray-600 mt-1">Tarifa estándar</p>
        </motion.div>
      </div>

      {/* Billability Progress */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="bg-white rounded-lg border border-gray-200 p-6"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-6">
          Análisis de Facturabilidad
        </h3>

        <div className="space-y-4">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Horas Facturables</span>
            <span className="font-medium">
              {data.billableHours}h de {data.totalHours}h
            </span>
          </div>

          <div className="w-full bg-gray-200 rounded-full h-4">
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: `${billabilityPercentage}%` }}
              transition={{ duration: 1, delay: 0.5 }}
              className="bg-gradient-to-r from-blue-500 to-green-500 h-4 rounded-full relative"
            >
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2 text-white text-xs font-medium">
                {billabilityPercentage.toFixed(1)}%
              </div>
            </motion.div>
          </div>

          <div className="grid grid-cols-2 gap-4 mt-6">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                ARS{" "}
                {(data.billableHours * data.hourlyRate).toLocaleString("es-AR")}
              </div>
              <div className="text-sm text-blue-800">Ingresos por Horas</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-600">
                {data.totalHours - data.billableHours}h
              </div>
              <div className="text-sm text-gray-600">Horas No Facturables</div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Top Time Spenders */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.5 }}
        className="bg-white rounded-lg border border-gray-200 p-6"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-6">
          Casos con Más Tiempo Invertido
        </h3>

        <div className="space-y-4">
          {data.topTimeSpenders.map((case_, index) => (
            <motion.div
              key={case_.caseId}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
              className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
            >
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">{case_.title}</h4>
                <p className="text-sm text-gray-600">ID: {case_.caseId}</p>
              </div>

              <div className="flex items-center space-x-6 text-sm">
                <div className="text-center">
                  <div className="font-semibold text-gray-900">
                    {case_.hours}h
                  </div>
                  <div className="text-gray-500">Total</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-blue-600">
                    {case_.billableHours}h
                  </div>
                  <div className="text-gray-500">Facturable</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-green-600">
                    ARS{" "}
                    {(case_.billableHours * data.hourlyRate).toLocaleString(
                      "es-AR"
                    )}
                  </div>
                  <div className="text-gray-500">Valor</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-purple-600">
                    {((case_.billableHours / case_.hours) * 100).toFixed(0)}%
                  </div>
                  <div className="text-gray-500">Eficiencia</div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </div>
  );
}
