"use client";

import { motion } from "framer-motion";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import {
  ClockIcon,
  CheckCircleIcon,
  StarIcon,
  ArrowTrendingUpIcon,
} from "@heroicons/react/24/outline";
import { PerformanceMetrics as PerformanceMetricsType } from "../../_lib/types";

interface PerformanceMetricsProps {
  data: PerformanceMetricsType;
}

export function PerformanceMetrics({ data }: PerformanceMetricsProps) {
  const formatMonth = (monthStr: string) => {
    const date = new Date(monthStr + "-01");
    return format(date, "MMM yyyy", { locale: es });
  };

  // const maxSatisfaction = 5; // Unused
  const chartHeight = 200;

  // const getSatisfactionHeight = (score: number) => {
  //   return (score / maxSatisfaction) * chartHeight;
  // };

  const areaColors = {
    Familia: "bg-pink-500",
    Laboral: "bg-blue-500",
    Civil: "bg-purple-500",
    Penal: "bg-red-500",
    Comercial: "bg-green-500",
  };

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="bg-white rounded-lg border border-gray-200 p-6"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-blue-50 rounded-lg">
              <ClockIcon className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="font-semibold text-gray-900">Tiempo de Respuesta</h3>
          </div>
          <div className="text-3xl font-bold text-blue-600">
            {data.averageResponseTime}h
          </div>
          <p className="text-sm text-gray-600 mt-1">Promedio inicial</p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="bg-white rounded-lg border border-gray-200 p-6"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-green-50 rounded-lg">
              <CheckCircleIcon className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="font-semibold text-gray-900">Resolución</h3>
          </div>
          <div className="text-3xl font-bold text-green-600">
            {data.caseResolutionTime} días
          </div>
          <p className="text-sm text-gray-600 mt-1">Tiempo promedio</p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-white rounded-lg border border-gray-200 p-6"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-yellow-50 rounded-lg">
              <StarIcon className="h-6 w-6 text-yellow-600" />
            </div>
            <h3 className="font-semibold text-gray-900">Satisfacción</h3>
          </div>
          <div className="text-3xl font-bold text-yellow-600">
            {data.clientSatisfactionTrend[
              data.clientSatisfactionTrend.length - 1
            ]?.score || 0}
          </div>
          <p className="text-sm text-gray-600 mt-1">Calificación actual</p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="bg-white rounded-lg border border-gray-200 p-6"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-purple-50 rounded-lg">
              <ArrowTrendingUpIcon className="h-6 w-6 text-purple-600" />
            </div>
            <h3 className="font-semibold text-gray-900">Tasa de Éxito</h3>
          </div>
          <div className="text-3xl font-bold text-purple-600">
            {Math.round(
              data.successRateByArea.reduce((sum, area) => sum + area.rate, 0) /
                data.successRateByArea.length
            )}
            %
          </div>
          <p className="text-sm text-gray-600 mt-1">Promedio general</p>
        </motion.div>
      </div>

      {/* Client Satisfaction Trend */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="bg-white rounded-lg border border-gray-200 p-6"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-6">
          Tendencia de Satisfacción del Cliente
        </h3>

        <div className="relative" style={{ height: chartHeight + 60 }}>
          {/* Y-axis labels */}
          <div className="absolute left-0 top-0 bottom-0 w-12 flex flex-col justify-between text-xs text-gray-500">
            {[1, 2, 3, 4, 5].reverse().map((score) => (
              <div key={score} className="text-right pr-2">
                {score}
              </div>
            ))}
          </div>

          {/* Chart area */}
          <div className="ml-12 mr-4 h-full relative">
            {/* Grid lines */}
            {[1, 2, 3, 4, 5].map((score) => (
              <div
                key={score}
                className="absolute left-0 right-0 border-t border-gray-100"
                style={{ bottom: `${((score - 1) / 4) * chartHeight + 40}px` }}
              />
            ))}

            {/* Line chart */}
            <svg className="absolute inset-0 w-full h-full">
              <motion.path
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{ duration: 2, delay: 0.5 }}
                d={`M ${data.clientSatisfactionTrend
                  .map((point, index) => {
                    const x =
                      (index / (data.clientSatisfactionTrend.length - 1)) * 100;
                    const y =
                      100 -
                      ((point.score - 1) / 4) *
                        (chartHeight / (chartHeight + 40)) *
                        100;
                    return `${index === 0 ? "M" : "L"} ${x}% ${y}%`;
                  })
                  .join(" ")}`}
                stroke="#3B82F6"
                strokeWidth="3"
                fill="none"
                className="drop-shadow-sm"
              />

              {/* Data points */}
              {data.clientSatisfactionTrend.map((point, index) => (
                <motion.circle
                  key={point.month}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.3, delay: 0.7 + index * 0.1 }}
                  cx={`${
                    (index / (data.clientSatisfactionTrend.length - 1)) * 100
                  }%`}
                  cy={`${
                    100 -
                    ((point.score - 1) / 4) *
                      (chartHeight / (chartHeight + 40)) *
                      100
                  }%`}
                  r="6"
                  fill="#3B82F6"
                  className="drop-shadow-sm"
                />
              ))}
            </svg>

            {/* X-axis labels */}
            <div className="absolute bottom-0 left-0 right-0 flex justify-between text-xs text-gray-500">
              {data.clientSatisfactionTrend.map((point) => (
                <div key={point.month} className="text-center">
                  {formatMonth(point.month)}
                </div>
              ))}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Success Rate by Area */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.5 }}
        className="bg-white rounded-lg border border-gray-200 p-6"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-6">
          Tasa de Éxito por Área Legal
        </h3>

        <div className="space-y-4">
          {data.successRateByArea
            .sort((a, b) => b.rate - a.rate)
            .map((area, index) => (
              <motion.div
                key={area.area}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
                className="flex items-center justify-between"
              >
                <div className="flex items-center space-x-3 flex-1">
                  <div
                    className={`w-4 h-4 rounded ${
                      areaColors[area.area as keyof typeof areaColors] ||
                      "bg-gray-500"
                    }`}
                  ></div>
                  <span className="font-medium text-gray-900">{area.area}</span>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-32 bg-gray-200 rounded-full h-3">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: `${area.rate}%` }}
                      transition={{ duration: 1, delay: 0.7 + index * 0.1 }}
                      className={`h-3 rounded-full ${
                        areaColors[
                          area.area as keyof typeof areaColors
                        ]?.replace("bg-", "bg-") || "bg-gray-500"
                      }`}
                    />
                  </div>
                  <div className="text-right min-w-12">
                    <span className="font-semibold text-gray-900">
                      {area.rate}%
                    </span>
                  </div>
                </div>
              </motion.div>
            ))}
        </div>

        {/* Performance Summary */}
        <div className="mt-6 pt-6 border-t border-gray-200 grid grid-cols-2 gap-6">
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              {Math.max(...data.successRateByArea.map((a) => a.rate))}%
            </div>
            <div className="text-sm text-green-800">Mejor Área</div>
            <div className="text-xs text-green-600 mt-1">
              {
                data.successRateByArea.find(
                  (a) =>
                    a.rate ===
                    Math.max(...data.successRateByArea.map((a) => a.rate))
                )?.area
              }
            </div>
          </div>
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {Math.round(
                data.successRateByArea.reduce(
                  (sum, area) => sum + area.rate,
                  0
                ) / data.successRateByArea.length
              )}
              %
            </div>
            <div className="text-sm text-blue-800">Promedio General</div>
            <div className="text-xs text-blue-600 mt-1">Todas las áreas</div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
