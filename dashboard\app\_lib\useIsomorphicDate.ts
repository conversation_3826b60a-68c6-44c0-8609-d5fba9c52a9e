"use client";

import { useState, useEffect } from "react";

/**
 * Hook to handle date operations that need to be consistent between server and client
 * This prevents hydration mismatches by ensuring dates are only calculated on the client
 */
export function useIsomorphicDate() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const now = isClient ? new Date() : new Date(0); // Use epoch time for SSR

  return {
    isClient,
    now,
    createDate: (dateString?: string | number | Date) => {
      if (!isClient) {
        // Return a consistent date for SSR
        return new Date(0);
      }
      return dateString ? new Date(dateString) : new Date();
    },
  };
}

/**
 * Hook to determine if we're on the client side (after hydration)
 * Useful for preventing hydration mismatches
 */
export function useIsMounted() {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  return isMounted;
}

/**
 * Safe hook for accessing localStorage that won't cause hydration issues
 */
export function useLocalStorage<T>(key: string, initialValue: T) {
  const [storedValue, setStoredValue] = useState<T>(initialValue);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    try {
      const item = window.localStorage.getItem(key);
      if (item) {
        setStoredValue(JSON.parse(item));
      }
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
    } finally {
      setIsLoaded(true);
    }
  }, [key]);

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore =
        value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      if (typeof window !== "undefined") {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      console.warn(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue, isLoaded] as const;
}
