"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AvatarDropdown } from "./AvatarDropdown";
import { NotificationDropdown } from "./NotificationDropdown";
import { Logo } from "../ui/Logo";
import { useUser } from "../../_contexts/UserContext";

export function TopBar() {
  const [newCasesCount, setNewCasesCount] = useState(0);
  const router = useRouter();
  const { user } = useUser();

  const handleLogoClick = () => {
    router.push("/dashboard");
  };

  // Load new cases count from localStorage or calculate from cases data
  useEffect(() => {
    // In a real app, this would come from an API or context
    // For now, we'll simulate getting new cases count
    const loadNewCasesCount = async () => {
      try {
        const response = await fetch("/data/cases.json");
        const cases = await response.json();
        const newCases = cases.filter(
          (c: { status: string }) => c.status === "new"
        );
        setNewCasesCount(newCases.length);
      } catch (error) {
        console.error("Error loading cases:", error);
        setNewCasesCount(2); // Fallback
      }
    };

    loadNewCasesCount();
  }, []);

  return (
    <header className="relative z-50 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Left side - Logo y título */}
        <div
          className="flex items-center space-x-3 cursor-pointer hover:opacity-80 transition-opacity"
          onClick={handleLogoClick}
        >
          <Logo size={32} className="flex-shrink-0" />
          <h1 className="text-xl font-bold text-gray-900 dark:text-gray-100">
            X-Legal
          </h1>
        </div>

        {/* Right side - notifications and avatar */}
        <div className="flex items-center space-x-3">
          {/* Notifications */}
          <NotificationDropdown newCasesCount={newCasesCount} />

          {/* User Avatar Dropdown */}
          <AvatarDropdown user={user} />
        </div>
      </div>
    </header>
  );
}
