"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { motion } from "framer-motion";
import {
  CheckCircleIcon,
  ChevronDownIcon,
  ArrowLeftIcon,
} from "@heroicons/react/24/outline";
import { AvailableCase } from "../../../_lib/types";

export default function BidPage() {
  const router = useRouter();
  const params = useParams();
  const caseId = params.caseId as string;

  const [step, setStep] = useState<"form" | "review" | "success">("form");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [caseData, setCaseData] = useState<AvailableCase | null>(null);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    feeType: "fixed",
    feeAmount: "",
    feeCurrency: "ARS",
    feePercentage: "",
    feeDescription: "",
    estimatedTimeline: "",
    experience: "",
    availability: "immediate",
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Load case data
  useEffect(() => {
    const loadCase = async () => {
      try {
        const response = await fetch("/data/available-cases.json");
        const casesData = await response.json();
        const foundCase = casesData.find((c: AvailableCase) => c.id === caseId);

        if (foundCase) {
          setCaseData(foundCase);
        } else {
          router.push("/dashboard?tab=available-cases");
        }
      } catch (error) {
        console.error("Error loading case:", error);
        router.push("/dashboard?tab=available-cases");
      } finally {
        setLoading(false);
      }
    };

    if (caseId) {
      loadCase();
    }
  }, [caseId, router]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validate fee amount or percentage based on fee type
    if (formData.feeType !== "contingency") {
      if (!formData.feeAmount.trim()) {
        newErrors.feeAmount = "El monto es obligatorio";
      } else if (
        isNaN(Number(formData.feeAmount)) ||
        Number(formData.feeAmount) <= 0
      ) {
        newErrors.feeAmount = "Ingrese un monto válido mayor a 0";
      }
    } else {
      if (!formData.feePercentage.trim()) {
        newErrors.feePercentage = "El porcentaje es obligatorio";
      } else if (
        isNaN(Number(formData.feePercentage)) ||
        Number(formData.feePercentage) <= 0 ||
        Number(formData.feePercentage) > 50
      ) {
        newErrors.feePercentage = "Ingrese un porcentaje válido entre 1 y 50";
      }
    }

    // Validate fee description
    if (!formData.feeDescription.trim()) {
      newErrors.feeDescription = "La descripción de honorarios es obligatoria";
    }

    // Validate estimated timeline
    if (!formData.estimatedTimeline.trim()) {
      newErrors.estimatedTimeline = "El tiempo estimado es obligatorio";
    }

    // Validate experience
    if (!formData.experience.trim()) {
      newErrors.experience = "La experiencia relevante es obligatoria";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;
    setStep("review");
  };

  const handleConfirmSubmit = async () => {
    setIsSubmitting(true);

    // Simulate submission delay
    setTimeout(() => {
      const bidData = {
        proposedFee: {
          type: formData.feeType as "fixed" | "contingency" | "hourly",
          amount:
            formData.feeType !== "contingency"
              ? parseInt(formData.feeAmount)
              : undefined,
          currency:
            formData.feeType !== "contingency"
              ? (formData.feeCurrency as "ARS" | "USD" | "UMA")
              : undefined,
          percentage:
            formData.feeType === "contingency"
              ? parseInt(formData.feePercentage)
              : undefined,
          description: formData.feeDescription,
        },
        estimatedTimeline: formData.estimatedTimeline,
        experience: formData.experience,
        availability: formData.availability,
      };

      // Save to localStorage (simulating API call)
      const userBids = JSON.parse(localStorage.getItem("userBids") || "[]");
      const newBid = {
        id: `bid-${Date.now()}`,
        caseId,
        ...bidData,
        submittedAt: new Date().toISOString(),
        status: "submitted",
      };
      userBids.push(newBid);
      localStorage.setItem("userBids", JSON.stringify(userBids));

      setIsSubmitting(false);
      setStep("success");

      // Auto-redirect after success
      setTimeout(() => {
        router.push("/dashboard?tab=available-cases");
      }, 3000);
    }, 1500);
  };

  const handleBack = () => {
    router.back();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!caseData) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            Caso no encontrado
          </p>
          <button
            onClick={() => router.push("/dashboard?tab=available-cases")}
            className="text-blue-600 hover:text-blue-700 font-medium"
          >
            Volver a casos disponibles
          </button>
        </div>
      </div>
    );
  }

  const renderStepContent = () => {
    switch (step) {
      case "form":
        return (
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Fee Structure */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Estructura de Honorarios
              </label>
              <div className="space-y-3">
                <label className="flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                  <input
                    type="radio"
                    name="feeType"
                    value="fixed"
                    checked={formData.feeType === "fixed"}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        feeType: e.target.value,
                      }))
                    }
                    className="text-blue-600 focus:ring-blue-500"
                  />
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 dark:text-gray-100">
                      Honorario Fijo
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">
                      Monto total por el caso completo
                    </div>
                  </div>
                </label>

                <label className="flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                  <input
                    type="radio"
                    name="feeType"
                    value="hourly"
                    checked={formData.feeType === "hourly"}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        feeType: e.target.value,
                      }))
                    }
                    className="text-blue-600 focus:ring-blue-500"
                  />
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 dark:text-gray-100">
                      Por Hora
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">
                      Tarifa por hora trabajada
                    </div>
                  </div>
                </label>

                <label className="flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                  <input
                    type="radio"
                    name="feeType"
                    value="contingency"
                    checked={formData.feeType === "contingency"}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        feeType: e.target.value,
                      }))
                    }
                    className="text-blue-600 focus:ring-blue-500"
                  />
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 dark:text-gray-100">
                      Cuota Litis
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">
                      Porcentaje del resultado obtenido
                    </div>
                  </div>
                </label>
              </div>
            </div>

            {/* Fee Amount */}
            {formData.feeType !== "contingency" ? (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Monto {formData.feeType === "hourly" ? "por Hora" : "Total"} *
                </label>
                <div className="flex space-x-2 items-center">
                  <div className="flex-1">
                    <input
                      type="number"
                      value={formData.feeAmount}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          feeAmount: e.target.value,
                        }))
                      }
                      className={`w-full h-12 px-4 py-3 border rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        errors.feeAmount
                          ? "border-red-500"
                          : "border-gray-300 dark:border-gray-600"
                      }`}
                      placeholder="150000"
                    />
                  </div>
                  <div className="w-28 relative">
                    <select
                      value={formData.feeCurrency}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          feeCurrency: e.target.value,
                        }))
                      }
                      className="w-full h-12 px-4 pr-10 py-3 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none cursor-pointer"
                    >
                      <option value="ARS">ARS</option>
                      <option value="USD">USD</option>
                      <option value="UMA">UMA</option>
                    </select>
                    <ChevronDownIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 dark:text-gray-500 pointer-events-none" />
                  </div>
                </div>
                {errors.feeAmount && (
                  <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                    {errors.feeAmount}
                  </p>
                )}
              </div>
            ) : (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Porcentaje (%) *
                </label>
                <input
                  type="number"
                  min="1"
                  max="50"
                  value={formData.feePercentage}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      feePercentage: e.target.value,
                    }))
                  }
                  className={`w-full h-12 px-4 py-3 border rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.feePercentage
                      ? "border-red-500"
                      : "border-gray-300 dark:border-gray-600"
                  }`}
                  placeholder="30"
                />
                {errors.feePercentage && (
                  <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                    {errors.feePercentage}
                  </p>
                )}
              </div>
            )}

            {/* Fee Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Descripción de Honorarios *
              </label>
              <textarea
                rows={4}
                value={formData.feeDescription}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    feeDescription: e.target.value,
                  }))
                }
                className={`w-full px-4 py-3 border rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none ${
                  errors.feeDescription
                    ? "border-red-500"
                    : "border-gray-300 dark:border-gray-600"
                }`}
                placeholder="Incluye todas las gestiones necesarias, presentaciones ante organismos, etc."
              />
              {errors.feeDescription && (
                <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                  {errors.feeDescription}
                </p>
              )}
            </div>

            {/* Timeline */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Tiempo Estimado de Resolución *
              </label>
              <input
                type="text"
                value={formData.estimatedTimeline}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    estimatedTimeline: e.target.value,
                  }))
                }
                className={`w-full h-12 px-4 py-3 border rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.estimatedTimeline
                    ? "border-red-500"
                    : "border-gray-300 dark:border-gray-600"
                }`}
                placeholder="3-6 meses"
              />
              {errors.estimatedTimeline && (
                <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                  {errors.estimatedTimeline}
                </p>
              )}
            </div>

            {/* Experience */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Experiencia Relevante *
              </label>
              <textarea
                rows={5}
                value={formData.experience}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    experience: e.target.value,
                  }))
                }
                className={`w-full px-4 py-3 border rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none ${
                  errors.experience
                    ? "border-red-500"
                    : "border-gray-300 dark:border-gray-600"
                }`}
                placeholder="Describa su experiencia en casos similares, resultados obtenidos, especialización en el área, etc."
              />
              {errors.experience && (
                <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                  {errors.experience}
                </p>
              )}
            </div>

            {/* Availability */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Disponibilidad para Comenzar
              </label>
              <select
                value={formData.availability}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    availability: e.target.value,
                  }))
                }
                className="w-full h-12 cursor-pointer px-4 py-3 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="immediate">Inmediata</option>
                <option value="1_week">En 1 semana</option>
                <option value="2_weeks">En 2 semanas</option>
                <option value="1_month">En 1 mes</option>
              </select>
            </div>

            <div className="flex flex-col sm:flex-row sm:space-x-4 space-y-3 sm:space-y-0 pt-6">
              <button
                type="button"
                onClick={handleBack}
                className="flex-1 px-6 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors cursor-pointer"
              >
                Cancelar
              </button>
              <button
                type="submit"
                className="flex-1 px-6 py-3 text-sm font-medium text-white bg-blue-600 dark:bg-blue-700 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors cursor-pointer"
              >
                Revisar Propuesta
              </button>
            </div>
          </form>
        );

      case "review":
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
                Revisar Propuesta
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Verifique los detalles antes de enviar su propuesta
              </p>
            </div>

            <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 space-y-4">
              <div className="flex justify-between items-center">
                <span className="font-medium text-gray-900 dark:text-gray-100">
                  Honorarios:
                </span>
                <span className="text-gray-600 dark:text-gray-300 font-semibold">
                  {formData.feeType === "contingency"
                    ? `${formData.feePercentage}% del resultado`
                    : `${formData.feeCurrency} ${parseInt(
                        formData.feeAmount
                      ).toLocaleString("es-AR")} ${
                        formData.feeType === "hourly" ? "por hora" : "total"
                      }`}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="font-medium text-gray-900 dark:text-gray-100">
                  Tiempo estimado:
                </span>
                <span className="text-gray-600 dark:text-gray-300">
                  {formData.estimatedTimeline}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="font-medium text-gray-900 dark:text-gray-100">
                  Disponibilidad:
                </span>
                <span className="text-gray-600 dark:text-gray-300">
                  {formData.availability === "immediate"
                    ? "Inmediata"
                    : formData.availability === "1_week"
                    ? "En 1 semana"
                    : formData.availability === "2_weeks"
                    ? "En 2 semanas"
                    : "En 1 mes"}
                </span>
              </div>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/30 rounded-xl p-6">
              <p className="text-blue-800 dark:text-blue-200">
                <strong>Nota:</strong> Una vez enviada, su propuesta será
                revisada por el cliente. Recibirá una notificación cuando el
                cliente tome una decisión.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row sm:space-x-4 space-y-3 sm:space-y-0">
              <button
                onClick={() => setStep("form")}
                className="flex-1 px-6 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors cursor-pointer"
              >
                Editar
              </button>
              <button
                onClick={handleConfirmSubmit}
                disabled={isSubmitting}
                className="flex-1 px-6 py-3 text-sm font-medium text-white bg-blue-600 dark:bg-blue-700 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors cursor-pointer"
              >
                {isSubmitting ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                    Enviando...
                  </div>
                ) : (
                  "Enviar Propuesta"
                )}
              </button>
            </div>
          </div>
        );

      case "success":
        return (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            className="text-center space-y-8"
          >
            <div className="text-green-600">
              <CheckCircleIcon className="h-20 w-20 mx-auto mb-6" />
            </div>
            <div>
              <h3 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                ¡Propuesta Enviada!
              </h3>
              <p className="text-gray-600 dark:text-gray-300 text-lg">
                Su propuesta para &quot;{caseData.title}&quot; ha sido enviada
                exitosamente. El cliente la revisará y se pondrá en contacto con
                usted.
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-4">
                Será redirigido a la lista de casos en unos segundos...
              </p>
            </div>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="px-2 py-2">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-3">
          {/* Left Panel - Case Info */}
          <div className="lg:col-span-1">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4">
              <div className="flex items-center mb-4">
                <button
                  onClick={handleBack}
                  className="mr-3 p-1.5 cursor-pointer text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  <ArrowLeftIcon className="h-5 w-5" />
                </button>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Información del Caso
                </h3>
              </div>

              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-gray-100 text-xl mb-3">
                    {caseData.title}
                  </h4>
                  <div className="flex flex-wrap gap-2 mb-4">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                      {caseData.type}
                    </span>
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                      {caseData.urgencyLevel === "urgent"
                        ? "Urgente"
                        : caseData.urgencyLevel === "high"
                        ? "Alta"
                        : caseData.urgencyLevel === "medium"
                        ? "Media"
                        : "Baja"}
                    </span>
                  </div>
                </div>

                <div>
                  <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-3">
                    Descripción:
                  </h5>
                  <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
                    {caseData.description}
                  </p>
                </div>

                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700 dark:text-gray-300">
                      Cliente:
                    </span>
                    <span className="text-gray-600 dark:text-gray-400">
                      {caseData.clientName}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700 dark:text-gray-300">
                      Ubicación:
                    </span>
                    <span className="text-gray-600 dark:text-gray-400">
                      {caseData.clientLocation}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700 dark:text-gray-300">
                      Presupuesto:
                    </span>
                    <span className="text-gray-600 dark:text-gray-400">
                      {caseData.budgetRange}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700 dark:text-gray-300">
                      Propuestas:
                    </span>
                    <span className="text-gray-600 dark:text-gray-400">
                      {caseData.bidsCount} recibidas
                    </span>
                  </div>
                </div>

                {/* Additional Details */}
                {caseData.details &&
                  Object.keys(caseData.details).length > 0 && (
                    <div>
                      <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-3">
                        Detalles Adicionales:
                      </h5>
                      <div className="space-y-2 text-sm">
                        {Object.entries(caseData.details).map(
                          ([key, value]) => (
                            <div key={key} className="flex justify-between">
                              <span className="font-medium text-gray-700 dark:text-gray-300 capitalize">
                                {key.replace(/([A-Z])/g, " $1").toLowerCase()}:
                              </span>
                              <span className="text-gray-600 dark:text-gray-400">
                                {typeof value === "boolean"
                                  ? value
                                    ? "Sí"
                                    : "No"
                                  : typeof value === "object"
                                  ? JSON.stringify(value)
                                  : String(value)}
                              </span>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}
              </div>
            </div>
          </div>

          {/* Right Panel - Form */}
          <div className="lg:col-span-2">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4">
              <div className="mb-4">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {step === "form" && "Enviar Propuesta"}
                  {step === "review" && "Revisar Propuesta"}
                  {step === "success" && "Propuesta Enviada"}
                </h1>
                {step === "form" && (
                  <p className="text-gray-600 dark:text-gray-400 mt-2">
                    Complete el formulario para enviar su propuesta al cliente
                  </p>
                )}
              </div>

              {renderStepContent()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
