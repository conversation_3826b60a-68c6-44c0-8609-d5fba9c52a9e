import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen } from "@testing-library/react";
import { CaseContextPanel } from "../cases/CaseContextPanel";
import type { Case } from "../../_lib/types";

// Mock the config.json to enable intelligence tab for testing
vi.mock("../../../public/data/config.json", () => ({
  default: {
    featureFlags: {
      caseDetails: {
        intelligenceTab: {
          enabled: true,
        },
      },
    },
  },
}));

// Mock framer-motion to avoid animation issues in tests
vi.mock("framer-motion", () => ({
  motion: {
    div: ({ children, ...props }: React.ComponentProps<"div">) => (
      <div {...props}>{children}</div>
    ),
  },
}));

const mockCaseWithRecommendations: Case = {
  id: "c-001",
  title: "Test Case with Recommendations",
  type: "Laboral",
  client: "Test Client",
  status: "new",
  progress: 0.2,
  createdAt: "2025-06-14T10:30:00-03:00",
  similarCount: 2,
  description: "Test case description",
  priority: "high",
  estimatedCost: "ARS 150,000 - 200,000",
  complexityScore: 7,
  riskAssessment: "medium",
  successProbability: 85,
  aiSummary: "Test AI summary",
  keyFacts: ["Test fact 1", "Test fact 2"],
  nextActions: [
    "Revisar cálculo de indemnización",
    "Preparar carta documento",
    "Evaluar negociación extrajudicial",
  ],
  milestones: [],
  messages: [],
  documents: [],
  activities: [],
};

const mockCaseWithoutRecommendations: Case = {
  id: "c-002",
  title: "Test Case without Recommendations",
  type: "Civil",
  client: "Test Client 2",
  status: "in_progress",
  progress: 0.6,
  createdAt: "2025-06-10T15:00:00-03:00",
  similarCount: 0,
  description: "Test case without recommendations",
  priority: "medium",
  estimatedCost: "ARS 60,000 - 90,000",
  complexityScore: 4,
  riskAssessment: "low",
  successProbability: 85,
  aiSummary: "Test AI summary without recommendations",
  keyFacts: ["Test fact 1", "Test fact 2"],
  // nextActions is missing - this should cause the test to fail
  milestones: [],
  messages: [],
  documents: [],
  activities: [],
};

describe("CaseContextPanel", () => {
  const mockOnTabChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should display AI recommendations in context panel when nextActions are provided", () => {
    render(
      <CaseContextPanel
        case={mockCaseWithRecommendations}
        onTabChange={mockOnTabChange}
      />
    );

    // Check that the AI recommendations section is displayed
    expect(screen.getByText("Recomendaciones IA")).toBeInTheDocument();

    // Check that specific recommendations are displayed
    expect(
      screen.getByText("Revisar cálculo de indemnización")
    ).toBeInTheDocument();
    expect(screen.getByText("Preparar carta documento")).toBeInTheDocument();
    expect(
      screen.getByText("Evaluar negociación extrajudicial")
    ).toBeInTheDocument();
  });

  it("should display AI recommendations for different case types", () => {
    const civilCase: Case = {
      ...mockCaseWithoutRecommendations,
      nextActions: [
        "Solicitar peritaje técnico de daños",
        "Enviar carta documento al consorcio",
        "Recopilar presupuestos de reparación",
      ],
    };

    render(<CaseContextPanel case={civilCase} onTabChange={mockOnTabChange} />);

    expect(screen.getByText("Recomendaciones IA")).toBeInTheDocument();
    expect(
      screen.getByText("Solicitar peritaje técnico de daños")
    ).toBeInTheDocument();
    expect(
      screen.getByText("Enviar carta documento al consorcio")
    ).toBeInTheDocument();
    expect(
      screen.getByText("Recopilar presupuestos de reparación")
    ).toBeInTheDocument();
  });

  it("should fail when case lacks AI recommendations in context panel", () => {
    // This test should fail until we add nextActions to all cases
    render(
      <CaseContextPanel
        case={mockCaseWithoutRecommendations}
        onTabChange={mockOnTabChange}
      />
    );

    // This should fail because the case doesn't have nextActions
    expect(screen.queryByText("Recomendaciones IA")).not.toBeInTheDocument();

    // The test expects recommendations to be present, so this will fail
    expect(() => {
      expect(
        screen.getByText("Solicitar peritaje técnico de daños")
      ).toBeInTheDocument();
    }).toThrow();
  });

  it("should display case summary and key facts", () => {
    render(
      <CaseContextPanel
        case={mockCaseWithRecommendations}
        onTabChange={mockOnTabChange}
      />
    );

    // Check that AI summary section is displayed
    expect(screen.getByText("Resumen IA del Caso")).toBeInTheDocument();

    // Check that key facts are displayed
    expect(screen.getByText("Test fact 1")).toBeInTheDocument();
    expect(screen.getByText("Test fact 2")).toBeInTheDocument();
  });

  it("should display navigation items", () => {
    render(
      <CaseContextPanel
        case={mockCaseWithRecommendations}
        onTabChange={mockOnTabChange}
      />
    );

    // Check that navigation items are displayed
    expect(screen.getByText("Mensajes")).toBeInTheDocument();
    expect(screen.getByText("Tareas")).toBeInTheDocument();
    expect(screen.getByText("Documentos")).toBeInTheDocument();
    expect(screen.getByText("Historial")).toBeInTheDocument();
    expect(screen.getByText("Inteligencia")).toBeInTheDocument();
  });

  it("should display progress information", () => {
    render(
      <CaseContextPanel
        case={mockCaseWithRecommendations}
        onTabChange={mockOnTabChange}
      />
    );

    // Check that progress is displayed in the intelligence navigation item (20% = 0.2)
    expect(screen.getByText("20% progreso")).toBeInTheDocument();
  });
});
