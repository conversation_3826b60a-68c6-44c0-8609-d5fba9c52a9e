{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/ui/Chat.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { format } from \"date-fns\";\r\nimport { es } from \"date-fns/locale\";\r\nimport {\r\n  PaperAirplaneIcon,\r\n  PaperClipIcon,\r\n  MagnifyingGlassIcon,\r\n  CheckIcon,\r\n  CheckCircleIcon,\r\n} from \"@heroicons/react/24/outline\";\r\nimport { Message } from \"../../_lib/types\";\r\n\r\ninterface ChatProps {\r\n  messages: Message[];\r\n  caseId: string;\r\n}\r\n\r\nconst quickReplies = [\r\n  \"Perfecto, revisaré la documentación.\",\r\n  \"Necesito más información sobre este punto.\",\r\n  \"Procederemos con la siguiente etapa.\",\r\n  \"Te mantendré informado del progreso.\",\r\n];\r\n\r\nexport function Chat({ messages: initialMessages, caseId }: ChatProps) {\r\n  const [messages, setMessages] = useState<Message[]>(initialMessages);\r\n  const [newMessage, setNewMessage] = useState(\"\");\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [showQuickReplies, setShowQuickReplies] = useState(false);\r\n  const messagesEndRef = useRef<HTMLDivElement>(null);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  const scrollToBottom = () => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\r\n  };\r\n\r\n  useEffect(() => {\r\n    scrollToBottom();\r\n  }, [messages]);\r\n\r\n  const filteredMessages = messages.filter(\r\n    (message) =>\r\n      searchTerm === \"\" ||\r\n      message.content.toLowerCase().includes(searchTerm.toLowerCase())\r\n  );\r\n\r\n  const handleSendMessage = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (!newMessage.trim()) return;\r\n\r\n    const message: Message = {\r\n      id: `msg-${crypto.randomUUID()}`,\r\n      sender: \"lawyer\",\r\n      content: newMessage.trim(),\r\n      timestamp: new Date().toISOString(),\r\n      status: \"sent\",\r\n      attachments: [],\r\n    };\r\n\r\n    setMessages((prev) => [...prev, message]);\r\n    setNewMessage(\"\");\r\n    setShowQuickReplies(false);\r\n\r\n    // Simulate message status updates\r\n    setTimeout(() => {\r\n      setMessages((prev) =>\r\n        prev.map((msg) =>\r\n          msg.id === message.id ? { ...msg, status: \"delivered\" } : msg\r\n        )\r\n      );\r\n    }, 1000);\r\n\r\n    setTimeout(() => {\r\n      setMessages((prev) =>\r\n        prev.map((msg) =>\r\n          msg.id === message.id ? { ...msg, status: \"read\" } : msg\r\n        )\r\n      );\r\n    }, 3000);\r\n\r\n    // Save to sessionStorage\r\n    const savedMessages = sessionStorage.getItem(`chat-${caseId}`);\r\n    const allMessages = savedMessages\r\n      ? JSON.parse(savedMessages)\r\n      : initialMessages;\r\n    sessionStorage.setItem(\r\n      `chat-${caseId}`,\r\n      JSON.stringify([...allMessages, message])\r\n    );\r\n  };\r\n\r\n  const handleQuickReply = (reply: string) => {\r\n    setNewMessage(reply);\r\n    setShowQuickReplies(false);\r\n  };\r\n\r\n  const handleFileAttach = () => {\r\n    fileInputRef.current?.click();\r\n  };\r\n\r\n  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const files = e.target.files;\r\n    if (files && files.length > 0) {\r\n      // In a real app, you would upload the file and get a URL\r\n      const file = files[0];\r\n      const message: Message = {\r\n        id: `msg-${Date.now()}`,\r\n        sender: \"lawyer\",\r\n        content: `Archivo adjunto: ${file.name}`,\r\n        timestamp: new Date().toISOString(),\r\n        status: \"sent\",\r\n        attachments: [\r\n          {\r\n            id: `att-${Date.now()}`,\r\n            name: file.name,\r\n            type: file.type,\r\n            size: `${Math.round(file.size / 1024)}KB`,\r\n          },\r\n        ],\r\n      };\r\n\r\n      setMessages((prev) => [...prev, message]);\r\n    }\r\n  };\r\n\r\n  const getStatusIcon = (status?: string) => {\r\n    switch (status) {\r\n      case \"sent\":\r\n        return <CheckIcon className=\"h-3 w-3\" />;\r\n      case \"delivered\":\r\n        return <CheckIcon className=\"h-3 w-3\" />;\r\n      case \"read\":\r\n        return <CheckCircleIcon className=\"h-3 w-3\" />;\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-96 flex flex-col\">\r\n      {/* Search Bar */}\r\n      <div className=\"border-b border-gray-200 dark:border-gray-700 p-3\">\r\n        <div className=\"relative\">\r\n          <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500\" />\r\n          <input\r\n            type=\"text\"\r\n            value={searchTerm}\r\n            onChange={(e) => setSearchTerm(e.target.value)}\r\n            placeholder=\"Buscar en el chat...\"\r\n            className=\"w-full pl-10 pr-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Messages */}\r\n      <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\r\n        {filteredMessages.map((message) => (\r\n          <div\r\n            key={message.id}\r\n            className={`flex ${\r\n              message.sender === \"lawyer\" ? \"justify-end\" : \"justify-start\"\r\n            }`}\r\n          >\r\n            <div\r\n              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${\r\n                message.sender === \"lawyer\"\r\n                  ? \"bg-blue-600 text-white\"\r\n                  : \"bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\r\n              }`}\r\n            >\r\n              <p className=\"text-sm\">{message.content}</p>\r\n\r\n              {/* Attachments */}\r\n              {message.attachments && message.attachments.length > 0 && (\r\n                <div className=\"mt-2 space-y-1\">\r\n                  {message.attachments.map((attachment) => (\r\n                    <div\r\n                      key={attachment.id}\r\n                      className={`flex items-center space-x-2 p-2 rounded ${\r\n                        message.sender === \"lawyer\"\r\n                          ? \"bg-blue-500\"\r\n                          : \"bg-gray-200 dark:bg-gray-600\"\r\n                      }`}\r\n                    >\r\n                      <PaperClipIcon className=\"h-3 w-3\" />\r\n                      <span className=\"text-xs\">{attachment.name}</span>\r\n                      <span className=\"text-xs opacity-75\">\r\n                        ({attachment.size})\r\n                      </span>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n\r\n              <div className=\"flex items-center justify-between mt-1\">\r\n                <p\r\n                  className={`text-xs ${\r\n                    message.sender === \"lawyer\"\r\n                      ? \"text-blue-100\"\r\n                      : \"text-gray-500 dark:text-gray-400\"\r\n                  }`}\r\n                >\r\n                  {format(new Date(message.timestamp), \"HH:mm\", { locale: es })}\r\n                </p>\r\n                {message.sender === \"lawyer\" && (\r\n                  <div\r\n                    className={`text-xs ${\r\n                      message.status === \"read\"\r\n                        ? \"text-blue-200\"\r\n                        : \"text-blue-300\"\r\n                    }`}\r\n                  >\r\n                    {getStatusIcon(message.status)}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n        <div ref={messagesEndRef} />\r\n      </div>\r\n\r\n      {/* Quick Replies */}\r\n      {showQuickReplies && (\r\n        <div className=\"border-t border-gray-200 dark:border-gray-700 p-3 animate-fade-in\">\r\n          <div className=\"flex flex-wrap gap-3\">\r\n            {quickReplies.map((reply, index) => (\r\n              <button\r\n                key={index}\r\n                onClick={() => handleQuickReply(reply)}\r\n                className=\"flex items-center gap-2 px-4 py-2 text-sm font-semibold bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 border border-blue-300 dark:border-blue-700 rounded-lg shadow hover:bg-blue-200 dark:hover:bg-blue-800 transition-all duration-150\"\r\n                style={{ boxShadow: '0 2px 8px rgba(59,130,246,0.08)' }}\r\n              >\r\n                <svg className=\"h-4 w-4 text-blue-400 dark:text-blue-200\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\"><path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M8 12h8m-8 4h6\" /></svg>\r\n                {reply}\r\n              </button>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Message Input */}\r\n      <form\r\n        onSubmit={handleSendMessage}\r\n        className=\"border-t border-gray-200 dark:border-gray-700 p-4\"\r\n      >\r\n        <div className=\"flex space-x-2\">\r\n          <button\r\n            type=\"button\"\r\n            onClick={handleFileAttach}\r\n            className=\"text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-400 p-2\"\r\n          >\r\n            <PaperClipIcon className=\"h-4 w-4\" />\r\n          </button>\r\n          <input\r\n            type=\"text\"\r\n            value={newMessage}\r\n            onChange={(e) => setNewMessage(e.target.value)}\r\n            onFocus={() => setShowQuickReplies(true)}\r\n            placeholder=\"Escribe un mensaje...\"\r\n            className=\"flex-1 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n          />\r\n          <button\r\n            type=\"submit\"\r\n            disabled={!newMessage.trim()}\r\n            className=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n          >\r\n            <PaperAirplaneIcon className=\"h-4 w-4\" />\r\n          </button>\r\n        </div>\r\n        <input\r\n          ref={fileInputRef}\r\n          type=\"file\"\r\n          onChange={handleFileSelect}\r\n          className=\"hidden\"\r\n          accept=\".pdf,.doc,.docx,.jpg,.jpeg,.png\"\r\n        />\r\n      </form>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAmBA,MAAM,eAAe;IACnB;IACA;IACA;IACA;CACD;AAEM,SAAS,KAAK,EAAE,UAAU,eAAe,EAAE,MAAM,EAAa;;IACnE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;IACpD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR;QACF;yBAAG;QAAC;KAAS;IAEb,MAAM,mBAAmB,SAAS,MAAM,CACtC,CAAC,UACC,eAAe,MACf,QAAQ,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAGjE,MAAM,oBAAoB,CAAC;QACzB,EAAE,cAAc;QAChB,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,MAAM,UAAmB;YACvB,IAAI,CAAC,IAAI,EAAE,OAAO,UAAU,IAAI;YAChC,QAAQ;YACR,SAAS,WAAW,IAAI;YACxB,WAAW,IAAI,OAAO,WAAW;YACjC,QAAQ;YACR,aAAa,EAAE;QACjB;QAEA,YAAY,CAAC,OAAS;mBAAI;gBAAM;aAAQ;QACxC,cAAc;QACd,oBAAoB;QAEpB,kCAAkC;QAClC,WAAW;YACT,YAAY,CAAC,OACX,KAAK,GAAG,CAAC,CAAC,MACR,IAAI,EAAE,KAAK,QAAQ,EAAE,GAAG;wBAAE,GAAG,GAAG;wBAAE,QAAQ;oBAAY,IAAI;QAGhE,GAAG;QAEH,WAAW;YACT,YAAY,CAAC,OACX,KAAK,GAAG,CAAC,CAAC,MACR,IAAI,EAAE,KAAK,QAAQ,EAAE,GAAG;wBAAE,GAAG,GAAG;wBAAE,QAAQ;oBAAO,IAAI;QAG3D,GAAG;QAEH,yBAAyB;QACzB,MAAM,gBAAgB,eAAe,OAAO,CAAC,CAAC,KAAK,EAAE,QAAQ;QAC7D,MAAM,cAAc,gBAChB,KAAK,KAAK,CAAC,iBACX;QACJ,eAAe,OAAO,CACpB,CAAC,KAAK,EAAE,QAAQ,EAChB,KAAK,SAAS,CAAC;eAAI;YAAa;SAAQ;IAE5C;IAEA,MAAM,mBAAmB,CAAC;QACxB,cAAc;QACd,oBAAoB;IACtB;IAEA,MAAM,mBAAmB;QACvB,aAAa,OAAO,EAAE;IACxB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YAC7B,yDAAyD;YACzD,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,MAAM,UAAmB;gBACvB,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;gBACvB,QAAQ;gBACR,SAAS,CAAC,iBAAiB,EAAE,KAAK,IAAI,EAAE;gBACxC,WAAW,IAAI,OAAO,WAAW;gBACjC,QAAQ;gBACR,aAAa;oBACX;wBACE,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;wBACvB,MAAM,KAAK,IAAI;wBACf,MAAM,KAAK,IAAI;wBACf,MAAM,GAAG,KAAK,KAAK,CAAC,KAAK,IAAI,GAAG,MAAM,EAAE,CAAC;oBAC3C;iBACD;YACH;YAEA,YAAY,CAAC,OAAS;uBAAI;oBAAM;iBAAQ;QAC1C;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,oNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;YAC9B,KAAK;gBACH,qBAAO,6LAAC,oNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;YAC9B,KAAK;gBACH,qBAAO,6LAAC,gOAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;YACpC;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,wOAAA,CAAA,sBAAmB;4BAAC,WAAU;;;;;;sCAC/B,6LAAC;4BACC,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,aAAY;4BACZ,WAAU;;;;;;;;;;;;;;;;;0BAMhB,6LAAC;gBAAI,WAAU;;oBACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC;4BAEC,WAAW,CAAC,KAAK,EACf,QAAQ,MAAM,KAAK,WAAW,gBAAgB,iBAC9C;sCAEF,cAAA,6LAAC;gCACC,WAAW,CAAC,0CAA0C,EACpD,QAAQ,MAAM,KAAK,WACf,2BACA,iEACJ;;kDAEF,6LAAC;wCAAE,WAAU;kDAAW,QAAQ,OAAO;;;;;;oCAGtC,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,MAAM,GAAG,mBACnD,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,WAAW,CAAC,GAAG,CAAC,CAAC,2BACxB,6LAAC;gDAEC,WAAW,CAAC,wCAAwC,EAClD,QAAQ,MAAM,KAAK,WACf,gBACA,gCACJ;;kEAEF,6LAAC,4NAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;kEACzB,6LAAC;wDAAK,WAAU;kEAAW,WAAW,IAAI;;;;;;kEAC1C,6LAAC;wDAAK,WAAU;;4DAAqB;4DACjC,WAAW,IAAI;4DAAC;;;;;;;;+CAVf,WAAW,EAAE;;;;;;;;;;kDAiB1B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAW,CAAC,QAAQ,EAClB,QAAQ,MAAM,KAAK,WACf,kBACA,oCACJ;0DAED,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ,SAAS,GAAG,SAAS;oDAAE,QAAQ,8IAAA,CAAA,KAAE;gDAAC;;;;;;4CAE5D,QAAQ,MAAM,KAAK,0BAClB,6LAAC;gDACC,WAAW,CAAC,QAAQ,EAClB,QAAQ,MAAM,KAAK,SACf,kBACA,iBACJ;0DAED,cAAc,QAAQ,MAAM;;;;;;;;;;;;;;;;;;2BAtDhC,QAAQ,EAAE;;;;;kCA6DnB,6LAAC;wBAAI,KAAK;;;;;;;;;;;;YAIX,kCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,OAAO,sBACxB,6LAAC;4BAEC,SAAS,IAAM,iBAAiB;4BAChC,WAAU;4BACV,OAAO;gCAAE,WAAW;4BAAkC;;8CAEtD,6LAAC;oCAAI,WAAU;oCAA2C,MAAK;oCAAO,QAAO;oCAAe,aAAY;oCAAI,SAAQ;8CAAY,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,GAAE;;;;;;;;;;;gCACpL;;2BANI;;;;;;;;;;;;;;;0BAcf,6LAAC;gBACC,UAAU;gBACV,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,4NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;0CAE3B,6LAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,SAAS,IAAM,oBAAoB;gCACnC,aAAY;gCACZ,WAAU;;;;;;0CAEZ,6LAAC;gCACC,MAAK;gCACL,UAAU,CAAC,WAAW,IAAI;gCAC1B,WAAU;0CAEV,cAAA,6LAAC,oOAAA,CAAA,oBAAiB;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGjC,6LAAC;wBACC,KAAK;wBACL,MAAK;wBACL,UAAU;wBACV,WAAU;wBACV,QAAO;;;;;;;;;;;;;;;;;;AAKjB;GAhQgB;KAAA", "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/cases/MilestoneTimeline.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { format } from 'date-fns';\r\nimport { es } from 'date-fns/locale';\r\nimport { CheckCircleIcon, ClockIcon } from '@heroicons/react/24/outline';\r\nimport { CheckCircleIcon as CheckCircleSolidIcon } from '@heroicons/react/24/solid';\r\nimport { Milestone } from '../../_lib/types';\r\n\r\ninterface MilestoneTimelineProps {\r\n  milestones: Milestone[];\r\n  caseId: string;\r\n}\r\n\r\nexport function MilestoneTimeline({ milestones: initialMilestones, caseId }: MilestoneTimelineProps) {\r\n  const [milestones, setMilestones] = useState<Milestone[]>(initialMilestones);\r\n\r\n  const toggleMilestone = (milestoneId: string) => {\r\n    setMilestones((prev) =>\r\n      prev.map((milestone) =>\r\n        milestone.id === milestoneId\r\n          ? { ...milestone, completed: !milestone.completed }\r\n          : milestone\r\n      )\r\n    );\r\n\r\n    // In a real app, you would save this to sessionStorage or send to an API\r\n    const updatedMilestones = milestones.map((milestone) =>\r\n      milestone.id === milestoneId\r\n        ? { ...milestone, completed: !milestone.completed }\r\n        : milestone\r\n    );\r\n    sessionStorage.setItem(`milestones-${caseId}`, JSON.stringify(updatedMilestones));\r\n  };\r\n\r\n  const completedCount = milestones.filter((m) => m.completed).length;\r\n  const progressPercentage = (completedCount / milestones.length) * 100;\r\n\r\n  return (\r\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6\">\r\n      {/* Progress Summary */}\r\n      <div className=\"mb-6\">\r\n        <div className=\"flex items-center justify-between mb-2\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n            Progreso del Caso\r\n          </h3>\r\n          <span className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n            {completedCount} de {milestones.length} completadas\r\n          </span>\r\n        </div>\r\n        <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\r\n          <div\r\n            className=\"bg-blue-600 dark:bg-blue-500 h-2 rounded-full transition-all duration-300\"\r\n            style={{ width: `${progressPercentage}%` }}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Timeline */}\r\n      <div className=\"space-y-4\">\r\n        {milestones.map((milestone, index) => {\r\n          const isLast = index === milestones.length - 1;\r\n          const dueDate = new Date(milestone.dueDate);\r\n          const isOverdue = !milestone.completed && dueDate < new Date();\r\n\r\n          return (\r\n            <div key={milestone.id} className=\"relative\">\r\n              {/* Timeline line */}\r\n              {!isLast && (\r\n                <div\r\n                  className={`absolute left-4 top-8 w-0.5 h-8 ${\r\n                    milestone.completed ? 'bg-green-300 dark:bg-green-400' : 'bg-gray-300 dark:bg-gray-600'\r\n                  }`}\r\n                />\r\n              )}\r\n\r\n              {/* Milestone item */}\r\n              <div className=\"flex items-start space-x-3\">\r\n                <button\r\n                  onClick={() => toggleMilestone(milestone.id)}\r\n                  className=\"flex-shrink-0 mt-0.5 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full\"\r\n                >\r\n                  {milestone.completed ? (\r\n                    <CheckCircleSolidIcon className=\"h-8 w-8 text-green-500\" />\r\n                  ) : (\r\n                    <CheckCircleIcon\r\n                      className={`h-8 w-8 ${\r\n                        isOverdue ? 'text-red-400 dark:text-red-500' : 'text-gray-400 dark:text-gray-500'\r\n                      } hover:text-blue-500 dark:hover:text-blue-400 transition-colors`}\r\n                    />\r\n                  )}\r\n                </button>\r\n\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <h4\r\n                      className={`text-sm font-medium ${\r\n                        milestone.completed\r\n                          ? 'text-gray-500 dark:text-gray-400 line-through'\r\n                          : 'text-gray-900 dark:text-gray-100'\r\n                      }`}\r\n                    >\r\n                      {milestone.title}\r\n                    </h4>\r\n                    <div className=\"flex items-center text-xs text-gray-500 dark:text-gray-400\">\r\n                      <ClockIcon className=\"h-3 w-3 mr-1\" />\r\n                      <span\r\n                        className={\r\n                          isOverdue ? 'text-red-500 dark:text-red-400 font-medium' : ''\r\n                        }\r\n                      >\r\n                        {format(dueDate, 'dd MMM yyyy', { locale: es })}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                  {/* Espacio reservado para \"Vencida\" - siempre presente para mantener altura consistente */}\r\n                  <div className=\"h-4 mt-1\">\r\n                    {isOverdue && (\r\n                      <p className=\"text-xs text-red-500 dark:text-red-400\">\r\n                        Vencida\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          );\r\n        })}\r\n      </div>\r\n\r\n      {milestones.length === 0 && (\r\n        <div className=\"text-center text-gray-500 dark:text-gray-400 py-8\">\r\n          No hay tareas definidas para este caso\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;;;AANA;;;;;;AAcO,SAAS,kBAAkB,EAAE,YAAY,iBAAiB,EAAE,MAAM,EAA0B;;IACjG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAE1D,MAAM,kBAAkB,CAAC;QACvB,cAAc,CAAC,OACb,KAAK,GAAG,CAAC,CAAC,YACR,UAAU,EAAE,KAAK,cACb;oBAAE,GAAG,SAAS;oBAAE,WAAW,CAAC,UAAU,SAAS;gBAAC,IAChD;QAIR,yEAAyE;QACzE,MAAM,oBAAoB,WAAW,GAAG,CAAC,CAAC,YACxC,UAAU,EAAE,KAAK,cACb;gBAAE,GAAG,SAAS;gBAAE,WAAW,CAAC,UAAU,SAAS;YAAC,IAChD;QAEN,eAAe,OAAO,CAAC,CAAC,WAAW,EAAE,QAAQ,EAAE,KAAK,SAAS,CAAC;IAChE;IAEA,MAAM,iBAAiB,WAAW,MAAM,CAAC,CAAC,IAAM,EAAE,SAAS,EAAE,MAAM;IACnE,MAAM,qBAAqB,AAAC,iBAAiB,WAAW,MAAM,GAAI;IAElE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAGvE,6LAAC;gCAAK,WAAU;;oCACb;oCAAe;oCAAK,WAAW,MAAM;oCAAC;;;;;;;;;;;;;kCAG3C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,OAAO,GAAG,mBAAmB,CAAC,CAAC;4BAAC;;;;;;;;;;;;;;;;;0BAM/C,6LAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,WAAW;oBAC1B,MAAM,SAAS,UAAU,WAAW,MAAM,GAAG;oBAC7C,MAAM,UAAU,IAAI,KAAK,UAAU,OAAO;oBAC1C,MAAM,YAAY,CAAC,UAAU,SAAS,IAAI,UAAU,IAAI;oBAExD,qBACE,6LAAC;wBAAuB,WAAU;;4BAE/B,CAAC,wBACA,6LAAC;gCACC,WAAW,CAAC,gCAAgC,EAC1C,UAAU,SAAS,GAAG,mCAAmC,gCACzD;;;;;;0CAKN,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,gBAAgB,UAAU,EAAE;wCAC3C,WAAU;kDAET,UAAU,SAAS,iBAClB,6LAAC,8NAAA,CAAA,kBAAoB;4CAAC,WAAU;;;;;iEAEhC,6LAAC,gOAAA,CAAA,kBAAe;4CACd,WAAW,CAAC,QAAQ,EAClB,YAAY,mCAAmC,mCAChD,+DAA+D,CAAC;;;;;;;;;;;kDAKvE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,WAAW,CAAC,oBAAoB,EAC9B,UAAU,SAAS,GACf,kDACA,oCACJ;kEAED,UAAU,KAAK;;;;;;kEAElB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,6LAAC;gEACC,WACE,YAAY,+CAA+C;0EAG5D,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,eAAe;oEAAE,QAAQ,8IAAA,CAAA,KAAE;gEAAC;;;;;;;;;;;;;;;;;;0DAKnD,6LAAC;gDAAI,WAAU;0DACZ,2BACC,6LAAC;oDAAE,WAAU;8DAAyC;;;;;;;;;;;;;;;;;;;;;;;;uBApDtD,UAAU,EAAE;;;;;gBA6D1B;;;;;;YAGD,WAAW,MAAM,KAAK,mBACrB,6LAAC;gBAAI,WAAU;0BAAoD;;;;;;;;;;;;AAM3E;GA3HgB;KAAA", "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/ui/DocumentUpload.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useCallback } from \"react\";\r\nimport { useDropzone } from \"react-dropzone\";\r\nimport { format } from \"date-fns\";\r\nimport { es } from \"date-fns/locale\";\r\nimport { CloudArrowUpIcon, TrashIcon } from \"@heroicons/react/24/outline\";\r\nimport { Document } from \"../../_lib/types\";\r\n\r\ninterface DocumentUploadProps {\r\n  documents: Document[];\r\n  caseId: string;\r\n}\r\n\r\nexport function DocumentUpload({\r\n  documents: initialDocuments,\r\n  caseId,\r\n}: DocumentUploadProps) {\r\n  const [documents, setDocuments] = useState<Document[]>(initialDocuments);\r\n  const [uploading, setUploading] = useState(false);\r\n\r\n  const onDrop = useCallback(\r\n    (acceptedFiles: File[]) => {\r\n      setUploading(true);\r\n\r\n      // Simulate upload delay\r\n      setTimeout(() => {\r\n        const newDocuments: Document[] = acceptedFiles.map((file) => ({\r\n          id: `doc-${crypto.randomUUID()}`,\r\n          name: file.name,\r\n          type: file.type || \"unknown\",\r\n          uploadedAt: new Date().toISOString(),\r\n        }));\r\n\r\n        setDocuments((prev) => [...prev, ...newDocuments]);\r\n\r\n        // Save to sessionStorage (in a real app, this would be sent to an API)\r\n        const allDocuments = [...documents, ...newDocuments];\r\n        sessionStorage.setItem(\r\n          `documents-${caseId}`,\r\n          JSON.stringify(allDocuments)\r\n        );\r\n\r\n        setUploading(false);\r\n      }, 1000);\r\n    },\r\n    [documents, caseId]\r\n  );\r\n\r\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\r\n    onDrop,\r\n    accept: {\r\n      \"application/pdf\": [\".pdf\"],\r\n      \"application/msword\": [\".doc\"],\r\n      \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\":\r\n        [\".docx\"],\r\n      \"image/*\": [\".png\", \".jpg\", \".jpeg\", \".gif\"],\r\n      \"text/*\": [\".txt\"],\r\n    },\r\n    maxSize: 10 * 1024 * 1024, // 10MB\r\n  });\r\n\r\n  const removeDocument = (documentId: string) => {\r\n    setDocuments((prev) => prev.filter((doc) => doc.id !== documentId));\r\n\r\n    // Update sessionStorage\r\n    const updatedDocuments = documents.filter((doc) => doc.id !== documentId);\r\n    sessionStorage.setItem(\r\n      `documents-${caseId}`,\r\n      JSON.stringify(updatedDocuments)\r\n    );\r\n  };\r\n\r\n  const getFileIcon = (type: string) => {\r\n    if (type.includes(\"pdf\")) return \"📄\";\r\n    if (type.includes(\"word\") || type.includes(\"document\")) return \"📝\";\r\n    if (type.includes(\"image\")) return \"🖼️\";\r\n    if (type.includes(\"text\")) return \"📄\";\r\n    return \"📎\";\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6\">\r\n      {/* Upload Area */}\r\n      <div\r\n        {...getRootProps()}\r\n        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${\r\n          isDragActive\r\n            ? \"border-blue-400 bg-blue-50 dark:bg-blue-900/20\"\r\n            : \"border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500\"\r\n        }`}\r\n      >\r\n        <input {...getInputProps()} />\r\n        <CloudArrowUpIcon className=\"h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-4\" />\r\n        {uploading ? (\r\n          <div className=\"space-y-2\">\r\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 dark:border-blue-500 mx-auto\"></div>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">Subiendo archivos...</p>\r\n          </div>\r\n        ) : isDragActive ? (\r\n          <p className=\"text-sm text-blue-600 dark:text-blue-400\">Suelta los archivos aquí...</p>\r\n        ) : (\r\n          <div className=\"space-y-2\">\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Arrastra archivos aquí o haz clic para seleccionar\r\n            </p>\r\n            <p className=\"text-xs text-gray-500 dark:text-gray-500\">\r\n              PDF, DOC, DOCX, imágenes y archivos de texto (máx. 10MB)\r\n            </p>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Documents List */}\r\n      {documents.length > 0 && (\r\n        <div className=\"mt-6\">\r\n          <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-3\">\r\n            Documentos ({documents.length})\r\n          </h4>\r\n          <div className=\"space-y-2\">\r\n            {documents.map((document) => (\r\n              <div\r\n                key={document.id}\r\n                className=\"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\"\r\n              >\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <span className=\"text-lg\">{getFileIcon(document.type)}</span>\r\n                  <div>\r\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                      {document.name}\r\n                    </p>\r\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                      Subido el{\" \"}\r\n                      {format(\r\n                        new Date(document.uploadedAt),\r\n                        \"dd MMM yyyy - HH:mm\",\r\n                        {\r\n                          locale: es,\r\n                        }\r\n                      )}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n                <button\r\n                  onClick={() => removeDocument(document.id)}\r\n                  className=\"text-red-400 hover:text-red-600 dark:hover:text-red-500 p-1 rounded-md hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors\"\r\n                  aria-label=\"Eliminar documento\"\r\n                >\r\n                  <TrashIcon className=\"h-4 w-4\" />\r\n                </button>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {documents.length === 0 && !uploading && (\r\n        <div className=\"mt-6 text-center text-gray-500 dark:text-gray-400 py-4\">\r\n          No hay documentos subidos\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AAAA;;;AANA;;;;;;AAcO,SAAS,eAAe,EAC7B,WAAW,gBAAgB,EAC3B,MAAM,EACc;;IACpB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CACvB,CAAC;YACC,aAAa;YAEb,wBAAwB;YACxB;sDAAW;oBACT,MAAM,eAA2B,cAAc,GAAG;2EAAC,CAAC,OAAS,CAAC;gCAC5D,IAAI,CAAC,IAAI,EAAE,OAAO,UAAU,IAAI;gCAChC,MAAM,KAAK,IAAI;gCACf,MAAM,KAAK,IAAI,IAAI;gCACnB,YAAY,IAAI,OAAO,WAAW;4BACpC,CAAC;;oBAED;8DAAa,CAAC,OAAS;mCAAI;mCAAS;6BAAa;;oBAEjD,uEAAuE;oBACvE,MAAM,eAAe;2BAAI;2BAAc;qBAAa;oBACpD,eAAe,OAAO,CACpB,CAAC,UAAU,EAAE,QAAQ,EACrB,KAAK,SAAS,CAAC;oBAGjB,aAAa;gBACf;qDAAG;QACL;6CACA;QAAC;QAAW;KAAO;IAGrB,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ;YACN,mBAAmB;gBAAC;aAAO;YAC3B,sBAAsB;gBAAC;aAAO;YAC9B,2EACE;gBAAC;aAAQ;YACX,WAAW;gBAAC;gBAAQ;gBAAQ;gBAAS;aAAO;YAC5C,UAAU;gBAAC;aAAO;QACpB;QACA,SAAS,KAAK,OAAO;IACvB;IAEA,MAAM,iBAAiB,CAAC;QACtB,aAAa,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;QAEvD,wBAAwB;QACxB,MAAM,mBAAmB,UAAU,MAAM,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;QAC9D,eAAe,OAAO,CACpB,CAAC,UAAU,EAAE,QAAQ,EACrB,KAAK,SAAS,CAAC;IAEnB;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,KAAK,QAAQ,CAAC,QAAQ,OAAO;QACjC,IAAI,KAAK,QAAQ,CAAC,WAAW,KAAK,QAAQ,CAAC,aAAa,OAAO;QAC/D,IAAI,KAAK,QAAQ,CAAC,UAAU,OAAO;QACnC,IAAI,KAAK,QAAQ,CAAC,SAAS,OAAO;QAClC,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,CAAC,mFAAmF,EAC7F,eACI,mDACA,yFACJ;;kCAEF,6LAAC;wBAAO,GAAG,eAAe;;;;;;kCAC1B,6LAAC,kOAAA,CAAA,mBAAgB;wBAAC,WAAU;;;;;;oBAC3B,0BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;+BAExD,6BACF,6LAAC;wBAAE,WAAU;kCAA2C;;;;;6CAExD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;0CAGxD,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;;;;;;;YAQ7D,UAAU,MAAM,GAAG,mBAClB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAA4D;4BAC3D,UAAU,MAAM;4BAAC;;;;;;;kCAEhC,6LAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAW,YAAY,SAAS,IAAI;;;;;;0DACpD,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEACV,SAAS,IAAI;;;;;;kEAEhB,6LAAC;wDAAE,WAAU;;4DAA2C;4DAC5C;4DACT,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EACJ,IAAI,KAAK,SAAS,UAAU,GAC5B,uBACA;gEACE,QAAQ,8IAAA,CAAA,KAAE;4DACZ;;;;;;;;;;;;;;;;;;;kDAKR,6LAAC;wCACC,SAAS,IAAM,eAAe,SAAS,EAAE;wCACzC,WAAU;wCACV,cAAW;kDAEX,cAAA,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;+BA1BlB,SAAS,EAAE;;;;;;;;;;;;;;;;YAkCzB,UAAU,MAAM,KAAK,KAAK,CAAC,2BAC1B,6LAAC;gBAAI,WAAU;0BAAyD;;;;;;;;;;;;AAMhF;GArJgB;;QAmCwC,2KAAA,CAAA,cAAW;;;KAnCnD", "debugId": null}}, {"offset": {"line": 998, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/cases/JurisprudencePanel.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { format } from \"date-fns\";\r\nimport { es } from \"date-fns/locale\";\r\nimport {\r\n  MagnifyingGlassIcon,\r\n  ScaleIcon,\r\n  ArrowTopRightOnSquareIcon,\r\n} from \"@heroicons/react/24/outline\";\r\nimport { Jurisprudence } from \"../../_lib/types\";\r\n\r\ninterface JurisprudencePanelProps {\r\n  caseType: string;\r\n}\r\n\r\nexport function JurisprudencePanel({ caseType }: JurisprudencePanelProps) {\r\n  const [jurisprudence, setJurisprudence] = useState<Jurisprudence[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [searched, setSearched] = useState(false);\r\n\r\n  const searchJurisprudence = async () => {\r\n    setLoading(true);\r\n\r\n    try {\r\n      // Simulate API delay\r\n      await new Promise((resolve) => setTimeout(resolve, 1500));\r\n\r\n      const response = await fetch(\"/data/jurisprudence.json\");\r\n      const allJurisprudence: Jurisprudence[] = await response.json();\r\n\r\n      // Filter by case type and sort by relevance\r\n      const filtered = allJurisprudence\r\n        .filter((j) => j.caseType === caseType)\r\n        .sort((a, b) => b.relevance - a.relevance)\r\n        .slice(0, 3); // Show top 3 most relevant\r\n\r\n      setJurisprudence(filtered);\r\n      setSearched(true);\r\n    } catch (error) {\r\n      console.error(\"Error loading jurisprudence:\", error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const highlightText = (text: string, highlight: string) => {\r\n    if (!highlight) return text;\r\n\r\n    const parts = text.split(new RegExp(`(${highlight})`, \"gi\"));\r\n    return parts.map((part, index) =>\r\n      part.toLowerCase() === highlight.toLowerCase() ? (\r\n        <mark key={index} className=\"bg-yellow-200 dark:bg-yellow-800 text-yellow-900 dark:text-yellow-200 px-1 rounded\">\r\n          {part}\r\n        </mark>\r\n      ) : (\r\n        part\r\n      )\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6\">\r\n      {/* Search Button */}\r\n      <div className=\"text-center mb-6\">\r\n        <button\r\n          onClick={searchJurisprudence}\r\n          disabled={loading}\r\n          className=\"inline-flex items-center px-6 py-3 bg-blue-600 dark:bg-blue-500 text-white font-medium rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n        >\r\n          {loading ? (\r\n            <>\r\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n              Buscando jurisprudencia...\r\n            </>\r\n          ) : (\r\n            <>\r\n              <MagnifyingGlassIcon className=\"h-5 w-5 mr-2\" />\r\n              Buscar jurisprudencia similar\r\n            </>\r\n          )}\r\n        </button>\r\n        <p className=\"text-sm text-gray-500 dark:text-gray-400 mt-2\">\r\n          Buscar casos similares de tipo: <strong>{caseType}</strong>\r\n        </p>\r\n      </div>\r\n\r\n      {/* Results */}\r\n      {searched && jurisprudence.length > 0 && (\r\n        <div className=\"space-y-4\">\r\n          <h4 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center\">\r\n            <ScaleIcon className=\"h-5 w-5 mr-2 text-blue-600 dark:text-blue-400\" />\r\n            Jurisprudencia Relevante ({jurisprudence.length})\r\n          </h4>\r\n\r\n          {jurisprudence.map((item) => (\r\n            <div\r\n              key={item.id}\r\n              className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:shadow-md dark:hover:shadow-lg transition-shadow bg-white dark:bg-gray-700\"\r\n            >\r\n              <div className=\"flex items-start justify-between mb-2\">\r\n                <h5 className=\"font-medium text-gray-900 dark:text-gray-100 text-sm leading-tight\">\r\n                  {item.title}\r\n                </h5>\r\n                <div className=\"flex items-center text-xs text-gray-500 dark:text-gray-400 ml-4\">\r\n                  <span className=\"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full\">\r\n                    {Math.round(item.relevance * 100)}% relevante\r\n                  </span>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"text-xs text-gray-500 dark:text-gray-400 mb-3 space-x-2\">\r\n                <span>{item.court}</span>\r\n                <span>•</span>\r\n                <span>\r\n                  {format(new Date(item.date), \"dd MMM yyyy\", { locale: es })}\r\n                </span>\r\n              </div>\r\n\r\n              <p className=\"text-sm text-gray-700 dark:text-gray-300 mb-3 leading-relaxed\">\r\n                {highlightText(item.excerpt, \"relación laboral\")}\r\n              </p>\r\n\r\n              <div className=\"flex items-center justify-between\">\r\n                <span className=\"bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 text-xs px-2 py-1 rounded-full\">\r\n                  {item.caseType}\r\n                </span>\r\n                <a\r\n                  href={item.link}\r\n                  className=\"inline-flex items-center text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors\"\r\n                >\r\n                  Ver fallo completo\r\n                  <ArrowTopRightOnSquareIcon className=\"h-3 w-3 ml-1\" />\r\n                </a>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      )}\r\n\r\n      {searched && jurisprudence.length === 0 && !loading && (\r\n        <div className=\"text-center text-gray-500 dark:text-gray-400 py-8\">\r\n          <ScaleIcon className=\"h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4\" />\r\n          <p className=\"text-sm\">\r\n            No se encontró jurisprudencia relevante para casos de tipo{\" \"}\r\n            <strong>{caseType}</strong>\r\n          </p>\r\n        </div>\r\n      )}\r\n\r\n      {!searched && !loading && (\r\n        <div className=\"text-center text-gray-500 dark:text-gray-400 py-8\">\r\n          <ScaleIcon className=\"h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4\" />\r\n          <p className=\"text-sm\">\r\n            Haz clic en el botón para buscar jurisprudencia similar a este caso\r\n          </p>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;;;AALA;;;;;AAgBO,SAAS,mBAAmB,EAAE,QAAQ,EAA2B;;IACtE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,sBAAsB;QAC1B,WAAW;QAEX,IAAI;YACF,qBAAqB;YACrB,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;YAEnD,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,mBAAoC,MAAM,SAAS,IAAI;YAE7D,4CAA4C;YAC5C,MAAM,WAAW,iBACd,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ,KAAK,UAC7B,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS,EACxC,KAAK,CAAC,GAAG,IAAI,2BAA2B;YAE3C,iBAAiB;YACjB,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,CAAC,MAAc;QACnC,IAAI,CAAC,WAAW,OAAO;QAEvB,MAAM,QAAQ,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE;QACtD,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM,QACtB,KAAK,WAAW,OAAO,UAAU,WAAW,mBAC1C,6LAAC;gBAAiB,WAAU;0BACzB;eADQ;;;;uBAIX;IAGN;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAU;kCAET,wBACC;;8CACE,6LAAC;oCAAI,WAAU;;;;;;gCAAuE;;yDAIxF;;8CACE,6LAAC,wOAAA,CAAA,sBAAmB;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;kCAKtD,6LAAC;wBAAE,WAAU;;4BAAgD;0CAC3B,6LAAC;0CAAQ;;;;;;;;;;;;;;;;;;YAK5C,YAAY,cAAc,MAAM,GAAG,mBAClC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,oNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAkD;4BAC5C,cAAc,MAAM;4BAAC;;;;;;;oBAGjD,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;4BAEC,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,KAAK,KAAK;;;;;;sDAEb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;;oDACb,KAAK,KAAK,CAAC,KAAK,SAAS,GAAG;oDAAK;;;;;;;;;;;;;;;;;;8CAKxC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAM,KAAK,KAAK;;;;;;sDACjB,6LAAC;sDAAK;;;;;;sDACN,6LAAC;sDACE,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,IAAI,GAAG,eAAe;gDAAE,QAAQ,8IAAA,CAAA,KAAE;4CAAC;;;;;;;;;;;;8CAI7D,6LAAC;oCAAE,WAAU;8CACV,cAAc,KAAK,OAAO,EAAE;;;;;;8CAG/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDACb,KAAK,QAAQ;;;;;;sDAEhB,6LAAC;4CACC,MAAM,KAAK,IAAI;4CACf,WAAU;;gDACX;8DAEC,6LAAC,oPAAA,CAAA,4BAAyB;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;2BAnCpC,KAAK,EAAE;;;;;;;;;;;YA2CnB,YAAY,cAAc,MAAM,KAAK,KAAK,CAAC,yBAC1C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,6LAAC;wBAAE,WAAU;;4BAAU;4BACsC;0CAC3D,6LAAC;0CAAQ;;;;;;;;;;;;;;;;;;YAKd,CAAC,YAAY,CAAC,yBACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,6LAAC;wBAAE,WAAU;kCAAU;;;;;;;;;;;;;;;;;;AAOjC;GAhJgB;KAAA", "debugId": null}}, {"offset": {"line": 1335, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/cases/CaseContextPanel.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  CheckCircleIcon,\r\n  LightBulbIcon,\r\n  ChatBubbleLeftRightIcon,\r\n  DocumentIcon,\r\n  ScaleIcon,\r\n  ChartBarIcon,\r\n  CpuChipIcon,\r\n} from \"@heroicons/react/24/outline\";\r\nimport { Case } from \"../../_lib/types\";\r\nimport config from \"../../../public/data/config.json\";\r\n\r\ninterface CaseContextPanelProps {\r\n  case: Case;\r\n  onTabChange: (tab: string) => void;\r\n}\r\n\r\n// Removed unused riskColors and complexityColors constants\r\n\r\nexport function CaseContextPanel({\r\n  case: caseData,\r\n  onTabChange,\r\n}: CaseContextPanelProps) {\r\n  // Verificar si es un caso creado por el usuario\r\n  const isUserCreatedCase = () => {\r\n    // Los casos creados por usuario tienen IDs con formato: c-xxxxxxxx (8 caracteres aleatorios)\r\n    // Los casos del JSON tienen IDs como: c-001, c-002, etc.\r\n    const idPattern = /^c-[a-f0-9]{8}$/i;\r\n    return idPattern.test(caseData.id);\r\n  };\r\n\r\n  // Funciones para calcular mini resúmenes\r\n  const getUnreadMessagesCount = () => {\r\n    // Simular mensajes no leídos (en una app real vendría del estado)\r\n    const unreadCount =\r\n      caseData.messages?.filter((msg) => msg.status !== \"read\").length || 0;\r\n    return unreadCount;\r\n  };\r\n\r\n  const getOverdueTasks = () => {\r\n    const now = new Date();\r\n    const overdue =\r\n      caseData.milestones?.filter(\r\n        (milestone) => !milestone.completed && new Date(milestone.dueDate) < now\r\n      ).length || 0;\r\n    return overdue;\r\n  };\r\n\r\n  const getRecentDocuments = () => {\r\n    const threeDaysAgo = new Date();\r\n    threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);\r\n    const recent =\r\n      caseData.documents?.filter(\r\n        (doc) => new Date(doc.uploadedAt) > threeDaysAgo\r\n      ).length || 0;\r\n    return recent;\r\n  };\r\n\r\n  const getRecentActivities = () => {\r\n    const oneDayAgo = new Date();\r\n    oneDayAgo.setDate(oneDayAgo.getDate() - 1);\r\n    const recent =\r\n      caseData.activities?.filter(\r\n        (activity) => new Date(activity.timestamp) > oneDayAgo\r\n      ).length || 0;\r\n    return recent;\r\n  };\r\n\r\n  const getTasksProgress = () => {\r\n    const total = caseData.milestones?.length || 0;\r\n    const completed =\r\n      caseData.milestones?.filter((m) => m.completed).length || 0;\r\n    return { completed, total };\r\n  };\r\n\r\n  // Configuración de elementos de navegación con mini resúmenes\r\n  const unreadMessages = getUnreadMessagesCount();\r\n  const overdueTasks = getOverdueTasks();\r\n  const recentDocs = getRecentDocuments();\r\n  const recentActivities = getRecentActivities();\r\n  const tasksProgress = getTasksProgress();\r\n\r\n  const allNavigationItems = [\r\n    // Solo incluir chat si no es un caso creado por usuario\r\n    ...(!isUserCreatedCase() ? [{\r\n      id: \"chat\",\r\n      label: \"Mensajes\",\r\n      icon: ChatBubbleLeftRightIcon,\r\n      iconColor:\r\n        \"text-blue-600 dark:text-blue-400 group-hover:text-blue-700 dark:group-hover:text-blue-300\",\r\n      value: caseData.messages?.length || 0,\r\n      unit: \"conversaciones\",\r\n      summary:\r\n        unreadMessages > 0 ? `${unreadMessages} sin leer` : \"Todo al día\",\r\n      summaryColor:\r\n        unreadMessages > 0\r\n          ? \"text-red-600 dark:text-red-400\"\r\n          : \"text-green-600 dark:text-green-400\",\r\n    }] : []),\r\n    {\r\n      id: \"tasks\",\r\n      label: \"Tareas\",\r\n      icon: CheckCircleIcon,\r\n      iconColor:\r\n        \"text-green-600 dark:text-green-400 group-hover:text-green-700 dark:group-hover:text-green-300\",\r\n      value: caseData.milestones?.length || 0,\r\n      unit: \"hitos\",\r\n      summary:\r\n        overdueTasks > 0\r\n          ? `${overdueTasks} vencidas`\r\n          : `${tasksProgress.completed}/${tasksProgress.total} completadas`,\r\n      summaryColor:\r\n        overdueTasks > 0\r\n          ? \"text-red-600 dark:text-red-400\"\r\n          : \"text-green-600 dark:text-green-400\",\r\n    },\r\n    {\r\n      id: \"documents\",\r\n      label: \"Documentos\",\r\n      icon: DocumentIcon,\r\n      iconColor:\r\n        \"text-orange-600 dark:text-orange-400 group-hover:text-orange-700 dark:group-hover:text-orange-300\",\r\n      value: caseData.documents?.length || 0,\r\n      unit: \"archivos\",\r\n      summary: recentDocs > 0 ? `${recentDocs} nuevos` : \"Sin cambios\",\r\n      summaryColor:\r\n        recentDocs > 0\r\n          ? \"text-blue-600 dark:text-blue-400\"\r\n          : \"text-gray-500 dark:text-gray-400\",\r\n    },\r\n    {\r\n      id: \"jurisprudence\",\r\n      label: \"Jurisprudencia\",\r\n      icon: ScaleIcon,\r\n      iconColor:\r\n        \"text-indigo-600 dark:text-indigo-400 group-hover:text-indigo-700 dark:group-hover:text-indigo-300\",\r\n      value: \"Ver\",\r\n      unit: \"referencias\",\r\n      summary: \"Buscar casos similares\",\r\n      summaryColor: \"text-indigo-600 dark:text-indigo-400\",\r\n    },\r\n    {\r\n      id: \"history\",\r\n      label: \"Historial\",\r\n      icon: ChartBarIcon,\r\n      iconColor:\r\n        \"text-gray-600 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300\",\r\n      value: caseData.activities?.length || 0,\r\n      unit: \"actividades\",\r\n      summary:\r\n        recentActivities > 0\r\n          ? `${recentActivities} recientes`\r\n          : \"Sin actividad reciente\",\r\n      summaryColor:\r\n        recentActivities > 0\r\n          ? \"text-blue-600 dark:text-blue-400\"\r\n          : \"text-gray-500 dark:text-gray-400\",\r\n    },\r\n    {\r\n      id: \"intelligence\",\r\n      label: \"Inteligencia\",\r\n      icon: CpuChipIcon,\r\n      iconColor:\r\n        \"text-cyan-600 dark:text-cyan-400 group-hover:text-cyan-700 dark:group-hover:text-cyan-300\",\r\n      value: \"IA\",\r\n      unit: \"análisis\",\r\n      summary: `${Math.round(caseData.progress * 100)}% progreso`,\r\n      summaryColor: \"text-cyan-600 dark:text-cyan-400\",\r\n    },\r\n  ];\r\n\r\n  // Filtrar elementos de navegación basado en feature flags - SOLO RENDERIZADO\r\n  const navigationItems = allNavigationItems.filter((item) => {\r\n    if (item.id === \"intelligence\") {\r\n      return config.featureFlags.caseDetails.intelligenceTab.enabled;\r\n    }\r\n    return true;\r\n  });\r\n\r\n  return (\r\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 space-y-6\">\r\n      {/* AI Summary */}\r\n      {((caseData.keyFacts?.length ?? 0) > 0 ||\r\n        (caseData.nextActions?.length ?? 0) > 0) && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6 }}\r\n          className=\"bg-blue-50 dark:bg-blue-900 rounded-lg p-4\"\r\n        >\r\n          <div className=\"flex items-start space-x-3\">\r\n            <LightBulbIcon className=\"h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5\" />\r\n            <div className=\"w-full\">\r\n              <h4 className=\"text-sm font-medium text-blue-900 dark:text-blue-100 mb-4\">\r\n                Resumen IA del Caso\r\n              </h4>\r\n\r\n              {/* Grid layout for Key Facts and Next Actions */}\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                {/* Key Facts */}\r\n                {caseData.keyFacts && caseData.keyFacts.length > 0 && (\r\n                  <div>\r\n                    <h5 className=\"text-xs font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center\">\r\n                      <CheckCircleIcon className=\"h-3 w-3 mr-1\" />\r\n                      Hechos Clave\r\n                    </h5>\r\n                    <ul className=\"space-y-1\">\r\n                      {caseData.keyFacts.map((fact, index) => (\r\n                        <li key={index} className=\"flex items-start space-x-2\">\r\n                          <div className=\"w-1 h-1 bg-blue-600 dark:bg-blue-400 rounded-full mt-2 flex-shrink-0\" />\r\n                          <span className=\"text-xs text-blue-800 dark:text-blue-200 leading-relaxed\">\r\n                            {fact}\r\n                          </span>\r\n                        </li>\r\n                      ))}\r\n                    </ul>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Next Actions */}\r\n                {caseData.nextActions && caseData.nextActions.length > 0 && (\r\n                  <div>\r\n                    <h5 className=\"text-xs font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center\">\r\n                      <LightBulbIcon className=\"h-3 w-3 mr-1\" />\r\n                      Recomendaciones IA\r\n                    </h5>\r\n                    <ul className=\"space-y-1\">\r\n                      {caseData.nextActions.map((action, index) => (\r\n                        <li key={index} className=\"flex items-start space-x-2\">\r\n                          <div className=\"w-1 h-1 bg-blue-600 dark:bg-blue-400 rounded-full mt-2 flex-shrink-0\" />\r\n                          <span className=\"text-xs text-blue-800 dark:text-blue-200 leading-relaxed\">\r\n                            {action}\r\n                          </span>\r\n                        </li>\r\n                      ))}\r\n                    </ul>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n\r\n      {/* Quick Navigation */}\r\n      <div className=\"grid grid-cols-2 lg:grid-cols-3 gap-4\">\r\n        {navigationItems.map((item) => {\r\n          const IconComponent = item.icon;\r\n          return (\r\n            <button\r\n              key={item.id}\r\n              onClick={() => onTabChange(item.id)}\r\n              className=\"bg-gray-50 cursor-pointer dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg p-4 text-left transition-colors group\"\r\n            >\r\n              <div className=\"flex items-center space-x-2 mb-2\">\r\n                <IconComponent className={`h-4 w-4 ${item.iconColor}`} />\r\n                <span className=\"text-xs font-medium text-gray-600 dark:text-gray-300 group-hover:text-gray-700 dark:group-hover:text-gray-200\">\r\n                  {item.label}\r\n                </span>\r\n              </div>\r\n              <div className=\"flex items-baseline space-x-1 mb-1\">\r\n                <span className=\"text-lg font-bold text-gray-900 dark:text-gray-100\">\r\n                  {item.value}\r\n                </span>\r\n                <span className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                  {item.unit}\r\n                </span>\r\n              </div>\r\n              {/* Mini resumen */}\r\n              <div className=\"text-xs font-medium\">\r\n                <span className={item.summaryColor}>{item.summary}</span>\r\n              </div>\r\n            </button>\r\n          );\r\n        })}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAbA;;;;;AAsBO,SAAS,iBAAiB,EAC/B,MAAM,QAAQ,EACd,WAAW,EACW;IACtB,gDAAgD;IAChD,MAAM,oBAAoB;QACxB,6FAA6F;QAC7F,yDAAyD;QACzD,MAAM,YAAY;QAClB,OAAO,UAAU,IAAI,CAAC,SAAS,EAAE;IACnC;IAEA,yCAAyC;IACzC,MAAM,yBAAyB;QAC7B,kEAAkE;QAClE,MAAM,cACJ,SAAS,QAAQ,EAAE,OAAO,CAAC,MAAQ,IAAI,MAAM,KAAK,QAAQ,UAAU;QACtE,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,UACJ,SAAS,UAAU,EAAE,OACnB,CAAC,YAAc,CAAC,UAAU,SAAS,IAAI,IAAI,KAAK,UAAU,OAAO,IAAI,KACrE,UAAU;QACd,OAAO;IACT;IAEA,MAAM,qBAAqB;QACzB,MAAM,eAAe,IAAI;QACzB,aAAa,OAAO,CAAC,aAAa,OAAO,KAAK;QAC9C,MAAM,SACJ,SAAS,SAAS,EAAE,OAClB,CAAC,MAAQ,IAAI,KAAK,IAAI,UAAU,IAAI,cACpC,UAAU;QACd,OAAO;IACT;IAEA,MAAM,sBAAsB;QAC1B,MAAM,YAAY,IAAI;QACtB,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;QACxC,MAAM,SACJ,SAAS,UAAU,EAAE,OACnB,CAAC,WAAa,IAAI,KAAK,SAAS,SAAS,IAAI,WAC7C,UAAU;QACd,OAAO;IACT;IAEA,MAAM,mBAAmB;QACvB,MAAM,QAAQ,SAAS,UAAU,EAAE,UAAU;QAC7C,MAAM,YACJ,SAAS,UAAU,EAAE,OAAO,CAAC,IAAM,EAAE,SAAS,EAAE,UAAU;QAC5D,OAAO;YAAE;YAAW;QAAM;IAC5B;IAEA,8DAA8D;IAC9D,MAAM,iBAAiB;IACvB,MAAM,eAAe;IACrB,MAAM,aAAa;IACnB,MAAM,mBAAmB;IACzB,MAAM,gBAAgB;IAEtB,MAAM,qBAAqB;QACzB,wDAAwD;WACpD,CAAC,sBAAsB;YAAC;gBAC1B,IAAI;gBACJ,OAAO;gBACP,MAAM,gPAAA,CAAA,0BAAuB;gBAC7B,WACE;gBACF,OAAO,SAAS,QAAQ,EAAE,UAAU;gBACpC,MAAM;gBACN,SACE,iBAAiB,IAAI,GAAG,eAAe,SAAS,CAAC,GAAG;gBACtD,cACE,iBAAiB,IACb,mCACA;YACR;SAAE,GAAG,EAAE;QACP;YACE,IAAI;YACJ,OAAO;YACP,MAAM,gOAAA,CAAA,kBAAe;YACrB,WACE;YACF,OAAO,SAAS,UAAU,EAAE,UAAU;YACtC,MAAM;YACN,SACE,eAAe,IACX,GAAG,aAAa,SAAS,CAAC,GAC1B,GAAG,cAAc,SAAS,CAAC,CAAC,EAAE,cAAc,KAAK,CAAC,YAAY,CAAC;YACrE,cACE,eAAe,IACX,mCACA;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,0NAAA,CAAA,eAAY;YAClB,WACE;YACF,OAAO,SAAS,SAAS,EAAE,UAAU;YACrC,MAAM;YACN,SAAS,aAAa,IAAI,GAAG,WAAW,OAAO,CAAC,GAAG;YACnD,cACE,aAAa,IACT,qCACA;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,oNAAA,CAAA,YAAS;YACf,WACE;YACF,OAAO;YACP,MAAM;YACN,SAAS;YACT,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,0NAAA,CAAA,eAAY;YAClB,WACE;YACF,OAAO,SAAS,UAAU,EAAE,UAAU;YACtC,MAAM;YACN,SACE,mBAAmB,IACf,GAAG,iBAAiB,UAAU,CAAC,GAC/B;YACN,cACE,mBAAmB,IACf,qCACA;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,wNAAA,CAAA,cAAW;YACjB,WACE;YACF,OAAO;YACP,MAAM;YACN,SAAS,GAAG,KAAK,KAAK,CAAC,SAAS,QAAQ,GAAG,KAAK,UAAU,CAAC;YAC3D,cAAc;QAChB;KACD;IAED,6EAA6E;IAC7E,MAAM,kBAAkB,mBAAmB,MAAM,CAAC,CAAC;QACjD,IAAI,KAAK,EAAE,KAAK,gBAAgB;YAC9B,OAAO,gGAAA,CAAA,UAAM,CAAC,YAAY,CAAC,WAAW,CAAC,eAAe,CAAC,OAAO;QAChE;QACA,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;YAEZ,CAAC,CAAC,SAAS,QAAQ,EAAE,UAAU,CAAC,IAAI,KACnC,CAAC,SAAS,WAAW,EAAE,UAAU,CAAC,IAAI,CAAC,mBACvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,4NAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAK1E,6LAAC;oCAAI,WAAU;;wCAEZ,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,mBAC/C,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC,gOAAA,CAAA,kBAAe;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAG9C,6LAAC;oDAAG,WAAU;8DACX,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC5B,6LAAC;4DAAe,WAAU;;8EACxB,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAK,WAAU;8EACb;;;;;;;2DAHI;;;;;;;;;;;;;;;;wCAYhB,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG,mBACrD,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC,4NAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAG5C,6LAAC;oDAAG,WAAU;8DACX,SAAS,WAAW,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACjC,6LAAC;4DAAe,WAAU;;8EACxB,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAK,WAAU;8EACb;;;;;;;2DAHI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiB3B,6LAAC;gBAAI,WAAU;0BACZ,gBAAgB,GAAG,CAAC,CAAC;oBACpB,MAAM,gBAAgB,KAAK,IAAI;oBAC/B,qBACE,6LAAC;wBAEC,SAAS,IAAM,YAAY,KAAK,EAAE;wBAClC,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAc,WAAW,CAAC,QAAQ,EAAE,KAAK,SAAS,EAAE;;;;;;kDACrD,6LAAC;wCAAK,WAAU;kDACb,KAAK,KAAK;;;;;;;;;;;;0CAGf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDACb,KAAK,KAAK;;;;;;kDAEb,6LAAC;wCAAK,WAAU;kDACb,KAAK,IAAI;;;;;;;;;;;;0CAId,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAW,KAAK,YAAY;8CAAG,KAAK,OAAO;;;;;;;;;;;;uBApB9C,KAAK,EAAE;;;;;gBAwBlB;;;;;;;;;;;;AAIR;KAnQgB", "debugId": null}}, {"offset": {"line": 1748, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/cases/CaseHistory.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport { format } from \"date-fns\";\r\nimport { es } from \"date-fns/locale\";\r\nimport {\r\n  ClockIcon,\r\n  DocumentIcon,\r\n  ChatBubbleLeftRightIcon,\r\n  CheckCircleIcon,\r\n  ExclamationTriangleIcon,\r\n  ScaleIcon,\r\n} from \"@heroicons/react/24/outline\";\r\nimport { Activity } from \"../../_lib/types\";\r\nimport { useIsMounted } from \"../../_lib/useIsomorphicDate\";\r\n\r\ninterface CaseHistoryProps {\r\n  activities: Activity[];\r\n}\r\n\r\nconst activityIcons = {\r\n  status_change: ExclamationTriangleIcon,\r\n  document_upload: DocumentIcon,\r\n  milestone_completed: CheckCircleIcon,\r\n  message_sent: ChatBubbleLeftRightIcon,\r\n  court_filing: ScaleIcon,\r\n};\r\n\r\nconst activityColors = {\r\n  status_change: \"text-blue-600 bg-blue-50\",\r\n  document_upload: \"text-green-600 bg-green-50\",\r\n  milestone_completed: \"text-purple-600 bg-purple-50\",\r\n  message_sent: \"text-gray-600 bg-gray-50\",\r\n  court_filing: \"text-orange-600 bg-orange-50\",\r\n};\r\n\r\nexport function CaseHistory({ activities }: CaseHistoryProps) {\r\n  const isMounted = useIsMounted();\r\n\r\n  const sortedActivities = [...activities].sort(\r\n    (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()\r\n  );\r\n\r\n  const groupedActivities = sortedActivities.reduce((groups, activity) => {\r\n    const date = isMounted\r\n      ? format(new Date(activity.timestamp), \"yyyy-MM-dd\")\r\n      : \"today\";\r\n    if (!groups[date]) {\r\n      groups[date] = [];\r\n    }\r\n    groups[date].push(activity);\r\n    return groups;\r\n  }, {} as Record<string, Activity[]>);\r\n\r\n  return (\r\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6\">\r\n      <div className=\"flex items-center space-x-2 mb-6\">\r\n        <ClockIcon className=\"h-5 w-5 text-gray-500 dark:text-gray-400\" />\r\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n          Historial del Caso\r\n        </h3>\r\n      </div>\r\n\r\n      <div className=\"space-y-6\">\r\n        {Object.entries(groupedActivities).map(\r\n          ([date, dayActivities], dayIndex) => (\r\n            <motion.div\r\n              key={date}\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: dayIndex * 0.1 }}\r\n            >\r\n              {/* Date Header */}\r\n              <div className=\"flex items-center space-x-3 mb-4\">\r\n                <div className=\"flex-shrink-0\">\r\n                  <div className=\"w-2 h-2 bg-blue-600 dark:bg-blue-500 rounded-full\"></div>\r\n                </div>\r\n                <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                  {isMounted\r\n                    ? format(new Date(date), \"EEEE, dd MMMM yyyy\", {\r\n                        locale: es,\r\n                      })\r\n                    : \"Actividades del día\"}\r\n                </h4>\r\n                <div className=\"flex-1 h-px bg-gray-200 dark:bg-gray-600\"></div>\r\n              </div>\r\n\r\n              {/* Activities for this day */}\r\n              <div className=\"ml-5 space-y-4\">\r\n                {dayActivities.map((activity, activityIndex) => {\r\n                  const IconComponent = activityIcons[activity.type];\r\n                  const colorClasses = activityColors[activity.type];\r\n\r\n                  return (\r\n                    <motion.div\r\n                      key={activity.id}\r\n                      initial={{ opacity: 0, x: -20 }}\r\n                      animate={{ opacity: 1, x: 0 }}\r\n                      transition={{\r\n                        duration: 0.4,\r\n                        delay: dayIndex * 0.1 + activityIndex * 0.05,\r\n                      }}\r\n                      className=\"flex items-start space-x-3\"\r\n                    >\r\n                      <div\r\n                        className={`flex-shrink-0 p-2 rounded-full ${colorClasses}`}\r\n                      >\r\n                        <IconComponent className=\"h-4 w-4\" />\r\n                      </div>\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <p className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n                            {activity.description}\r\n                          </p>\r\n                          <span className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                            {isMounted\r\n                              ? format(new Date(activity.timestamp), \"HH:mm\", {\r\n                                  locale: es,\r\n                                })\r\n                              : \"--:--\"}\r\n                          </span>\r\n                        </div>\r\n                        <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                          {activity.user === \"system\"\r\n                            ? \"Sistema\"\r\n                            : activity.user === \"client\"\r\n                            ? \"Cliente\"\r\n                            : \"Abogado\"}\r\n                        </p>\r\n                      </div>\r\n                    </motion.div>\r\n                  );\r\n                })}\r\n              </div>\r\n            </motion.div>\r\n          )\r\n        )}\r\n      </div>\r\n\r\n      {activities.length === 0 && (\r\n        <div className=\"text-center py-8\">\r\n          <ClockIcon className=\"h-12 w-12 text-gray-300 mx-auto mb-4\" />\r\n          <p className=\"text-sm text-gray-500\">\r\n            No hay actividades registradas para este caso\r\n          </p>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;;;AAdA;;;;;;AAoBA,MAAM,gBAAgB;IACpB,eAAe,gPAAA,CAAA,0BAAuB;IACtC,iBAAiB,0NAAA,CAAA,eAAY;IAC7B,qBAAqB,gOAAA,CAAA,kBAAe;IACpC,cAAc,gPAAA,CAAA,0BAAuB;IACrC,cAAc,oNAAA,CAAA,YAAS;AACzB;AAEA,MAAM,iBAAiB;IACrB,eAAe;IACf,iBAAiB;IACjB,qBAAqB;IACrB,cAAc;IACd,cAAc;AAChB;AAEO,SAAS,YAAY,EAAE,UAAU,EAAoB;;IAC1D,MAAM,YAAY,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE7B,MAAM,mBAAmB;WAAI;KAAW,CAAC,IAAI,CAC3C,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;IAG3E,MAAM,oBAAoB,iBAAiB,MAAM,CAAC,CAAC,QAAQ;QACzD,MAAM,OAAO,YACT,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,SAAS,SAAS,GAAG,gBACrC;QACJ,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;YACjB,MAAM,CAAC,KAAK,GAAG,EAAE;QACnB;QACA,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;QAClB,OAAO;IACT,GAAG,CAAC;IAEJ,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,6LAAC;wBAAG,WAAU;kCAAyD;;;;;;;;;;;;0BAKzE,6LAAC;gBAAI,WAAU;0BACZ,OAAO,OAAO,CAAC,mBAAmB,GAAG,CACpC,CAAC,CAAC,MAAM,cAAc,EAAE,yBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO,WAAW;wBAAI;;0CAGnD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,6LAAC;wCAAG,WAAU;kDACX,YACG,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,sBAAsB;4CAC3C,QAAQ,8IAAA,CAAA,KAAE;wCACZ,KACA;;;;;;kDAEN,6LAAC;wCAAI,WAAU;;;;;;;;;;;;0CAIjB,6LAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,UAAU;oCAC5B,MAAM,gBAAgB,aAAa,CAAC,SAAS,IAAI,CAAC;oCAClD,MAAM,eAAe,cAAc,CAAC,SAAS,IAAI,CAAC;oCAElD,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CACV,UAAU;4CACV,OAAO,WAAW,MAAM,gBAAgB;wCAC1C;wCACA,WAAU;;0DAEV,6LAAC;gDACC,WAAW,CAAC,+BAA+B,EAAE,cAAc;0DAE3D,cAAA,6LAAC;oDAAc,WAAU;;;;;;;;;;;0DAE3B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EACV,SAAS,WAAW;;;;;;0EAEvB,6LAAC;gEAAK,WAAU;0EACb,YACG,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,SAAS,SAAS,GAAG,SAAS;oEAC5C,QAAQ,8IAAA,CAAA,KAAE;gEACZ,KACA;;;;;;;;;;;;kEAGR,6LAAC;wDAAE,WAAU;kEACV,SAAS,IAAI,KAAK,WACf,YACA,SAAS,IAAI,KAAK,WAClB,YACA;;;;;;;;;;;;;uCAhCH,SAAS,EAAE;;;;;gCAqCtB;;;;;;;uBAjEG;;;;;;;;;;YAwEZ,WAAW,MAAM,KAAK,mBACrB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;AAO/C;GAjHgB;;QACI,mIAAA,CAAA,eAAY;;;KADhB", "debugId": null}}, {"offset": {"line": 2030, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/cases/CaseIntelligence.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  ChartBarIcon,\r\n  ClockIcon,\r\n  CurrencyDollarIcon,\r\n  ArrowTrendingUpIcon,\r\n  ArrowTrendingDownIcon,\r\n  ExclamationTriangleIcon,\r\n  LightBulbIcon,\r\n} from \"@heroicons/react/24/outline\";\r\nimport { Case } from \"../../_lib/types\";\r\n\r\ninterface CaseIntelligenceProps {\r\n  case: Case;\r\n}\r\n\r\nexport function CaseIntelligence({ case: caseData }: CaseIntelligenceProps) {\r\n  // Mock data for similar cases comparison\r\n  const similarCasesAverage = {\r\n    duration: 4.2, // months\r\n    successRate: 78,\r\n    averageCost: 180000,\r\n  };\r\n\r\n  const getProgressComparison = () => {\r\n    const expectedProgress = 0.3; // Based on time elapsed\r\n    const actualProgress = caseData.progress;\r\n    const difference = actualProgress - expectedProgress;\r\n\r\n    return {\r\n      difference,\r\n      isAhead: difference > 0,\r\n      percentage: Math.abs(difference * 100),\r\n    };\r\n  };\r\n\r\n  const progressComparison = getProgressComparison();\r\n\r\n  // Use case-specific AI recommendations from nextActions, fallback to generic ones\r\n  const recommendations = caseData.nextActions || [\r\n    \"Acelerar la recopilación de documentos pendientes\",\r\n    \"Programar reunión con el cliente para revisar estrategia\",\r\n    \"Considerar mediación antes de proceder judicialmente\",\r\n    \"Actualizar cálculo de indemnización con últimos precedentes\",\r\n  ];\r\n\r\n  return (\r\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 space-y-6\">\r\n      <div className=\"flex items-center space-x-2 mb-6\">\r\n        <ChartBarIcon className=\"h-5 w-5 text-blue-600 dark:text-blue-400\" />\r\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n          Inteligencia del Caso\r\n        </h3>\r\n      </div>\r\n\r\n      {/* Key Metrics Grid */}\r\n      <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4\">\r\n        {/* Time Tracking */}\r\n        {caseData.timeTracking && (\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4\"\r\n          >\r\n            <div className=\"flex items-center space-x-2 mb-2\">\r\n              <ClockIcon className=\"h-4 w-4 text-blue-600 dark:text-blue-400\" />\r\n              <span className=\"text-xs font-medium text-blue-900 dark:text-blue-200\">\r\n                Tiempo Invertido\r\n              </span>\r\n            </div>\r\n            <div className=\"space-y-1\">\r\n              <div className=\"text-2xl font-bold text-blue-900 dark:text-blue-100\">\r\n                {caseData.timeTracking.totalHours}h\r\n              </div>\r\n              <div className=\"text-xs text-blue-700 dark:text-blue-300\">\r\n                {caseData.timeTracking.billableHours}h facturables\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        )}\r\n\r\n        {/* Success Probability */}\r\n        {caseData.successProbability && (\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.1 }}\r\n            className=\"bg-green-50 dark:bg-green-900/20 rounded-lg p-4\"\r\n          >\r\n            <div className=\"flex items-center space-x-2 mb-2\">\r\n              <ArrowTrendingUpIcon className=\"h-4 w-4 text-green-600 dark:text-green-400\" />\r\n              <span className=\"text-xs font-medium text-green-900 dark:text-green-200\">\r\n                Prob. Éxito\r\n              </span>\r\n            </div>\r\n            <div className=\"space-y-1\">\r\n              <div className=\"text-2xl font-bold text-green-900 dark:text-green-100\">\r\n                {caseData.successProbability}%\r\n              </div>\r\n              <div className=\"text-xs text-green-700 dark:text-green-300\">\r\n                vs {similarCasesAverage.successRate}% promedio\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        )}\r\n\r\n        {/* Progress Comparison */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6, delay: 0.2 }}\r\n          className={`${\r\n            progressComparison.isAhead\r\n              ? \"bg-green-50 dark:bg-green-900/20\"\r\n              : \"bg-yellow-50 dark:bg-yellow-900/20\"\r\n          } rounded-lg p-4`}\r\n        >\r\n          <div className=\"flex items-center space-x-2 mb-2\">\r\n            {progressComparison.isAhead ? (\r\n              <ArrowTrendingUpIcon className=\"h-4 w-4 text-green-600 dark:text-green-400\" />\r\n            ) : (\r\n              <ArrowTrendingDownIcon className=\"h-4 w-4 text-yellow-600 dark:text-yellow-400\" />\r\n            )}\r\n            <span\r\n              className={`text-xs font-medium ${\r\n                progressComparison.isAhead\r\n                  ? \"text-green-900 dark:text-green-200\"\r\n                  : \"text-yellow-900 dark:text-yellow-200\"\r\n              }`}\r\n            >\r\n              Progreso\r\n            </span>\r\n          </div>\r\n          <div className=\"space-y-1\">\r\n            <div\r\n              className={`text-2xl font-bold ${\r\n                progressComparison.isAhead\r\n                  ? \"text-green-900 dark:text-green-100\"\r\n                  : \"text-yellow-900 dark:text-yellow-100\"\r\n              }`}\r\n            >\r\n              {Math.round(caseData.progress * 100)}%\r\n            </div>\r\n            <div\r\n              className={`text-xs ${\r\n                progressComparison.isAhead\r\n                  ? \"text-green-700 dark:text-green-300\"\r\n                  : \"text-yellow-700 dark:text-yellow-300\"\r\n              }`}\r\n            >\r\n              {progressComparison.isAhead ? \"+\" : \"-\"}\r\n              {progressComparison.percentage.toFixed(0)}% vs esperado\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Estimated Value */}\r\n        {caseData.estimatedCost && (\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.3 }}\r\n            className=\"bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4\"\r\n          >\r\n            <div className=\"flex items-center space-x-2 mb-2\">\r\n              <CurrencyDollarIcon className=\"h-4 w-4 text-purple-600 dark:text-purple-400\" />\r\n              <span className=\"text-xs font-medium text-purple-900 dark:text-purple-200\">\r\n                Valor Estimado\r\n              </span>\r\n            </div>\r\n            <div className=\"space-y-1\">\r\n              <div className=\"text-lg font-bold text-purple-900 dark:text-purple-100\">\r\n                {caseData.estimatedCost.split(\" - \")[0]}\r\n              </div>\r\n              <div className=\"text-xs text-purple-700 dark:text-purple-300\">\r\n                Rango estimado\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Recommendations */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.6, delay: 0.4 }}\r\n        className=\"bg-amber-50 dark:bg-amber-900/20 rounded-lg p-4\"\r\n      >\r\n        <div className=\"flex items-center space-x-2 mb-3\">\r\n          <LightBulbIcon className=\"h-5 w-5 text-amber-600 dark:text-amber-400\" />\r\n          <h4 className=\"text-sm font-medium text-amber-900 dark:text-amber-200\">\r\n            Recomendaciones IA\r\n          </h4>\r\n        </div>\r\n        <ul className=\"space-y-2\">\r\n          {recommendations.slice(0, 3).map((recommendation, index) => (\r\n            <li key={index} className=\"flex items-start space-x-2\">\r\n              <div className=\"w-1.5 h-1.5 bg-amber-600 dark:bg-amber-400 rounded-full mt-2 flex-shrink-0\" />\r\n              <span className=\"text-sm text-amber-800 dark:text-amber-200\">\r\n                {recommendation}\r\n              </span>\r\n            </li>\r\n          ))}\r\n        </ul>\r\n      </motion.div>\r\n\r\n      {/* Risk Indicators */}\r\n      {caseData.riskAssessment && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6, delay: 0.5 }}\r\n          className=\"border-t border-gray-200 dark:border-gray-600 pt-4\"\r\n        >\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <ExclamationTriangleIcon className=\"h-4 w-4 text-gray-500 dark:text-gray-400\" />\r\n              <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                Indicadores de Riesgo\r\n              </span>\r\n            </div>\r\n            <span\r\n              className={`px-2 py-1 rounded-full text-xs font-medium ${\r\n                caseData.riskAssessment === \"low\"\r\n                  ? \"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200\"\r\n                  : caseData.riskAssessment === \"medium\"\r\n                  ? \"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200\"\r\n                  : \"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200\"\r\n              }`}\r\n            >\r\n              Riesgo{\" \"}\r\n              {caseData.riskAssessment === \"low\"\r\n                ? \"Bajo\"\r\n                : caseData.riskAssessment === \"medium\"\r\n                ? \"Medio\"\r\n                : \"Alto\"}\r\n            </span>\r\n          </div>\r\n\r\n          <div className=\"mt-3 grid grid-cols-3 gap-4 text-sm\">\r\n            <div className=\"text-center\">\r\n              <div className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n                {caseData.complexityScore}/10\r\n              </div>\r\n              <div className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                Complejidad\r\n              </div>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n                {similarCasesAverage.duration}m\r\n              </div>\r\n              <div className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                Duración promedio\r\n              </div>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n                {caseData.similarCount}\r\n              </div>\r\n              <div className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                Casos similares\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAkBO,SAAS,iBAAiB,EAAE,MAAM,QAAQ,EAAyB;IACxE,yCAAyC;IACzC,MAAM,sBAAsB;QAC1B,UAAU;QACV,aAAa;QACb,aAAa;IACf;IAEA,MAAM,wBAAwB;QAC5B,MAAM,mBAAmB,KAAK,wBAAwB;QACtD,MAAM,iBAAiB,SAAS,QAAQ;QACxC,MAAM,aAAa,iBAAiB;QAEpC,OAAO;YACL;YACA,SAAS,aAAa;YACtB,YAAY,KAAK,GAAG,CAAC,aAAa;QACpC;IACF;IAEA,MAAM,qBAAqB;IAE3B,kFAAkF;IAClF,MAAM,kBAAkB,SAAS,WAAW,IAAI;QAC9C;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,0NAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;kCACxB,6LAAC;wBAAG,WAAU;kCAAyD;;;;;;;;;;;;0BAMzE,6LAAC;gBAAI,WAAU;;oBAEZ,SAAS,YAAY,kBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;wCAAK,WAAU;kDAAuD;;;;;;;;;;;;0CAIzE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CACZ,SAAS,YAAY,CAAC,UAAU;4CAAC;;;;;;;kDAEpC,6LAAC;wCAAI,WAAU;;4CACZ,SAAS,YAAY,CAAC,aAAa;4CAAC;;;;;;;;;;;;;;;;;;;oBAO5C,SAAS,kBAAkB,kBAC1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,wOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;kDAC/B,6LAAC;wCAAK,WAAU;kDAAyD;;;;;;;;;;;;0CAI3E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CACZ,SAAS,kBAAkB;4CAAC;;;;;;;kDAE/B,6LAAC;wCAAI,WAAU;;4CAA6C;4CACtD,oBAAoB,WAAW;4CAAC;;;;;;;;;;;;;;;;;;;kCAO5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAW,GACT,mBAAmB,OAAO,GACtB,qCACA,qCACL,eAAe,CAAC;;0CAEjB,6LAAC;gCAAI,WAAU;;oCACZ,mBAAmB,OAAO,iBACzB,6LAAC,wOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;6DAE/B,6LAAC,4OAAA,CAAA,wBAAqB;wCAAC,WAAU;;;;;;kDAEnC,6LAAC;wCACC,WAAW,CAAC,oBAAoB,EAC9B,mBAAmB,OAAO,GACtB,uCACA,wCACJ;kDACH;;;;;;;;;;;;0CAIH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAW,CAAC,mBAAmB,EAC7B,mBAAmB,OAAO,GACtB,uCACA,wCACJ;;4CAED,KAAK,KAAK,CAAC,SAAS,QAAQ,GAAG;4CAAK;;;;;;;kDAEvC,6LAAC;wCACC,WAAW,CAAC,QAAQ,EAClB,mBAAmB,OAAO,GACtB,uCACA,wCACJ;;4CAED,mBAAmB,OAAO,GAAG,MAAM;4CACnC,mBAAmB,UAAU,CAAC,OAAO,CAAC;4CAAG;;;;;;;;;;;;;;;;;;;oBAM/C,SAAS,aAAa,kBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,sOAAA,CAAA,qBAAkB;wCAAC,WAAU;;;;;;kDAC9B,6LAAC;wCAAK,WAAU;kDAA2D;;;;;;;;;;;;0CAI7E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,SAAS,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;;;;;;kDAEzC,6LAAC;wCAAI,WAAU;kDAA+C;;;;;;;;;;;;;;;;;;;;;;;;0BAStE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;gBACxC,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,4NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CACzB,6LAAC;gCAAG,WAAU;0CAAyD;;;;;;;;;;;;kCAIzE,6LAAC;wBAAG,WAAU;kCACX,gBAAgB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,gBAAgB,sBAChD,6LAAC;gCAAe,WAAU;;kDACxB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDACb;;;;;;;+BAHI;;;;;;;;;;;;;;;;YAWd,SAAS,cAAc,kBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;gBACxC,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gPAAA,CAAA,0BAAuB;wCAAC,WAAU;;;;;;kDACnC,6LAAC;wCAAK,WAAU;kDAAuD;;;;;;;;;;;;0CAIzE,6LAAC;gCACC,WAAW,CAAC,2CAA2C,EACrD,SAAS,cAAc,KAAK,QACxB,sEACA,SAAS,cAAc,KAAK,WAC5B,0EACA,6DACJ;;oCACH;oCACQ;oCACN,SAAS,cAAc,KAAK,QACzB,SACA,SAAS,cAAc,KAAK,WAC5B,UACA;;;;;;;;;;;;;kCAIR,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CACZ,SAAS,eAAe;4CAAC;;;;;;;kDAE5B,6LAAC;wCAAI,WAAU;kDAA2C;;;;;;;;;;;;0CAI5D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CACZ,oBAAoB,QAAQ;4CAAC;;;;;;;kDAEhC,6LAAC;wCAAI,WAAU;kDAA2C;;;;;;;;;;;;0CAI5D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,SAAS,YAAY;;;;;;kDAExB,6LAAC;wCAAI,WAAU;kDAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxE;KA/PgB", "debugId": null}}, {"offset": {"line": 2663, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/modals/DeleteCaseModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport {\n  XMarkIcon,\n  ExclamationTriangleIcon,\n  TrashIcon,\n} from \"@heroicons/react/24/outline\";\nimport * as Dialog from \"@radix-ui/react-dialog\";\nimport { Case } from \"../../_lib/types\";\n\ninterface DeleteCaseModalProps {\n  case: Case;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport function DeleteCaseModal({ case: caseData, isOpen, onClose }: DeleteCaseModalProps) {\n  const [isDeleting, setIsDeleting] = useState(false);\n  const [isConfirmed, setIsConfirmed] = useState(false);\n  const router = useRouter();\n\n  const handleDelete = async () => {\n    if (!isConfirmed) {\n      return;\n    }\n\n    setIsDeleting(true);\n\n    try {\n      // Simular eliminación del caso\n      // En una implementación real, aquí harías la llamada a la API\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      // Redirigir al dashboard después de eliminar\n      router.push(\"/dashboard\");\n      \n      // Mostrar notificación de éxito (opcional)\n      console.log(`Caso \"${caseData.title}\" eliminado exitosamente`);\n      \n    } catch (error) {\n      console.error(\"Error al eliminar el caso:\", error);\n      setIsDeleting(false);\n    }\n  };\n\n  return (\n    <Dialog.Root open={isOpen} onOpenChange={onClose}>\n      <Dialog.Portal>\n        <Dialog.Overlay className=\"fixed inset-0 bg-black/50 dark:bg-black/70 z-50\" />\n        <Dialog.Content className=\"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-xl z-50 w-full max-w-md p-6\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <Dialog.Title className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center\">\n              <ExclamationTriangleIcon className=\"h-6 w-6 text-red-500 mr-2\" />\n              Eliminar Caso\n            </Dialog.Title>\n            <Dialog.Close asChild>\n              <button className=\"text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 cursor-pointer\">\n                <XMarkIcon className=\"h-5 w-5\" />\n              </button>\n            </Dialog.Close>\n          </div>\n\n          <Dialog.Description className=\"sr-only\">\n            Confirmación para eliminar el caso permanentemente\n          </Dialog.Description>\n\n          <div className=\"space-y-4\">\n            <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n              <div className=\"flex items-start\">\n                <ExclamationTriangleIcon className=\"h-5 w-5 text-red-500 dark:text-red-400 mt-0.5 mr-3 flex-shrink-0\" />\n                <div>\n                  <h4 className=\"text-sm font-medium text-red-800 dark:text-red-200 mb-1\">\n                    ¡Advertencia! Esta acción no se puede deshacer\n                  </h4>\n                  <p className=\"text-sm text-red-700 dark:text-red-300\">\n                    Eliminarás permanentemente el caso <strong>&ldquo;{caseData.title}&rdquo;</strong> y todos sus datos asociados:\n                  </p>\n                  <ul className=\"text-sm text-red-700 dark:text-red-300 mt-2 ml-4 list-disc\">\n                    <li>Mensajes y conversaciones</li>\n                    <li>Documentos subidos</li>\n                    <li>Tareas y milestones</li>\n                    <li>Historial de actividades</li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"flex items-center space-x-3 cursor-pointer\">\n                <input\n                  type=\"checkbox\"\n                  checked={isConfirmed}\n                  onChange={(e) => setIsConfirmed(e.target.checked)}\n                  className=\"h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700\"\n                />\n                <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  Entiendo que esta acción no se puede deshacer y quiero eliminar este caso permanentemente\n                </span>\n              </label>\n            </div>\n\n            <div className=\"flex space-x-3 pt-4\">\n              <button\n                onClick={onClose}\n                disabled={isDeleting}\n                className=\"flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                Cancelar\n              </button>\n              <button\n                onClick={handleDelete}\n                disabled={!isConfirmed || isDeleting}\n                className=\"flex-1 px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors cursor-pointer\"\n              >\n                {isDeleting ? (\n                  <div className=\"flex items-center justify-center\">\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                    Eliminando...\n                  </div>\n                ) : (\n                  <div className=\"flex items-center justify-center\">\n                    <TrashIcon className=\"h-4 w-4 mr-2\" />\n                    Eliminar Caso\n                  </div>\n                )}\n              </button>\n            </div>\n          </div>\n        </Dialog.Content>\n      </Dialog.Portal>\n    </Dialog.Root>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAKA;;;AATA;;;;;AAkBO,SAAS,gBAAgB,EAAE,MAAM,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAwB;;IACvF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI,CAAC,aAAa;YAChB;QACF;QAEA,cAAc;QAEd,IAAI;YACF,+BAA+B;YAC/B,8DAA8D;YAC9D,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,6CAA6C;YAC7C,OAAO,IAAI,CAAC;YAEZ,2CAA2C;YAC3C,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,SAAS,KAAK,CAAC,wBAAwB,CAAC;QAE/D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,cAAc;QAChB;IACF;IAEA,qBACE,6LAAC,qKAAA,CAAA,OAAW;QAAC,MAAM;QAAQ,cAAc;kBACvC,cAAA,6LAAC,qKAAA,CAAA,SAAa;;8BACZ,6LAAC,qKAAA,CAAA,UAAc;oBAAC,WAAU;;;;;;8BAC1B,6LAAC,qKAAA,CAAA,UAAc;oBAAC,WAAU;;sCACxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qKAAA,CAAA,QAAY;oCAAC,WAAU;;sDACtB,6LAAC,gPAAA,CAAA,0BAAuB;4CAAC,WAAU;;;;;;wCAA8B;;;;;;;8CAGnE,6LAAC,qKAAA,CAAA,QAAY;oCAAC,OAAO;8CACnB,cAAA,6LAAC;wCAAO,WAAU;kDAChB,cAAA,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAK3B,6LAAC,qKAAA,CAAA,cAAkB;4BAAC,WAAU;sCAAU;;;;;;sCAIxC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,gPAAA,CAAA,0BAAuB;gDAAC,WAAU;;;;;;0DACnC,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA0D;;;;;;kEAGxE,6LAAC;wDAAE,WAAU;;4DAAyC;0EACjB,6LAAC;;oEAAO;oEAAQ,SAAS,KAAK;oEAAC;;;;;;;4DAAgB;;;;;;;kEAEpF,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMZ,6LAAC;8CACC,cAAA,6LAAC;wCAAM,WAAU;;0DACf,6LAAC;gDACC,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,OAAO;gDAChD,WAAU;;;;;;0DAEZ,6LAAC;gDAAK,WAAU;0DAAuD;;;;;;;;;;;;;;;;;8CAM3E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS;4CACT,UAAU;4CACV,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,SAAS;4CACT,UAAU,CAAC,eAAe;4CAC1B,WAAU;sDAET,2BACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;oDAAuE;;;;;;qEAIxF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1D;GApHgB;;QAGC,qIAAA,CAAA,YAAS;;;KAHV", "debugId": null}}, {"offset": {"line": 3009, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/cases/CaseDetailPage.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { format } from \"date-fns\";\r\nimport { es } from \"date-fns/locale\";\r\nimport {\r\n  ChevronLeftIcon,\r\n  ChatBubbleLeftRightIcon,\r\n  CheckCircleIcon,\r\n  DocumentIcon,\r\n  ScaleIcon,\r\n  ChartBarIcon,\r\n  TrashIcon,\r\n} from \"@heroicons/react/24/outline\";\r\nimport * as Tabs from \"@radix-ui/react-tabs\";\r\nimport { Case } from \"../../_lib/types\";\r\nimport { Chat } from \"../ui/Chat\";\r\nimport { MilestoneTimeline } from \"./MilestoneTimeline\";\r\nimport { DocumentUpload } from \"../ui/DocumentUpload\";\r\nimport { JurisprudencePanel } from \"./JurisprudencePanel\";\r\nimport { CaseContextPanel } from \"./CaseContextPanel\";\r\nimport { CaseHistory } from \"./CaseHistory\";\r\n\r\nimport { CaseIntelligence } from \"./CaseIntelligence\";\r\nimport { useIsMounted } from \"../../_lib/useIsomorphicDate\";\r\nimport config from \"../../../public/data/config.json\";\r\nimport { DeleteCaseModal } from \"../modals/DeleteCaseModal\";\r\n\r\ninterface CaseDetailPageProps {\r\n  case: Case;\r\n}\r\n\r\nexport function CaseDetailPage({ case: caseData }: CaseDetailPageProps) {\r\n  const [activeTab, setActiveTab] = useState(\"context\");\r\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\r\n  const isMounted = useIsMounted();\r\n\r\n  // Configuración de tabs\r\n  const allTabs = [\r\n    {\r\n      value: \"context\",\r\n      label: \"Resumen\",\r\n      icon: DocumentIcon,\r\n    },\r\n    {\r\n      value: \"chat\",\r\n      label: \"Chat\",\r\n      icon: ChatBubbleLeftRightIcon,\r\n    },\r\n    {\r\n      value: \"tasks\",\r\n      label: \"Tareas\",\r\n      icon: CheckCircleIcon,\r\n    },\r\n    {\r\n      value: \"documents\",\r\n      label: \"Documentos\",\r\n      icon: DocumentIcon,\r\n    },\r\n    {\r\n      value: \"jurisprudence\",\r\n      label: \"Jurisprudencia\",\r\n      icon: ScaleIcon,\r\n    },\r\n    {\r\n      value: \"history\",\r\n      label: \"Historial\",\r\n      icon: ChartBarIcon,\r\n    },\r\n    {\r\n      value: \"intelligence\",\r\n      label: \"Inteligencia\",\r\n      icon: CheckCircleIcon,\r\n    },\r\n  ];\r\n\r\n  // Verificar si es un caso creado por el usuario\r\n  const isUserCreatedCase = () => {\r\n    // Los casos creados por usuario tienen IDs con formato: c-xxxxxxxx (8 caracteres aleatorios)\r\n    // Los casos del JSON tienen IDs como: c-001, c-002, etc.\r\n    const idPattern = /^c-[a-f0-9]{8}$/i;\r\n    return idPattern.test(caseData.id);\r\n  };\r\n\r\n  // Filtrar tabs basado en feature flags y tipo de caso - SOLO RENDERIZADO\r\n  const tabsConfig = allTabs.filter(tab => {\r\n    if (tab.value === \"intelligence\") {\r\n      return config.featureFlags.caseDetails.intelligenceTab.enabled;\r\n    }\r\n    // Ocultar chat para casos creados por usuario\r\n    if (tab.value === \"chat\" && isUserCreatedCase()) {\r\n      return false;\r\n    }\r\n    return true;\r\n  });\r\n\r\n  const formattedDate = isMounted\r\n    ? format(new Date(caseData.createdAt), \"dd MMM yyyy\", { locale: es })\r\n    : \"Fecha de creación\";\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Breadcrumb */}\r\n      <nav className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\r\n        <Link\r\n          href=\"/dashboard\"\r\n          className=\"flex items-center hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 px-2 py-1 rounded-md transition-all duration-200 cursor-pointer\"\r\n        >\r\n          <ChevronLeftIcon className=\"h-4 w-4 mr-1\" />\r\n          Casos\r\n        </Link>\r\n        <span>/</span>\r\n        <span className=\"text-gray-900 dark:text-gray-100 font-medium\">\r\n          {caseData.title}\r\n        </span>\r\n      </nav>\r\n\r\n      {/* Case Header */}\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6\">\r\n        <div className=\"mb-4\">\r\n          <div className=\"flex items-start justify-between mb-2\">\r\n            <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">\r\n              {caseData.title}\r\n            </h1>\r\n            <button\r\n              onClick={() => setShowDeleteModal(true)}\r\n              className=\"flex items-center px-3 py-2 text-sm font-medium text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md hover:bg-red-100 dark:hover:bg-red-900/30 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer\"\r\n              title=\"Eliminar caso\"\r\n            >\r\n              <TrashIcon className=\"h-4 w-4 mr-2\" />\r\n              Eliminar\r\n            </button>\r\n          </div>\r\n          <div className=\"flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400\">\r\n            <span>Cliente: {caseData.client}</span>\r\n            <span>•</span>\r\n            <span>Tipo: {caseData.type}</span>\r\n            <span>•</span>\r\n            <span>Creado: {formattedDate}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <p className=\"text-gray-700 dark:text-gray-300 mb-4\">\r\n          {caseData.description}\r\n        </p>\r\n\r\n        <div className=\"flex items-center justify-between\">\r\n          <div className=\"flex items-center space-x-4\">\r\n            <span className=\"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium px-2 py-1 rounded-full\">\r\n              {caseData.type}\r\n            </span>\r\n          </div>\r\n          {caseData.similarCount > 0 && (\r\n            <div className=\"flex items-center text-sm text-gray-600 dark:text-gray-400\">\r\n              <ScaleIcon className=\"h-4 w-4 mr-1\" />\r\n              {caseData.similarCount} casos similares\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Tabs */}\r\n      <Tabs.Root value={activeTab} onValueChange={setActiveTab}>\r\n        <Tabs.List className=\"flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg overflow-x-auto\">\r\n          {tabsConfig.map((tab) => {\r\n            const IconComponent = tab.icon;\r\n            return (\r\n              <Tabs.Trigger\r\n                key={tab.value}\r\n                value={tab.value}\r\n                className=\"flex items-center cursor-pointer px-4 py-2 text-sm font-medium rounded-md transition-colors hover:bg-gray-200 dark:hover:bg-gray-600 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 text-gray-600 dark:text-gray-300 whitespace-nowrap\"\r\n              >\r\n                <IconComponent className=\"h-4 w-4 mr-2\" />\r\n                {tab.label}\r\n              </Tabs.Trigger>\r\n            );\r\n          })}\r\n        </Tabs.List>\r\n\r\n        <div className=\"mt-6\">\r\n          <Tabs.Content value=\"context\">\r\n            <CaseContextPanel case={caseData} onTabChange={setActiveTab} />\r\n          </Tabs.Content>\r\n          <Tabs.Content value=\"chat\">\r\n            <Chat messages={caseData.messages} caseId={caseData.id} />\r\n          </Tabs.Content>\r\n          <Tabs.Content value=\"tasks\">\r\n            <MilestoneTimeline\r\n              milestones={caseData.milestones}\r\n              caseId={caseData.id}\r\n            />\r\n          </Tabs.Content>\r\n          <Tabs.Content value=\"documents\">\r\n            <DocumentUpload\r\n              documents={caseData.documents}\r\n              caseId={caseData.id}\r\n            />\r\n          </Tabs.Content>\r\n          <Tabs.Content value=\"jurisprudence\">\r\n            <JurisprudencePanel caseType={caseData.type} />\r\n          </Tabs.Content>\r\n          <Tabs.Content value=\"history\">\r\n            <CaseHistory activities={caseData.activities || []} />\r\n          </Tabs.Content>\r\n          {config.featureFlags.caseDetails.intelligenceTab.enabled && (\r\n            <Tabs.Content value=\"intelligence\">\r\n              <CaseIntelligence case={caseData} />\r\n            </Tabs.Content>\r\n          )}\r\n        </div>\r\n      </Tabs.Root>\r\n\r\n      {/* Modal de eliminación */}\r\n      <DeleteCaseModal\r\n        case={caseData}\r\n        isOpen={showDeleteModal}\r\n        onClose={() => setShowDeleteModal(false)}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;AA3BA;;;;;;;;;;;;;;;;;AAiCO,SAAS,eAAe,EAAE,MAAM,QAAQ,EAAuB;;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,YAAY,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE7B,wBAAwB;IACxB,MAAM,UAAU;QACd;YACE,OAAO;YACP,OAAO;YACP,MAAM,0NAAA,CAAA,eAAY;QACpB;QACA;YACE,OAAO;YACP,OAAO;YACP,MAAM,gPAAA,CAAA,0BAAuB;QAC/B;QACA;YACE,OAAO;YACP,OAAO;YACP,MAAM,gOAAA,CAAA,kBAAe;QACvB;QACA;YACE,OAAO;YACP,OAAO;YACP,MAAM,0NAAA,CAAA,eAAY;QACpB;QACA;YACE,OAAO;YACP,OAAO;YACP,MAAM,oNAAA,CAAA,YAAS;QACjB;QACA;YACE,OAAO;YACP,OAAO;YACP,MAAM,0NAAA,CAAA,eAAY;QACpB;QACA;YACE,OAAO;YACP,OAAO;YACP,MAAM,gOAAA,CAAA,kBAAe;QACvB;KACD;IAED,gDAAgD;IAChD,MAAM,oBAAoB;QACxB,6FAA6F;QAC7F,yDAAyD;QACzD,MAAM,YAAY;QAClB,OAAO,UAAU,IAAI,CAAC,SAAS,EAAE;IACnC;IAEA,yEAAyE;IACzE,MAAM,aAAa,QAAQ,MAAM,CAAC,CAAA;QAChC,IAAI,IAAI,KAAK,KAAK,gBAAgB;YAChC,OAAO,gGAAA,CAAA,UAAM,CAAC,YAAY,CAAC,WAAW,CAAC,eAAe,CAAC,OAAO;QAChE;QACA,8CAA8C;QAC9C,IAAI,IAAI,KAAK,KAAK,UAAU,qBAAqB;YAC/C,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,gBAAgB,YAClB,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,SAAS,SAAS,GAAG,eAAe;QAAE,QAAQ,8IAAA,CAAA,KAAE;IAAC,KACjE;IAEJ,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC,gOAAA,CAAA,kBAAe;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAG9C,6LAAC;kCAAK;;;;;;kCACN,6LAAC;wBAAK,WAAU;kCACb,SAAS,KAAK;;;;;;;;;;;;0BAKnB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX,SAAS,KAAK;;;;;;kDAEjB,6LAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,WAAU;wCACV,OAAM;;0DAEN,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAI1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAK;4CAAU,SAAS,MAAM;;;;;;;kDAC/B,6LAAC;kDAAK;;;;;;kDACN,6LAAC;;4CAAK;4CAAO,SAAS,IAAI;;;;;;;kDAC1B,6LAAC;kDAAK;;;;;;kDACN,6LAAC;;4CAAK;4CAAS;;;;;;;;;;;;;;;;;;;kCAInB,6LAAC;wBAAE,WAAU;kCACV,SAAS,WAAW;;;;;;kCAGvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CACb,SAAS,IAAI;;;;;;;;;;;4BAGjB,SAAS,YAAY,GAAG,mBACvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCACpB,SAAS,YAAY;oCAAC;;;;;;;;;;;;;;;;;;;0BAO/B,6LAAC,mKAAA,CAAA,OAAS;gBAAC,OAAO;gBAAW,eAAe;;kCAC1C,6LAAC,mKAAA,CAAA,OAAS;wBAAC,WAAU;kCAClB,WAAW,GAAG,CAAC,CAAC;4BACf,MAAM,gBAAgB,IAAI,IAAI;4BAC9B,qBACE,6LAAC,mKAAA,CAAA,UAAY;gCAEX,OAAO,IAAI,KAAK;gCAChB,WAAU;;kDAEV,6LAAC;wCAAc,WAAU;;;;;;oCACxB,IAAI,KAAK;;+BALL,IAAI,KAAK;;;;;wBAQpB;;;;;;kCAGF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mKAAA,CAAA,UAAY;gCAAC,OAAM;0CAClB,cAAA,6LAAC,mJAAA,CAAA,mBAAgB;oCAAC,MAAM;oCAAU,aAAa;;;;;;;;;;;0CAEjD,6LAAC,mKAAA,CAAA,UAAY;gCAAC,OAAM;0CAClB,cAAA,6LAAC,oIAAA,CAAA,OAAI;oCAAC,UAAU,SAAS,QAAQ;oCAAE,QAAQ,SAAS,EAAE;;;;;;;;;;;0CAExD,6LAAC,mKAAA,CAAA,UAAY;gCAAC,OAAM;0CAClB,cAAA,6LAAC,oJAAA,CAAA,oBAAiB;oCAChB,YAAY,SAAS,UAAU;oCAC/B,QAAQ,SAAS,EAAE;;;;;;;;;;;0CAGvB,6LAAC,mKAAA,CAAA,UAAY;gCAAC,OAAM;0CAClB,cAAA,6LAAC,8IAAA,CAAA,iBAAc;oCACb,WAAW,SAAS,SAAS;oCAC7B,QAAQ,SAAS,EAAE;;;;;;;;;;;0CAGvB,6LAAC,mKAAA,CAAA,UAAY;gCAAC,OAAM;0CAClB,cAAA,6LAAC,qJAAA,CAAA,qBAAkB;oCAAC,UAAU,SAAS,IAAI;;;;;;;;;;;0CAE7C,6LAAC,mKAAA,CAAA,UAAY;gCAAC,OAAM;0CAClB,cAAA,6LAAC,8IAAA,CAAA,cAAW;oCAAC,YAAY,SAAS,UAAU,IAAI,EAAE;;;;;;;;;;;4BAEnD,gGAAA,CAAA,UAAM,CAAC,YAAY,CAAC,WAAW,CAAC,eAAe,CAAC,OAAO,kBACtD,6LAAC,mKAAA,CAAA,UAAY;gCAAC,OAAM;0CAClB,cAAA,6LAAC,mJAAA,CAAA,mBAAgB;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;0BAOhC,6LAAC,mJAAA,CAAA,kBAAe;gBACd,MAAM;gBACN,QAAQ;gBACR,SAAS,IAAM,mBAAmB;;;;;;;;;;;;AAI1C;GA5LgB;;QAGI,mIAAA,CAAA,eAAY;;;KAHhB", "debugId": null}}, {"offset": {"line": 3502, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/cases/CaseDetailWrapper.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { notFound } from \"next/navigation\";\nimport { CaseDetailPage } from \"./CaseDetailPage\";\nimport { Case } from \"../../_lib/types\";\n\ninterface CaseDetailWrapperProps {\n  caseId: string;\n}\n\nexport function CaseDetailWrapper({ caseId }: CaseDetailWrapperProps) {\n  const [case_, setCase] = useState<Case | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [notFoundCase, setNotFoundCase] = useState(false);\n\n  useEffect(() => {\n    const loadCase = async () => {\n      try {\n        // First, try to load from static JSON data\n        const response = await fetch(\"/data/cases.json\");\n        const staticCases: Case[] = await response.json();\n        const staticCase = staticCases.find((c) => c.id === caseId);\n        \n        if (staticCase) {\n          setCase(staticCase);\n          setLoading(false);\n          return;\n        }\n\n        // If not found in static data, check sessionStorage\n        const localCases = JSON.parse(sessionStorage.getItem(\"cases\") || \"[]\");\n        const localCase = localCases.find((c: Case) => c.id === caseId);\n        \n        if (localCase) {\n          setCase(localCase);\n        } else {\n          setNotFoundCase(true);\n        }\n      } catch (error) {\n        console.error(\"Error loading case:\", error);\n        setNotFoundCase(true);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadCase();\n  }, [caseId]);\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 dark:border-blue-500\"></div>\n      </div>\n    );\n  }\n\n  if (notFoundCase || !case_) {\n    notFound();\n  }\n\n  return <CaseDetailPage case={case_} />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAWO,SAAS,kBAAkB,EAAE,MAAM,EAA0B;;IAClE,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;wDAAW;oBACf,IAAI;wBACF,2CAA2C;wBAC3C,MAAM,WAAW,MAAM,MAAM;wBAC7B,MAAM,cAAsB,MAAM,SAAS,IAAI;wBAC/C,MAAM,aAAa,YAAY,IAAI;+EAAC,CAAC,IAAM,EAAE,EAAE,KAAK;;wBAEpD,IAAI,YAAY;4BACd,QAAQ;4BACR,WAAW;4BACX;wBACF;wBAEA,oDAAoD;wBACpD,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC,YAAY;wBACjE,MAAM,YAAY,WAAW,IAAI;8EAAC,CAAC,IAAY,EAAE,EAAE,KAAK;;wBAExD,IAAI,WAAW;4BACb,QAAQ;wBACV,OAAO;4BACL,gBAAgB;wBAClB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,uBAAuB;wBACrC,gBAAgB;oBAClB,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;sCAAG;QAAC;KAAO;IAEX,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,gBAAgB,CAAC,OAAO;QAC1B,CAAA,GAAA,qIAAA,CAAA,WAAQ,AAAD;IACT;IAEA,qBAAO,6LAAC,iJAAA,CAAA,iBAAc;QAAC,MAAM;;;;;;AAC/B;GApDgB;KAAA", "debugId": null}}, {"offset": {"line": 3599, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/layout/DashboardTabs.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { useSearchParams, useRouter, usePathname } from \"next/navigation\";\r\nimport * as Tabs from \"@radix-ui/react-tabs\";\r\nimport {\r\n  BriefcaseIcon,\r\n  MagnifyingGlassIcon,\r\n  DocumentTextIcon,\r\n} from \"@heroicons/react/24/outline\";\r\n\r\ninterface DashboardTabsProps {\r\n  children?: React.ReactNode;\r\n  defaultTab?: string;\r\n}\r\n\r\nexport function DashboardTabs({ children, defaultTab = \"my-cases\" }: DashboardTabsProps) {\r\n  const searchParams = useSearchParams();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const [activeTab, setActiveTab] = useState(defaultTab);\r\n\r\n  // Determinar el tab activo basado en la ruta actual\r\n  useEffect(() => {\r\n    if (pathname.includes(\"/dashboard/\") && pathname !== \"/dashboard\") {\r\n      // Si estamos en una página de detalle, mantener el tab \"my-cases\"\r\n      setActiveTab(\"my-cases\");\r\n    } else {\r\n      // Si estamos en la página principal del dashboard, usar el tab de la URL\r\n      const tabFromUrl = searchParams.get(\"tab\");\r\n      if (tabFromUrl && (tabFromUrl === \"my-cases\" || tabFromUrl === \"available-cases\" || tabFromUrl === \"templates\")) {\r\n        setActiveTab(tabFromUrl);\r\n      } else {\r\n        setActiveTab(\"my-cases\");\r\n      }\r\n    }\r\n  }, [searchParams, pathname]);\r\n\r\n  // Manejar cambio de tab y actualizar URL\r\n  const handleTabChange = (newTab: string) => {\r\n    setActiveTab(newTab);\r\n    // Navegar a la página principal del dashboard con el tab seleccionado\r\n    const newUrl = newTab === \"my-cases\" ? \"/dashboard\" : `/dashboard?tab=${newTab}`;\r\n    router.push(newUrl);\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-4 sm:space-y-6\">\r\n      <Tabs.Root value={activeTab} onValueChange={handleTabChange}>\r\n        {/* Tabs list with responsive design */}\r\n        <div className=\"w-full overflow-x-auto scrollbar-hide\">\r\n          <Tabs.List className=\"flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg min-w-fit w-full sm:w-auto\">\r\n            <Tabs.Trigger\r\n              value=\"my-cases\"\r\n              className=\"flex items-center cursor-pointer whitespace-nowrap px-2 sm:px-4 py-2 text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md transition-colors hover:bg-gray-200 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 min-w-0 flex-shrink-0\"\r\n            >\r\n              <BriefcaseIcon className=\"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 flex-shrink-0\" />\r\n              <span className=\"hidden sm:inline\">Mis Casos</span>\r\n              <span className=\"sm:hidden\">Casos</span>\r\n            </Tabs.Trigger>\r\n            <Tabs.Trigger\r\n              value=\"available-cases\"\r\n              className=\"flex items-center cursor-pointer whitespace-nowrap px-2 sm:px-4 py-2 text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md transition-colors hover:bg-gray-200 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 min-w-0 flex-shrink-0\"\r\n            >\r\n              <MagnifyingGlassIcon className=\"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 flex-shrink-0\" />\r\n              <span className=\"hidden sm:inline\">Casos Disponibles</span>\r\n              <span className=\"sm:hidden\">Disponibles</span>\r\n            </Tabs.Trigger>\r\n            <Tabs.Trigger\r\n              value=\"templates\"\r\n              className=\"flex items-center cursor-pointer whitespace-nowrap px-2 sm:px-4 py-2 text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md transition-colors hover:bg-gray-200 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 min-w-0 flex-shrink-0\"\r\n            >\r\n              <DocumentTextIcon className=\"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 flex-shrink-0\" />\r\n              <span className=\"hidden sm:inline\">Plantillas</span>\r\n              <span className=\"sm:hidden\">Docs</span>\r\n            </Tabs.Trigger>\r\n          </Tabs.List>\r\n        </div>\r\n\r\n        <div className=\"mt-4 sm:mt-6\">\r\n          {children}\r\n        </div>\r\n      </Tabs.Root>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;;;AALA;;;;;AAgBO,SAAS,cAAc,EAAE,QAAQ,EAAE,aAAa,UAAU,EAAsB;;IACrF,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,oDAAoD;IACpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,SAAS,QAAQ,CAAC,kBAAkB,aAAa,cAAc;gBACjE,kEAAkE;gBAClE,aAAa;YACf,OAAO;gBACL,yEAAyE;gBACzE,MAAM,aAAa,aAAa,GAAG,CAAC;gBACpC,IAAI,cAAc,CAAC,eAAe,cAAc,eAAe,qBAAqB,eAAe,WAAW,GAAG;oBAC/G,aAAa;gBACf,OAAO;oBACL,aAAa;gBACf;YACF;QACF;kCAAG;QAAC;QAAc;KAAS;IAE3B,yCAAyC;IACzC,MAAM,kBAAkB,CAAC;QACvB,aAAa;QACb,sEAAsE;QACtE,MAAM,SAAS,WAAW,aAAa,eAAe,CAAC,eAAe,EAAE,QAAQ;QAChF,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mKAAA,CAAA,OAAS;YAAC,OAAO;YAAW,eAAe;;8BAE1C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,mKAAA,CAAA,OAAS;wBAAC,WAAU;;0CACnB,6LAAC,mKAAA,CAAA,UAAY;gCACX,OAAM;gCACN,WAAU;;kDAEV,6LAAC,4NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,6LAAC;wCAAK,WAAU;kDAAmB;;;;;;kDACnC,6LAAC;wCAAK,WAAU;kDAAY;;;;;;;;;;;;0CAE9B,6LAAC,mKAAA,CAAA,UAAY;gCACX,OAAM;gCACN,WAAU;;kDAEV,6LAAC,wOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;kDAC/B,6LAAC;wCAAK,WAAU;kDAAmB;;;;;;kDACnC,6LAAC;wCAAK,WAAU;kDAAY;;;;;;;;;;;;0CAE9B,6LAAC,mKAAA,CAAA,UAAY;gCACX,OAAM;gCACN,WAAU;;kDAEV,6LAAC,kOAAA,CAAA,mBAAgB;wCAAC,WAAU;;;;;;kDAC5B,6LAAC;wCAAK,WAAU;kDAAmB;;;;;;kDACnC,6LAAC;wCAAK,WAAU;kDAAY;;;;;;;;;;;;;;;;;;;;;;;8BAKlC,6LAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX;GArEgB;;QACO,qIAAA,CAAA,kBAAe;QACrB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAHd", "debugId": null}}]}