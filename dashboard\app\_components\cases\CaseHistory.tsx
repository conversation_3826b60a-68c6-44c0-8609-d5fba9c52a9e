"use client";

import { motion } from "framer-motion";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import {
  ClockIcon,
  DocumentIcon,
  ChatBubbleLeftRightIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ScaleIcon,
} from "@heroicons/react/24/outline";
import { Activity } from "../../_lib/types";
import { useIsMounted } from "../../_lib/useIsomorphicDate";

interface CaseHistoryProps {
  activities: Activity[];
}

const activityIcons = {
  status_change: ExclamationTriangleIcon,
  document_upload: DocumentIcon,
  milestone_completed: CheckCircleIcon,
  message_sent: ChatBubbleLeftRightIcon,
  court_filing: ScaleIcon,
};

const activityColors = {
  status_change: "text-blue-600 bg-blue-50",
  document_upload: "text-green-600 bg-green-50",
  milestone_completed: "text-purple-600 bg-purple-50",
  message_sent: "text-gray-600 bg-gray-50",
  court_filing: "text-orange-600 bg-orange-50",
};

export function CaseHistory({ activities }: CaseHistoryProps) {
  const isMounted = useIsMounted();

  const sortedActivities = [...activities].sort(
    (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );

  const groupedActivities = sortedActivities.reduce((groups, activity) => {
    const date = isMounted
      ? format(new Date(activity.timestamp), "yyyy-MM-dd")
      : "today";
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(activity);
    return groups;
  }, {} as Record<string, Activity[]>);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <ClockIcon className="h-5 w-5 text-gray-500 dark:text-gray-400" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Historial del Caso
        </h3>
      </div>

      <div className="space-y-6">
        {Object.entries(groupedActivities).map(
          ([date, dayActivities], dayIndex) => (
            <motion.div
              key={date}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: dayIndex * 0.1 }}
            >
              {/* Date Header */}
              <div className="flex items-center space-x-3 mb-4">
                <div className="flex-shrink-0">
                  <div className="w-2 h-2 bg-blue-600 dark:bg-blue-500 rounded-full"></div>
                </div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {isMounted
                    ? format(new Date(date), "EEEE, dd MMMM yyyy", {
                        locale: es,
                      })
                    : "Actividades del día"}
                </h4>
                <div className="flex-1 h-px bg-gray-200 dark:bg-gray-600"></div>
              </div>

              {/* Activities for this day */}
              <div className="ml-5 space-y-4">
                {dayActivities.map((activity, activityIndex) => {
                  const IconComponent = activityIcons[activity.type];
                  const colorClasses = activityColors[activity.type];

                  return (
                    <motion.div
                      key={activity.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{
                        duration: 0.4,
                        delay: dayIndex * 0.1 + activityIndex * 0.05,
                      }}
                      className="flex items-start space-x-3"
                    >
                      <div
                        className={`flex-shrink-0 p-2 rounded-full ${colorClasses}`}
                      >
                        <IconComponent className="h-4 w-4" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm text-gray-900 dark:text-gray-100">
                            {activity.description}
                          </p>
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {isMounted
                              ? format(new Date(activity.timestamp), "HH:mm", {
                                  locale: es,
                                })
                              : "--:--"}
                          </span>
                        </div>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          {activity.user === "system"
                            ? "Sistema"
                            : activity.user === "client"
                            ? "Cliente"
                            : "Abogado"}
                        </p>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            </motion.div>
          )
        )}
      </div>

      {activities.length === 0 && (
        <div className="text-center py-8">
          <ClockIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <p className="text-sm text-gray-500">
            No hay actividades registradas para este caso
          </p>
        </div>
      )}
    </div>
  );
}
