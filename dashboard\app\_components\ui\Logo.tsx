interface LogoProps {
  size?: number;
  className?: string;
}

export function Logo({ size = 32, className = "" }: LogoProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`stroke-current ${className}`}
      role="img"
      aria-label="X-Legal Logo"
    >
      {/* Background rectangle with rounded corners for clean geometric design */}
      <rect
        x="1"
        y="1"
        width="30"
        height="30"
        rx="15"
        className="fill-sky-100 dark:fill-slate-800 stroke-sky-200 dark:stroke-slate-700"
        strokeWidth="1"
      />

      {/* Scales of justice using clean geometric shapes */}
      <g strokeLinecap="round" strokeLinejoin="round" strokeWidth="2">
        {/* Central pillar - rectangle for clean geometry */}
        <rect
          x="15"
          y="7"
          width="2"
          height="13"
          className="fill-sky-600 dark:fill-sky-400"
        />

        {/* Base - rectangle */}
        <rect
          x="11"
          y="23"
          width="10"
          height="2"
          className="fill-sky-600 dark:fill-sky-400"
        />

        {/* Horizontal beam - rectangle */}
        <rect
          x="10"
          y="11"
          width="12"
          height="2"
          className="fill-sky-600 dark:fill-sky-400"
        />

        {/* Left scale pan - ellipse for clean geometric design */}
        <ellipse
          cx="10"
          cy="16"
          rx="3"
          ry="1.5"
          className="fill-transparent stroke-sky-600 dark:stroke-sky-400"
        />

        {/* Right scale pan - ellipse for clean geometric design */}
        <ellipse
          cx="22"
          cy="16"
          rx="3"
          ry="1.5"
          className="fill-transparent stroke-sky-600 dark:stroke-sky-400"
        />

        {/* Left chain - simple rectangle */}
        <rect
          x="9.5"
          y="12"
          width="1"
          height="4"
          className="fill-sky-600 dark:fill-sky-400"
        />

        {/* Right chain - simple rectangle */}
        <rect
          x="21.5"
          y="12"
          width="1"
          height="4"
          className="fill-sky-600 dark:fill-sky-400"
        />
      </g>
    </svg>
  );
}
