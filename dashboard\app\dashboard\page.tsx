"use client";

import { CaseList } from "../_components/cases/CaseList";
import { AvailableCases } from "../_components/cases/AvailableCases";
import { TemplatesMarketplace } from "../_components/templates/TemplatesMarketplace";
import { DashboardTabs } from "../_components/layout/DashboardTabs";
import * as Tabs from "@radix-ui/react-tabs";

export default function DashboardPage() {
  return (
    <DashboardTabs>
      <Tabs.Content value="my-cases">
        <CaseList />
      </Tabs.Content>
      <Tabs.Content value="available-cases">
        <AvailableCases />
      </Tabs.Content>
      <Tabs.Content value="templates">
        <TemplatesMarketplace />
      </Tabs.Content>
    </DashboardTabs>
  );
}
