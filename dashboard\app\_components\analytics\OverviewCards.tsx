"use client";

import { motion } from "framer-motion";
import {
  BriefcaseIcon,
  CurrencyDollarIcon,
  StarIcon,
  ClockIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
} from "@heroicons/react/24/outline";
import { AnalyticsOverview } from "../../_lib/types";

interface OverviewCardsProps {
  overview: AnalyticsOverview;
}

export function OverviewCards({ overview }: OverviewCardsProps) {
  const cards = [
    {
      title: "Total de Casos",
      value: overview.totalCases.toString(),
      subtitle: `${overview.activeCases} activos, ${overview.completedCases} completados`,
      icon: BriefcaseIcon,
      color: "blue",
      trend: overview.newCasesThisMonth > 0 ? "up" : "down",
      trendValue: `+${overview.newCasesThisMonth} este mes`,
    },
    {
      title: "Ingresos Totales",
      value: `ARS ${overview.totalRevenue.toLocaleString("es-AR")}`,
      subtitle: `Promedio: ARS ${overview.averageCaseValue.toLocaleString(
        "es-AR"
      )} por caso`,
      icon: CurrencyDollarIcon,
      color: "green",
      trend: "up",
      trendValue: "+15% vs mes anterior",
    },
    {
      title: "Satisfacción del Cliente",
      value: overview.clientSatisfaction.toFixed(1),
      subtitle: "Promedio de calificaciones",
      icon: StarIcon,
      color: "yellow",
      trend: overview.clientSatisfaction >= 4.5 ? "up" : "down",
      trendValue: overview.clientSatisfaction >= 4.5 ? "Excelente" : "Bueno",
    },
    {
      title: "Tiempo de Respuesta",
      value: `${overview.responseTime}h`,
      subtitle: "Promedio de respuesta inicial",
      icon: ClockIcon,
      color: "purple",
      trend: overview.responseTime <= 3 ? "up" : "down",
      trendValue: overview.responseTime <= 3 ? "Muy rápido" : "Mejorable",
    },
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      blue: "bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400",
      green: "bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400",
      yellow: "bg-yellow-50 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400",
      purple: "bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400",
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {cards.map((card, index) => (
        <motion.div
          key={card.title}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: index * 0.1 }}
          className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md dark:hover:shadow-lg transition-shadow"
        >
          <div className="flex items-center justify-between mb-4">
            <div className={`p-2 rounded-lg ${getColorClasses(card.color)}`}>
              <card.icon className="h-6 w-6" />
            </div>
            <div className="flex items-center space-x-1">
              {card.trend === "up" ? (
                <ArrowTrendingUpIcon className="h-4 w-4 text-green-500 dark:text-green-400" />
              ) : (
                <ArrowTrendingDownIcon className="h-4 w-4 text-red-500 dark:text-red-400" />
              )}
              <span
                className={`text-xs font-medium ${
                  card.trend === "up"
                    ? "text-green-600 dark:text-green-400"
                    : "text-red-600 dark:text-red-400"
                }`}
              >
                {card.trendValue}
              </span>
            </div>
          </div>

          <div className="space-y-1">
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-300">{card.title}</h3>
            <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{card.value}</p>
            <p className="text-xs text-gray-500 dark:text-gray-400">{card.subtitle}</p>
          </div>
        </motion.div>
      ))}
    </div>
  );
}
