# 🧪 Guía de Pruebas - Navegación de Casos

## 🎯 Objetivo
Validar que la navegación desde las tarjetas de casos hacia las páginas de detalle funciona perfectamente para casos estáticos y dinámicos.

## 📋 Lista de Verificación

### ✅ **Prueba 1: <PERSON><PERSON>os <PERSON> (JSON)**
1. Ir a `http://localhost:3001/dashboard`
2. Hacer clic en cualquier caso existente (ej: "Despido sin causa – ACME S.A.")
3. **Verificar**: Se abre la página de detalle correctamente
4. **Verificar**: Todas las pestañas funcionan (Resumen, Chat, Tareas, etc.)
5. **Verificar**: Los datos se muestran correctamente

### ✅ **Prueba 2: Crear <PERSON>**
1. En el dashboard, hacer clic en **"Agregar Caso"**
2. Completar el formulario paso a paso:
   ```
   Paso 1 - Información Básica:
   - Tí<PERSON><PERSON>: "Prueba - Accidente de Tránsito"
   - Tip<PERSON>: "Civil"
   - Cliente: "<PERSON>"
   - Prioridad: "Alta"
   
   Paso 2 - Detalles:
   - Descripción: "Accidente de tránsito en intersección. Cliente resultó con lesiones menores. Responsabilidad del tercero clara según testigos."
   - Costo Estimado: "ARS 80,000 - 120,000"
   
   Paso 3 - Tareas:
   - Tarea 1: "Recopilar documentación médica" (Fecha: próxima semana)
   - Tarea 2: "Solicitar parte policial" (Fecha: en 3 días)
   - Tarea 3: "Pericia del vehículo" (Fecha: en 10 días)
   ```
3. **Verificar**: El caso se crea exitosamente
4. **Verificar**: Aparece en la columna "Nuevos"

### ✅ **Prueba 3: Navegación a Caso Nuevo**
1. Hacer clic en la tarjeta del caso recién creado
2. **Verificar**: Se abre la página de detalle
3. **Verificar**: La URL es `/dashboard/c-[id-único]`
4. **Verificar**: No hay errores en la consola

### ✅ **Prueba 4: Datos Completos en Detalle**

#### **Pestaña Resumen:**
- **Verificar**: Título correcto: "Prueba - Accidente de Tránsito"
- **Verificar**: Cliente: "Pedro Martínez"
- **Verificar**: Tipo: "Civil"
- **Verificar**: Prioridad: "Alta" (badge rojo)
- **Verificar**: Descripción completa visible
- **Verificar**: AI Summary generado automáticamente
- **Verificar**: Key Facts con 4 elementos
- **Verificar**: Next Actions con 4 recomendaciones

#### **Pestaña Chat:**
- **Verificar**: Mensaje inicial del abogado presente
- **Verificar**: Contenido: "Hola Pedro, he creado tu caso..."
- **Verificar**: Funcionalidad de chat operativa
- **Verificar**: Se pueden enviar mensajes nuevos

#### **Pestaña Tareas:**
- **Verificar**: 3 milestones creados
- **Verificar**: Títulos correctos de las tareas
- **Verificar**: Fechas asignadas
- **Verificar**: Checkboxes interactivos funcionan

#### **Pestaña Documentos:**
- **Verificar**: Sección vacía pero funcional
- **Verificar**: Área de drag & drop visible
- **Verificar**: Se pueden subir archivos

#### **Pestaña Jurisprudencia:**
- **Verificar**: Búsqueda por tipo "Civil"
- **Verificar**: Casos similares mostrados

#### **Pestaña Historial:**
- **Verificar**: Actividad "Caso creado" registrada
- **Verificar**: Timestamp correcto
- **Verificar**: Usuario: "lawyer"

### ✅ **Prueba 5: Persistencia**
1. Estando en la página de detalle del caso nuevo
2. **Refrescar la página** (F5 o Ctrl+R)
3. **Verificar**: La página se recarga correctamente
4. **Verificar**: Todos los datos siguen presentes
5. **Verificar**: No hay errores

### ✅ **Prueba 6: Navegación Bidireccional**
1. Desde la página de detalle, hacer clic en "← Volver"
2. **Verificar**: Regresa al dashboard
3. **Verificar**: El caso sigue visible en la lista
4. Hacer clic nuevamente en el caso
5. **Verificar**: Se abre la página de detalle otra vez

### ✅ **Prueba 7: Múltiples Casos**
1. Crear un segundo caso con datos diferentes
2. **Verificar**: Ambos casos aparecen en la lista
3. **Verificar**: Se puede navegar a ambos casos
4. **Verificar**: Los datos no se mezclan entre casos

### ✅ **Prueba 8: Caso Inexistente**
1. Ir manualmente a una URL como `/dashboard/c-inexistente`
2. **Verificar**: Se muestra página 404
3. **Verificar**: No hay errores en consola

## 🔧 Verificaciones Técnicas

### **Consola del Navegador:**
- **Verificar**: No hay errores en rojo
- **Verificar**: No hay warnings críticos
- **Verificar**: Las requests a `/data/cases.json` funcionan

### **sessionStorage:**
```javascript
// Abrir DevTools → Application → Session Storage
// Verificar que existen estas claves:

sessionStorage.getItem("cases")           // Array con casos creados
sessionStorage.getItem("chat-c-[id]")     // Mensajes del caso
sessionStorage.getItem("milestones-c-[id]") // Tareas del caso
sessionStorage.getItem("documents-c-[id]")  // Documentos del caso
```

### **Network Tab:**
- **Verificar**: Request a `/data/cases.json` exitoso (200)
- **Verificar**: No hay requests fallidos (404, 500)

## 🎯 Resultados Esperados

### **✅ Éxito Total:**
- Navegación fluida entre dashboard y detalles
- Casos nuevos funcionan igual que casos estáticos
- Todas las pestañas operativas
- Persistencia completa después de refresh
- No hay errores en consola
- sessionStorage se actualiza correctamente

### **❌ Problemas Posibles:**
- **404 en caso nuevo**: Verificar que CaseDetailWrapper está funcionando
- **Datos vacíos**: Verificar que sessionStorage tiene los datos
- **Pestañas no funcionan**: Verificar que el caso tiene estructura completa
- **No persiste**: Verificar sessionStorage y que no se está limpiando

## 🚀 Comandos de Desarrollo

```bash
# Iniciar servidor de desarrollo
npm run dev

# Limpiar sessionStorage (si es necesario)
# En DevTools Console:
sessionStorage.clear()

# Ver casos en sessionStorage
console.log(JSON.parse(sessionStorage.getItem("cases") || "[]"))

# Ver datos específicos de un caso
console.log(sessionStorage.getItem("chat-c-[id]"))
console.log(sessionStorage.getItem("milestones-c-[id]"))
```

## 📊 Métricas de Éxito

- **Navegación**: 100% de casos navegables
- **Funcionalidad**: 100% de pestañas operativas  
- **Persistencia**: 100% de datos conservados
- **Performance**: Carga < 2 segundos
- **UX**: Sin errores visibles al usuario

## 🎉 Validación Final

Si todas las pruebas pasan, la implementación está **100% completa** y lista para:
- ✅ Uso en desarrollo
- ✅ Uso en producción
- ✅ Demostración a clientes
- ✅ Migración de casos reales
