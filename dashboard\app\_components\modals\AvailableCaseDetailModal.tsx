"use client";

import * as Dialog from "@radix-ui/react-dialog";
import {
  XMarkIcon,
  MapPinIcon,
  ClockIcon,
  UserGroupIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  DocumentTextIcon,
  CalendarIcon,
  UserIcon,
} from "@heroicons/react/24/outline";
import { formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";
import { AvailableCase } from "../../_lib/types";
import { useIsMounted } from "../../_lib/useIsomorphicDate";

interface AvailableCaseDetailModalProps {
  case: AvailableCase;
  onClose: () => void;
  onSendProposal: (caseData: AvailableCase) => void;
}

export function AvailableCaseDetailModal({
  case: caseData,
  onClose,
  onSendProposal,
}: AvailableCaseDetailModalProps) {
  const isMounted = useIsMounted();

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case "urgent":
        return ExclamationTriangleIcon;
      case "high":
        return ExclamationTriangleIcon;
      case "medium":
        return ClockIcon;
      case "low":
        return CheckCircleIcon;
      default:
        return ClockIcon;
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case "urgent":
        return "text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20";
      case "high":
        return "text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20";
      case "medium":
        return "text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20";
      case "low":
        return "text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20";
      default:
        return "text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-900/20";
    }
  };

  const getUrgencyText = (urgency: string) => {
    switch (urgency) {
      case "urgent":
        return "Urgente";
      case "high":
        return "Alta";
      case "medium":
        return "Media";
      case "low":
        return "Baja";
      default:
        return "Media";
    }
  };

  const UrgencyIcon = getUrgencyIcon(caseData.urgencyLevel);
  const userBids = JSON.parse(localStorage.getItem("userBids") || "[]");
  const hasBid = userBids.some(
    (bid: { caseId: string }) => bid.caseId === caseData.id
  );

  return (
    <Dialog.Root open onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 z-50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-xl z-50 w-full max-w-4xl p-6 max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between mb-6">
            <Dialog.Title className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              Detalles del Caso
            </Dialog.Title>
            <Dialog.Close asChild>
              <button
                onClick={onClose}
                className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 cursor-pointer"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </Dialog.Close>
          </div>

          <div className="space-y-6">
            {/* Header del caso */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                    {caseData.title}
                  </h2>
                  <div className="flex items-center space-x-4 mb-4">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                      {caseData.type}
                    </span>
                    <div
                      className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getUrgencyColor(
                        caseData.urgencyLevel
                      )}`}
                    >
                      <UrgencyIcon className="h-4 w-4 mr-1" />
                      Urgencia {getUrgencyText(caseData.urgencyLevel)}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-1">
                    {caseData.budgetRange}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-300">
                    Presupuesto estimado
                  </div>
                </div>
              </div>

              {/* Información básica */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center text-gray-600 dark:text-gray-300">
                  <MapPinIcon className="h-4 w-4 mr-2" />
                  {caseData.clientLocation}
                </div>
                <div className="flex items-center text-gray-600 dark:text-gray-300">
                  <UserGroupIcon className="h-4 w-4 mr-2" />
                  {caseData.bidsCount} propuestas
                </div>
                <div className="flex items-center text-gray-600 dark:text-gray-300">
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  {isMounted
                    ? formatDistanceToNow(new Date(caseData.postedAt), {
                        addSuffix: true,
                        locale: es,
                      })
                    : "Hace poco"}
                </div>
              </div>
            </div>

            {/* Descripción */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                <DocumentTextIcon className="h-5 w-5 mr-2" />
                Descripción del Caso
              </h3>
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                {caseData.description}
              </p>
            </div>

            {/* Cliente */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                <UserIcon className="h-5 w-5 mr-2" />
                Información del Cliente
              </h3>
              <div className="bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Cliente:
                    </span>
                    <p className="text-gray-900 dark:text-gray-100">
                      {caseData.clientName}
                    </p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Ubicación:
                    </span>
                    <p className="text-gray-900 dark:text-gray-100">
                      {caseData.clientLocation}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Detalles específicos */}
            {caseData.details && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">
                  Detalles Específicos
                </h3>
                <div className="bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 p-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(caseData.details).map(([key, value]) => (
                      <div key={key}>
                        <span className="text-sm font-medium text-gray-500 dark:text-gray-400 capitalize">
                          {key.replace(/([A-Z])/g, " $1").toLowerCase()}:
                        </span>
                        <p className="text-gray-900 dark:text-gray-100">
                          {typeof value === "boolean" 
                            ? (value ? "Sí" : "No") 
                            : typeof value === "object" 
                            ? JSON.stringify(value)
                            : String(value)}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Acciones */}
            <div className="flex space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={onClose}
                className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors cursor-pointer"
              >
                Cerrar
              </button>
              <button
                onClick={() => onSendProposal(caseData)}
                disabled={hasBid}
                className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                  hasBid
                    ? "bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed"
                    : "bg-blue-600 dark:bg-blue-700 text-white hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 cursor-pointer"
                }`}
              >
                {hasBid ? "Propuesta Enviada" : "Enviar Propuesta"}
              </button>
            </div>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
