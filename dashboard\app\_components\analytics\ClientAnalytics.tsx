"use client";

import { motion } from "framer-motion";
import {
  UserGroupIcon,
  UserPlusIcon,
  CurrencyDollarIcon,
  StarIcon,
  ArrowTrendingUpIcon,
} from "@heroicons/react/24/outline";
import { ClientMetrics } from "../../_lib/types";

interface ClientAnalyticsProps {
  data: ClientMetrics;
}

export function ClientAnalytics({ data }: ClientAnalyticsProps) {
  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="bg-white rounded-lg border border-gray-200 p-6"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-blue-50 rounded-lg">
              <UserGroupIcon className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="font-semibold text-gray-900">Total Clientes</h3>
          </div>
          <div className="text-3xl font-bold text-gray-900">
            {data.totalClients}
          </div>
          <p className="text-sm text-gray-600 mt-1">Cartera activa</p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="bg-white rounded-lg border border-gray-200 p-6"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-green-50 rounded-lg">
              <UserPlusIcon className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="font-semibold text-gray-900">Nuevos Este Mes</h3>
          </div>
          <div className="text-3xl font-bold text-green-600">
            {data.newClientsThisMonth}
          </div>
          <p className="text-sm text-gray-600 mt-1">
            +{Math.round((data.newClientsThisMonth / data.totalClients) * 100)}%
            crecimiento
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-white rounded-lg border border-gray-200 p-6"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-purple-50 rounded-lg">
              <ArrowTrendingUpIcon className="h-6 w-6 text-purple-600" />
            </div>
            <h3 className="font-semibold text-gray-900">Retención</h3>
          </div>
          <div className="text-3xl font-bold text-purple-600">
            {data.clientRetentionRate}%
          </div>
          <p className="text-sm text-gray-600 mt-1">Tasa de retención</p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="bg-white rounded-lg border border-gray-200 p-6"
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-yellow-50 rounded-lg">
              <CurrencyDollarIcon className="h-6 w-6 text-yellow-600" />
            </div>
            <h3 className="font-semibold text-gray-900">Valor Promedio</h3>
          </div>
          <div className="text-2xl font-bold text-yellow-600">
            ARS {data.averageClientValue.toLocaleString("es-AR")}
          </div>
          <p className="text-sm text-gray-600 mt-1">Por cliente</p>
        </motion.div>
      </div>

      {/* Client Retention Visualization */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="bg-white rounded-lg border border-gray-200 p-6"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-6">
          Análisis de Retención de Clientes
        </h3>

        <div className="space-y-4">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Clientes Retenidos</span>
            <span className="font-medium">
              {Math.round((data.clientRetentionRate / 100) * data.totalClients)}{" "}
              de {data.totalClients} clientes
            </span>
          </div>

          <div className="w-full bg-gray-200 rounded-full h-4">
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: `${data.clientRetentionRate}%` }}
              transition={{ duration: 1, delay: 0.5 }}
              className="bg-gradient-to-r from-purple-500 to-blue-500 h-4 rounded-full relative"
            >
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2 text-white text-xs font-medium">
                {data.clientRetentionRate}%
              </div>
            </motion.div>
          </div>

          <div className="grid grid-cols-3 gap-4 mt-6">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-xl font-bold text-green-600">
                {Math.round(
                  (data.clientRetentionRate / 100) * data.totalClients
                )}
              </div>
              <div className="text-sm text-green-800">Clientes Retenidos</div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-xl font-bold text-blue-600">
                {data.newClientsThisMonth}
              </div>
              <div className="text-sm text-blue-800">Nuevos Este Mes</div>
            </div>
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-xl font-bold text-red-600">
                {data.totalClients -
                  Math.round(
                    (data.clientRetentionRate / 100) * data.totalClients
                  )}
              </div>
              <div className="text-sm text-red-800">Clientes Perdidos</div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Top Clients */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.5 }}
        className="bg-white rounded-lg border border-gray-200 p-6"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-6">
          Clientes Principales
        </h3>

        <div className="space-y-4">
          {data.topClients.map((client, index) => (
            <motion.div
              key={client.name}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
              className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                  {client.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">{client.name}</h4>
                  <p className="text-sm text-gray-600">
                    {client.casesCount} caso{client.casesCount !== 1 ? "s" : ""}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-6 text-sm">
                <div className="text-center">
                  <div className="font-semibold text-green-600">
                    ARS {client.totalValue.toLocaleString("es-AR")}
                  </div>
                  <div className="text-gray-500">Valor Total</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-blue-600">
                    ARS{" "}
                    {Math.round(
                      client.totalValue / client.casesCount
                    ).toLocaleString("es-AR")}
                  </div>
                  <div className="text-gray-500">Promedio/Caso</div>
                </div>
                <div className="flex items-center space-x-1">
                  <StarIcon className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="font-semibold text-yellow-600">
                    {client.satisfaction}
                  </span>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Summary Stats */}
        <div className="mt-6 pt-6 border-t border-gray-200 grid grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">
              ARS{" "}
              {data.topClients
                .reduce((sum, client) => sum + client.totalValue, 0)
                .toLocaleString("es-AR")}
            </div>
            <div className="text-sm text-gray-600">Valor Top Clientes</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {data.topClients.reduce(
                (sum, client) => sum + client.casesCount,
                0
              )}
            </div>
            <div className="text-sm text-gray-600">Total Casos</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {(
                data.topClients.reduce(
                  (sum, client) => sum + client.satisfaction,
                  0
                ) / data.topClients.length
              ).toFixed(1)}
            </div>
            <div className="text-sm text-gray-600">Satisfacción Promedio</div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
