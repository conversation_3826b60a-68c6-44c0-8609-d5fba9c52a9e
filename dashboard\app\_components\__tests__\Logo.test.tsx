import { describe, it, expect, beforeEach } from "vitest";
import { render, screen } from "@testing-library/react";
import { Logo } from "../ui/Logo";
import { ThemeProvider } from "../../_contexts/ThemeContext";

function TestApp({
  size,
  className,
}: { size?: number; className?: string } = {}) {
  return (
    <ThemeProvider>
      <Logo size={size} className={className} />
    </ThemeProvider>
  );
}

describe("Logo", () => {
  beforeEach(() => {
    localStorage.clear();
    document.documentElement.className = "";
  });

  it("should render the logo SVG", () => {
    render(<TestApp />);

    const logo = screen.getByRole("img", { name: /x-legal logo/i });
    expect(logo).toBeInTheDocument();
  });

  it("should use default size when no size prop is provided", () => {
    render(<TestApp />);

    const logo = screen.getByRole("img", { name: /x-legal logo/i });
    expect(logo).toHaveAttribute("width", "32");
    expect(logo).toHaveAttribute("height", "32");
  });

  it("should use custom size when size prop is provided", () => {
    render(<TestApp size={48} />);

    const logo = screen.getByRole("img", { name: /x-legal logo/i });
    expect(logo).toHaveAttribute("width", "48");
    expect(logo).toHaveAttribute("height", "48");
  });

  it("should apply custom className when provided", () => {
    render(<TestApp className="custom-logo-class" />);

    const logo = screen.getByRole("img", { name: /x-legal logo/i });
    expect(logo).toHaveClass("custom-logo-class");
  });

  it("should have proper accessibility attributes", () => {
    render(<TestApp />);

    const logo = screen.getByRole("img", { name: /x-legal logo/i });
    expect(logo).toHaveAttribute("aria-label", "X-Legal Logo");
  });

  it("should render scales of justice elements", () => {
    render(<TestApp />);

    const logo = screen.getByRole("img", { name: /x-legal logo/i });
    // Check that the SVG contains the scales of justice geometric elements
    expect(logo.innerHTML).toContain("rect");
    expect(logo.innerHTML).toContain("ellipse");
  });

  it("should support dark theme colors", () => {
    // Set dark theme
    document.documentElement.classList.add("dark");

    render(<TestApp />);

    const logo = screen.getByRole("img", { name: /x-legal logo/i });
    expect(logo).toBeInTheDocument();
    // The component should render without errors in dark mode
  });

  it("should be scalable at different sizes", () => {
    const { rerender } = render(<TestApp size={16} />);

    let logo = screen.getByRole("img", { name: /x-legal logo/i });
    expect(logo).toHaveAttribute("width", "16");
    expect(logo).toHaveAttribute("height", "16");

    rerender(<TestApp size={64} />);

    logo = screen.getByRole("img", { name: /x-legal logo/i });
    expect(logo).toHaveAttribute("width", "64");
    expect(logo).toHaveAttribute("height", "64");
  });

  it("should maintain aspect ratio", () => {
    render(<TestApp size={40} />);

    const logo = screen.getByRole("img", { name: /x-legal logo/i });
    expect(logo).toHaveAttribute("width", "40");
    expect(logo).toHaveAttribute("height", "40");
    expect(logo).toHaveAttribute("viewBox", "0 0 32 32");
  });

  it("should have clean geometric design without decorative elements", () => {
    render(<TestApp />);

    const logo = screen.getByRole("img", { name: /x-legal logo/i });
    // Should use clean geometric shapes (rect, ellipse) without complex paths
    expect(logo.innerHTML).toContain("rect");
    expect(logo.innerHTML).toContain("ellipse");
    // Should not contain complex decorative elements
    expect(logo.innerHTML).not.toContain("polygon");
  });
});
