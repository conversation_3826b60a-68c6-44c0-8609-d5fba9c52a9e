'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { CheckCircleIcon, ClockIcon } from '@heroicons/react/24/outline';
import { CheckCircleIcon as CheckCircleSolidIcon } from '@heroicons/react/24/solid';
import { Milestone } from '../../_lib/types';

interface MilestoneTimelineProps {
  milestones: Milestone[];
  caseId: string;
}

export function MilestoneTimeline({ milestones: initialMilestones, caseId }: MilestoneTimelineProps) {
  const [milestones, setMilestones] = useState<Milestone[]>(initialMilestones);

  const toggleMilestone = (milestoneId: string) => {
    setMilestones((prev) =>
      prev.map((milestone) =>
        milestone.id === milestoneId
          ? { ...milestone, completed: !milestone.completed }
          : milestone
      )
    );

    // In a real app, you would save this to sessionStorage or send to an API
    const updatedMilestones = milestones.map((milestone) =>
      milestone.id === milestoneId
        ? { ...milestone, completed: !milestone.completed }
        : milestone
    );
    sessionStorage.setItem(`milestones-${caseId}`, JSON.stringify(updatedMilestones));
  };

  const completedCount = milestones.filter((m) => m.completed).length;
  const progressPercentage = (completedCount / milestones.length) * 100;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
      {/* Progress Summary */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Progreso del Caso
          </h3>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {completedCount} de {milestones.length} completadas
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className="bg-blue-600 dark:bg-blue-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      </div>

      {/* Timeline */}
      <div className="space-y-4">
        {milestones.map((milestone, index) => {
          const isLast = index === milestones.length - 1;
          const dueDate = new Date(milestone.dueDate);
          const isOverdue = !milestone.completed && dueDate < new Date();

          return (
            <div key={milestone.id} className="relative">
              {/* Timeline line */}
              {!isLast && (
                <div
                  className={`absolute left-4 top-8 w-0.5 h-8 ${
                    milestone.completed ? 'bg-green-300 dark:bg-green-400' : 'bg-gray-300 dark:bg-gray-600'
                  }`}
                />
              )}

              {/* Milestone item */}
              <div className="flex items-start space-x-3">
                <button
                  onClick={() => toggleMilestone(milestone.id)}
                  className="flex-shrink-0 mt-0.5 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full"
                >
                  {milestone.completed ? (
                    <CheckCircleSolidIcon className="h-8 w-8 text-green-500" />
                  ) : (
                    <CheckCircleIcon
                      className={`h-8 w-8 ${
                        isOverdue ? 'text-red-400 dark:text-red-500' : 'text-gray-400 dark:text-gray-500'
                      } hover:text-blue-500 dark:hover:text-blue-400 transition-colors`}
                    />
                  )}
                </button>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4
                      className={`text-sm font-medium ${
                        milestone.completed
                          ? 'text-gray-500 dark:text-gray-400 line-through'
                          : 'text-gray-900 dark:text-gray-100'
                      }`}
                    >
                      {milestone.title}
                    </h4>
                    <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                      <ClockIcon className="h-3 w-3 mr-1" />
                      <span
                        className={
                          isOverdue ? 'text-red-500 dark:text-red-400 font-medium' : ''
                        }
                      >
                        {format(dueDate, 'dd MMM yyyy', { locale: es })}
                      </span>
                    </div>
                  </div>
                  {/* Espacio reservado para "Vencida" - siempre presente para mantener altura consistente */}
                  <div className="h-4 mt-1">
                    {isOverdue && (
                      <p className="text-xs text-red-500 dark:text-red-400">
                        Vencida
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {milestones.length === 0 && (
        <div className="text-center text-gray-500 dark:text-gray-400 py-8">
          No hay tareas definidas para este caso
        </div>
      )}
    </div>
  );
}
