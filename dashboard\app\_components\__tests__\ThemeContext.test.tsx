import { describe, it, expect, beforeEach, vi } from "vitest";
import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
} from "@testing-library/react";
import { ThemeProvider, useTheme } from "../../_contexts/ThemeContext";

// Test component to access theme context
function TestComponent() {
  const { theme, resolvedTheme, setTheme } = useTheme();

  return (
    <div>
      <div data-testid="current-theme">{theme}</div>
      <div data-testid="resolved-theme">{resolvedTheme}</div>
      <button data-testid="set-light" onClick={() => setTheme("light")}>
        Set Light
      </button>
      <button data-testid="set-dark" onClick={() => setTheme("dark")}>
        Set Dark
      </button>
      <button data-testid="set-system" onClick={() => setTheme("system")}>
        Set System
      </button>
    </div>
  );
}

function TestApp() {
  return (
    <ThemeProvider>
      <TestComponent />
    </ThemeProvider>
  );
}

describe("ThemeContext", () => {
  let mockMatchMedia: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
    // Reset document classes
    document.documentElement.className = "";

    // Create a more sophisticated matchMedia mock
    mockMatchMedia = vi.fn().mockImplementation((query) => {
      const isSystemDark = query === "(prefers-color-scheme: dark)";
      return {
        matches: isSystemDark,
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      };
    });
    window.matchMedia = mockMatchMedia;
  });

  // NEW REQUIRED BEHAVIOR TESTS
  describe("System Mode Default Behavior", () => {
    it("should default to system mode when no saved preference exists", async () => {
      render(<TestApp />);

      await waitFor(() => {
        expect(screen.getByTestId("current-theme")).toHaveTextContent("system");
        expect(screen.getByTestId("resolved-theme")).toHaveTextContent("dark");
      });
    });

    it("should detect system dark theme preference on initial load", async () => {
      // Mock system preference for dark mode
      mockMatchMedia.mockImplementation((query: string) => ({
        matches: query === "(prefers-color-scheme: dark)",
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }));

      render(<TestApp />);

      await waitFor(() => {
        expect(screen.getByTestId("current-theme")).toHaveTextContent("system");
        expect(screen.getByTestId("resolved-theme")).toHaveTextContent("dark");
        expect(document.documentElement.classList.contains("dark")).toBe(true);
      });
    });

    it("should detect system light theme preference on initial load", async () => {
      // Mock system preference for light mode
      mockMatchMedia.mockImplementation((query: string) => ({
        matches: false, // System prefers light mode
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }));

      render(<TestApp />);

      await waitFor(() => {
        expect(screen.getByTestId("current-theme")).toHaveTextContent("system");
        expect(screen.getByTestId("resolved-theme")).toHaveTextContent("light");
        expect(document.documentElement.classList.contains("dark")).toBe(false);
      });
    });

    it("should fallback to dark theme when matchMedia is not available", async () => {
      // Mock environment where matchMedia is not available
      window.matchMedia = undefined as unknown as typeof window.matchMedia;

      render(<TestApp />);

      await waitFor(() => {
        expect(screen.getByTestId("current-theme")).toHaveTextContent("system");
        expect(screen.getByTestId("resolved-theme")).toHaveTextContent("dark");
        expect(document.documentElement.classList.contains("dark")).toBe(true);
      });
    });
  });

  describe("Manual Theme Override Behavior", () => {
    it("should override system preference when light theme is selected", async () => {
      // Start with system dark preference
      mockMatchMedia.mockImplementation((query: string) => ({
        matches: query === "(prefers-color-scheme: dark)",
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }));

      render(<TestApp />);

      // Verify initial system dark mode
      await waitFor(() => {
        expect(screen.getByTestId("current-theme")).toHaveTextContent("system");
        expect(screen.getByTestId("resolved-theme")).toHaveTextContent("dark");
      });

      // Override with light theme
      const setLightButton = screen.getByTestId("set-light");
      fireEvent.click(setLightButton);

      await waitFor(() => {
        expect(screen.getByTestId("current-theme")).toHaveTextContent("light");
        expect(screen.getByTestId("resolved-theme")).toHaveTextContent("light");
        expect(document.documentElement.classList.contains("dark")).toBe(false);
      });
    });

    it("should override system preference when dark theme is selected", async () => {
      // Start with system light preference
      mockMatchMedia.mockImplementation((query: string) => ({
        matches: false, // System prefers light
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }));

      render(<TestApp />);

      // Verify initial system light mode
      await waitFor(() => {
        expect(screen.getByTestId("current-theme")).toHaveTextContent("system");
        expect(screen.getByTestId("resolved-theme")).toHaveTextContent("light");
      });

      // Override with dark theme
      const setDarkButton = screen.getByTestId("set-dark");
      fireEvent.click(setDarkButton);

      await waitFor(() => {
        expect(screen.getByTestId("current-theme")).toHaveTextContent("dark");
        expect(screen.getByTestId("resolved-theme")).toHaveTextContent("dark");
        expect(document.documentElement.classList.contains("dark")).toBe(true);
      });
    });

    it("should return to system mode when explicitly selected", async () => {
      render(<TestApp />);

      // First override with light theme
      const setLightButton = screen.getByTestId("set-light");
      fireEvent.click(setLightButton);

      await waitFor(() => {
        expect(screen.getByTestId("current-theme")).toHaveTextContent("light");
      });

      // Then return to system mode
      const setSystemButton = screen.getByTestId("set-system");
      fireEvent.click(setSystemButton);

      await waitFor(() => {
        expect(screen.getByTestId("current-theme")).toHaveTextContent("system");
        expect(screen.getByTestId("resolved-theme")).toHaveTextContent("dark");
        expect(document.documentElement.classList.contains("dark")).toBe(true);
      });
    });
  });

  describe("System Theme Change Detection", () => {
    it("should respond to system theme changes when in system mode", async () => {
      let mediaQueryCallback: (() => void) | undefined;
      let currentMatches = true; // Start with dark theme

      // Mock matchMedia with ability to trigger change events
      const mockMediaQuery = {
        get matches() {
          return currentMatches;
        },
        media: "(prefers-color-scheme: dark)",
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn((event, callback) => {
          if (event === "change") {
            mediaQueryCallback = callback;
          }
        }),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      };

      mockMatchMedia.mockReturnValue(mockMediaQuery);

      render(<TestApp />);

      // Should start in system mode with dark theme
      await waitFor(() => {
        expect(screen.getByTestId("current-theme")).toHaveTextContent("system");
        expect(screen.getByTestId("resolved-theme")).toHaveTextContent("dark");
      });

      // Simulate system theme change to light
      currentMatches = false; // Now system prefers light

      // Trigger the change event
      if (mediaQueryCallback) {
        act(() => {
          mediaQueryCallback!();
        });
      }

      await waitFor(() => {
        expect(screen.getByTestId("current-theme")).toHaveTextContent("system");
        expect(screen.getByTestId("resolved-theme")).toHaveTextContent("light");
        expect(document.documentElement.classList.contains("dark")).toBe(false);
      });
    });

    it("should not respond to system theme changes when manual theme is selected", async () => {
      let mediaQueryCallback: (() => void) | undefined;
      let currentMatches = true; // Start with dark theme

      const mockMediaQuery = {
        get matches() {
          return currentMatches;
        },
        media: "(prefers-color-scheme: dark)",
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn((event, callback) => {
          if (event === "change") {
            mediaQueryCallback = callback;
          }
        }),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      };

      mockMatchMedia.mockReturnValue(mockMediaQuery);

      render(<TestApp />);

      // Override with light theme
      const setLightButton = screen.getByTestId("set-light");
      fireEvent.click(setLightButton);

      await waitFor(() => {
        expect(screen.getByTestId("current-theme")).toHaveTextContent("light");
        expect(screen.getByTestId("resolved-theme")).toHaveTextContent("light");
      });

      // Simulate system theme change - should not affect manual selection
      currentMatches = false; // System changes to light, but shouldn't matter
      if (mediaQueryCallback) {
        act(() => {
          mediaQueryCallback!();
        });
      }

      await waitFor(() => {
        expect(screen.getByTestId("current-theme")).toHaveTextContent("light");
        expect(screen.getByTestId("resolved-theme")).toHaveTextContent("light");
        expect(document.documentElement.classList.contains("dark")).toBe(false);
      });
    });
  });

  describe("Theme Persistence", () => {
    it("should persist system mode preference in localStorage", async () => {
      render(<TestApp />);

      await waitFor(() => {
        expect(localStorage.setItem).toHaveBeenCalledWith("theme", "system");
      });
    });

    it("should persist manual theme selection in localStorage", async () => {
      render(<TestApp />);

      const setLightButton = screen.getByTestId("set-light");
      fireEvent.click(setLightButton);

      await waitFor(() => {
        expect(localStorage.setItem).toHaveBeenCalledWith("theme", "light");
      });
    });

    it("should load saved theme preference from localStorage on mount", async () => {
      // Mock localStorage to return light theme
      localStorage.getItem = vi.fn().mockReturnValue("light");

      render(<TestApp />);

      await waitFor(() => {
        expect(screen.getByTestId("current-theme")).toHaveTextContent("light");
        expect(screen.getByTestId("resolved-theme")).toHaveTextContent("light");
        expect(document.documentElement.classList.contains("dark")).toBe(false);
      });
    });

    it("should load saved system preference from localStorage and detect current system theme", async () => {
      // Mock localStorage to return system theme
      localStorage.getItem = vi.fn().mockReturnValue("system");

      // Mock system preference for light mode
      mockMatchMedia.mockImplementation((query: string) => ({
        matches: false, // System currently prefers light
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }));

      render(<TestApp />);

      await waitFor(() => {
        expect(screen.getByTestId("current-theme")).toHaveTextContent("system");
        expect(screen.getByTestId("resolved-theme")).toHaveTextContent("light");
        expect(document.documentElement.classList.contains("dark")).toBe(false);
      });
    });
  });
});
