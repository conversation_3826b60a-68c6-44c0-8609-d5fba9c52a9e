// Configuración simple para feature flags
interface Config {
  featureFlags: {
    intelligenceTab: boolean;
    [key: string]: unknown;
  };
}

let config: Config | null = null;

export async function getConfig() {
  if (!config) {
    try {
      const response = await fetch("/data/config.json");
      config = await response.json();
    } catch (error) {
      console.error("Error loading config:", error);
      // Valores por defecto
      config = {
        featureFlags: {
          intelligenceTab: true,
        },
      };
    }
  }
  return config;
}

export function isFeatureEnabled(_feature: string): boolean {
  // Para uso en el servidor o cuando no hay config cargado
  return true; // Por defecto habilitado
}
