# 🏛️ X-Legal Dashboard

> Plataforma legal inteligente para abogados - Dashboard de gestión de casos

## ✨ Características

- 📋 **Gestión de Casos**: Kanban board para visualizar casos por estado
- 💬 **Chat Integrado**: Comunicación directa con clientes estilo WhatsApp
- 📊 **Timeline de Tareas**: Seguimiento visual del progreso de cada caso
- 📁 **Gestión de Documentos**: Upload y organización de archivos
- ⚖️ **Búsqueda de Jurisprudencia**: IA para encontrar casos similares
- 🏪 **Marketplace de Plantillas**: Compra y descarga de documentos legales
- 📈 **Analytics Dashboard**: Métricas y reportes de rendimiento
- 📱 **Responsive Design**: Optimizado para desktop y mobile
- 🌙 **Tema Oscuro**: Modo oscuro por defecto con soporte completo
- 🎨 **UI Profesional**: Diseño sobrio y accesible con Tailwind CSS
- ⚖️ **Logo Profesional**: Identidad visual con escalas de justicia

## 🚀 Inicio Rápido

### Instalación

```bash
npm install
```

### Desarrollo

```bash
npm run dev
```

Abre [http://localhost:3000](http://localhost:3000) en tu navegador.

### Build para Producción

```bash
npm run build
npm start
```

## 🛠️ Stack Tecnológico

- **Framework**: Next.js 15 (App Router)
- **Lenguaje**: TypeScript
- **Estilos**: Tailwind CSS + Typography plugin
- **Componentes**: Radix UI + Heroicons
- **Testing**: Vitest + @testing-library/react
- **Drag & Drop**: @dnd-kit
- **Fechas**: date-fns (localización es-AR)
- **Animaciones**: Framer Motion
- **Upload**: react-dropzone

## 📁 Estructura del Proyecto

```
dashboard/
├── app/
│   ├── _components/          # Componentes organizados por funcionalidad
│   │   ├── layout/           # Navegación y estructura UI
│   │   │   ├── Sidebar.tsx   # Navegación lateral con logo
│   │   │   ├── TopBar.tsx    # Barra superior
│   │   │   ├── AvatarDropdown.tsx # Menú de usuario
│   │   │   └── NotificationDropdown.tsx # Notificaciones
│   │   ├── cases/            # Gestión de casos
│   │   │   ├── CaseList.tsx  # Lista kanban de casos
│   │   │   ├── CaseCard.tsx  # Tarjeta individual de caso
│   │   │   ├── CaseDetailPage.tsx # Página de detalle
│   │   │   ├── AvailableCases.tsx # Casos disponibles
│   │   │   └── MilestoneTimeline.tsx # Timeline de tareas
│   │   ├── templates/        # Marketplace de plantillas
│   │   │   ├── TemplatesMarketplace.tsx # Página principal
│   │   │   ├── TemplateCard.tsx # Tarjeta de plantilla
│   │   │   └── MyTemplatesSection.tsx # Mis plantillas
│   │   ├── analytics/        # Dashboard de métricas
│   │   │   ├── AnalyticsDashboard.tsx # Dashboard principal
│   │   │   ├── CasesChart.tsx # Gráfico de casos
│   │   │   └── RevenueChart.tsx # Gráfico de ingresos
│   │   ├── ui/               # Componentes reutilizables
│   │   │   ├── Chat.tsx      # Chat con cliente
│   │   │   └── DocumentUpload.tsx # Gestión de documentos
│   │   └── modals/           # Diálogos modales
│   │       ├── BidModal.tsx  # Modal de propuestas
│   │       └── PurchaseModal.tsx # Modal de compra
│   ├── _contexts/            # Contextos React
│   │   └── ThemeContext.tsx  # Gestión de tema oscuro/claro
│   ├── _lib/
│   │   └── types.ts          # Tipos TypeScript
│   ├── dashboard/
│   │   ├── page.tsx          # Lista de casos
│   │   ├── templates/        # Marketplace de plantillas
│   │   ├── analytics/        # Dashboard de analytics
│   │   └── [caseId]/
│   │       └── page.tsx      # Detalle de caso
│   ├── layout.tsx            # Layout principal
│   └── page.tsx              # Página de inicio
├── public/
│   └── data/                 # Mock data JSON
│       ├── cases.json        # Datos de casos
│       ├── templates.json    # Plantillas disponibles
│       └── jurisprudence.json # Base de jurisprudencia
└── tailwind.config.ts        # Configuración Tailwind
```

## 📊 Mock Data

El dashboard utiliza datos simulados almacenados en JSON:

- **Casos**: 3 casos de ejemplo (Laboral, Civil, Familia)
- **Estados**: Nuevos, En Curso, Cerrados
- **Jurisprudencia**: Base de datos con fallos relevantes

## 🎯 Funcionalidades Implementadas

### ✅ Completadas

- [x] Sidebar colapsible con persistencia en localStorage
- [x] Logo profesional con escalas de justicia (SVG responsivo)
- [x] TopBar con notificaciones y perfil de abogado
- [x] Kanban board para visualización de casos
- [x] Tarjetas de caso con información completa
- [x] Página de detalle con tabs (Chat, Tareas, Documentos, Jurisprudencia)
- [x] Chat funcional con persistencia local
- [x] Timeline de tareas con checkboxes interactivos
- [x] Upload de documentos con preview
- [x] Búsqueda de jurisprudencia por tipo de caso
- [x] Marketplace de plantillas con compra/descarga
- [x] Dashboard de analytics con métricas
- [x] Casos disponibles para aplicar
- [x] Tema oscuro por defecto con soporte completo
- [x] Componentes organizados en estructura modular
- [x] Testing con Vitest y @testing-library/react
- [x] Responsive design mobile-first
- [x] Fechas formateadas en español argentino

### 🔄 Próximas Mejoras

- [ ] Autenticación de usuarios
- [ ] API backend real
- [ ] Notificaciones push
- [ ] Exportación de reportes
- [ ] Integración con calendario
- [ ] PWA (Progressive Web App)
- [ ] Optimización de performance

## 🚀 Deploy en Vercel

1. Conecta tu repositorio a Vercel
2. Configura las variables de entorno si es necesario
3. Deploy automático en cada push a main

```bash
npm run build && vercel
```

## 🧪 Testing

Para ejecutar los tests:

```bash
npm run test
```

## 📝 Licencia

© 2025 X-Legal SRL - Todos los derechos reservados
