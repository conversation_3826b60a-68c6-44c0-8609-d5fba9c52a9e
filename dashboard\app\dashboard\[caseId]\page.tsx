import { notFound } from "next/navigation";
import { readFile } from "fs/promises";
import { join } from "path";
import { CaseDetailPage } from "../../_components/cases/CaseDetailPage";
import { CaseDetailWrapper } from "../../_components/cases/CaseDetailWrapper";
import { Case } from "../../_lib/types";
import { DashboardTabs } from "../../_components/layout/DashboardTabs";

async function getCase(caseId: string): Promise<Case | null> {
  try {
    // Read the JSON file directly from the filesystem
    // This works reliably in both development and production
    const filePath = join(process.cwd(), "public", "data", "cases.json");
    const fileContents = await readFile(filePath, "utf8");
    const cases: Case[] = JSON.parse(fileContents);

    // Try to find the case in the static JSON data
    const staticCase = cases.find((c) => c.id === caseId);
    return staticCase || null;
  } catch (error) {
    console.error("Error fetching case:", error);
    // For errors (file read, JSON parse), we should call notFound
    notFound();
  }
}

interface CaseDetailProps {
  params: Promise<{
    caseId: string;
  }>;
}

export default async function CaseDetail({ params }: CaseDetailProps) {
  const { caseId } = await params;
  const staticCase = await getCase(caseId);

  // If we found the case in static data, render it directly
  if (staticCase) {
    return (
      <DashboardTabs>
        <CaseDetailPage case={staticCase} />
      </DashboardTabs>
    );
  }

  // If not found in static data, use the wrapper to check localStorage
  return (
    <DashboardTabs>
      <CaseDetailWrapper caseId={caseId} />
    </DashboardTabs>
  );
}
