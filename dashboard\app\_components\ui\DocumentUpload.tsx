"use client";

import { useState, useCallback } from "react";
import { useDropzone } from "react-dropzone";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import { CloudArrowUpIcon, TrashIcon } from "@heroicons/react/24/outline";
import { Document } from "../../_lib/types";

interface DocumentUploadProps {
  documents: Document[];
  caseId: string;
}

export function DocumentUpload({
  documents: initialDocuments,
  caseId,
}: DocumentUploadProps) {
  const [documents, setDocuments] = useState<Document[]>(initialDocuments);
  const [uploading, setUploading] = useState(false);

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      setUploading(true);

      // Simulate upload delay
      setTimeout(() => {
        const newDocuments: Document[] = acceptedFiles.map((file) => ({
          id: `doc-${crypto.randomUUID()}`,
          name: file.name,
          type: file.type || "unknown",
          uploadedAt: new Date().toISOString(),
        }));

        setDocuments((prev) => [...prev, ...newDocuments]);

        // Save to sessionStorage (in a real app, this would be sent to an API)
        const allDocuments = [...documents, ...newDocuments];
        sessionStorage.setItem(
          `documents-${caseId}`,
          JSON.stringify(allDocuments)
        );

        setUploading(false);
      }, 1000);
    },
    [documents, caseId]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "application/pdf": [".pdf"],
      "application/msword": [".doc"],
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        [".docx"],
      "image/*": [".png", ".jpg", ".jpeg", ".gif"],
      "text/*": [".txt"],
    },
    maxSize: 10 * 1024 * 1024, // 10MB
  });

  const removeDocument = (documentId: string) => {
    setDocuments((prev) => prev.filter((doc) => doc.id !== documentId));

    // Update sessionStorage
    const updatedDocuments = documents.filter((doc) => doc.id !== documentId);
    sessionStorage.setItem(
      `documents-${caseId}`,
      JSON.stringify(updatedDocuments)
    );
  };

  const getFileIcon = (type: string) => {
    if (type.includes("pdf")) return "📄";
    if (type.includes("word") || type.includes("document")) return "📝";
    if (type.includes("image")) return "🖼️";
    if (type.includes("text")) return "📄";
    return "📎";
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
      {/* Upload Area */}
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
          isDragActive
            ? "border-blue-400 bg-blue-50 dark:bg-blue-900/20"
            : "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
        }`}
      >
        <input {...getInputProps()} />
        <CloudArrowUpIcon className="h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
        {uploading ? (
          <div className="space-y-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 dark:border-blue-500 mx-auto"></div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Subiendo archivos...</p>
          </div>
        ) : isDragActive ? (
          <p className="text-sm text-blue-600 dark:text-blue-400">Suelta los archivos aquí...</p>
        ) : (
          <div className="space-y-2">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Arrastra archivos aquí o haz clic para seleccionar
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-500">
              PDF, DOC, DOCX, imágenes y archivos de texto (máx. 10MB)
            </p>
          </div>
        )}
      </div>

      {/* Documents List */}
      {documents.length > 0 && (
        <div className="mt-6">
          <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
            Documentos ({documents.length})
          </h4>
          <div className="space-y-2">
            {documents.map((document) => (
              <div
                key={document.id}
                className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <span className="text-lg">{getFileIcon(document.type)}</span>
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {document.name}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Subido el{" "}
                      {format(
                        new Date(document.uploadedAt),
                        "dd MMM yyyy - HH:mm",
                        {
                          locale: es,
                        }
                      )}
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => removeDocument(document.id)}
                  className="text-red-400 hover:text-red-600 dark:hover:text-red-500 p-1 rounded-md hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                  aria-label="Eliminar documento"
                >
                  <TrashIcon className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {documents.length === 0 && !uploading && (
        <div className="mt-6 text-center text-gray-500 dark:text-gray-400 py-4">
          No hay documentos subidos
        </div>
      )}
    </div>
  );
}
