# 🧪 Guía de Pruebas - sessionStorage Implementation

## 🎯 Objetivo
Validar que la migración a `sessionStorage` funciona correctamente y proporciona la experiencia de demostración deseada.

## 📋 Lista de Verificación Completa

### ✅ **Prueba 1: Funcionalidad Básica**
1. **Ir a** `http://localhost:3001/dashboard`
2. **Crear caso nuevo:**
   ```
   Título: "PRUEBA sessionStorage - Caso Civil"
   Tipo: "Civil"
   Cliente: "Ana García"
   Prioridad: "Media"
   Descripción: "Caso de prueba para validar sessionStorage"
   Tareas: 
   - "Revisar documentación inicial"
   - "Contactar con el cliente"
   ```
3. **Verificar**: El caso se crea y aparece en la lista
4. **Verificar**: Se puede hacer clic y navegar al detalle

### ✅ **Prueba 2: Verificación de sessionStorage**
1. **Abrir DevTools** (F12)
2. **Ir a Application → Session Storage → localhost:3001**
3. **Verificar que existen las claves:**
   ```
   cases                    // Array con el caso creado
   chat-c-[id-del-caso]    // Mensaje inicial del chat
   ```
4. **Verificar que NO existen en Local Storage:**
   ```
   Application → Local Storage → localhost:3001
   NO debe haber claves de casos aquí
   ```

### ✅ **Prueba 3: Funcionalidad Completa Durante la Sesión**
1. **Navegar al detalle del caso creado**
2. **Probar Chat:**
   - Enviar mensaje: "Hola, este es un mensaje de prueba"
   - Verificar que se guarda en sessionStorage
3. **Probar Tareas:**
   - Marcar primera tarea como completada
   - Verificar que se actualiza en sessionStorage
4. **Probar Documentos:**
   - Intentar subir un archivo (simulado)
   - Verificar que se guarda en sessionStorage

### ✅ **Prueba 4: Persistencia Durante la Sesión**
1. **Estando en la página de detalle del caso**
2. **Refrescar la página** (F5)
3. **Verificar:**
   - ✅ La página se recarga correctamente
   - ✅ Todos los datos siguen presentes
   - ✅ El chat mantiene los mensajes
   - ✅ Las tareas mantienen su estado
4. **Navegar de vuelta al dashboard**
5. **Verificar:**
   - ✅ El caso sigue visible en la lista
   - ✅ Se puede navegar nuevamente al detalle

### ✅ **Prueba 5: Aislamiento Entre Pestañas**
1. **Con el caso creado visible en pestaña 1**
2. **Abrir nueva pestaña** (Ctrl+T)
3. **Ir a** `http://localhost:3001/dashboard` **en pestaña 2**
4. **Verificar:**
   - ❌ El caso NO aparece en pestaña 2
   - ✅ Solo casos estáticos son visibles
5. **Volver a pestaña 1**
6. **Verificar:**
   - ✅ El caso sigue visible en pestaña 1

### ✅ **Prueba 6: Limpieza Entre Sesiones**
1. **Con casos de prueba creados y visibles**
2. **Cerrar COMPLETAMENTE el navegador** (todas las ventanas)
3. **Esperar 5 segundos**
4. **Abrir el navegador nuevamente**
5. **Ir a** `http://localhost:3001/dashboard`
6. **Verificar:**
   - ✅ Solo casos estáticos son visibles
   - ❌ Los casos de prueba han desaparecido
   - ✅ sessionStorage está vacío

### ✅ **Prueba 7: Múltiples Casos en la Misma Sesión**
1. **Crear primer caso:**
   ```
   Título: "Caso 1 - Laboral"
   Cliente: "Pedro Martínez"
   ```
2. **Crear segundo caso:**
   ```
   Título: "Caso 2 - Familia"
   Cliente: "María López"
   ```
3. **Verificar:**
   - ✅ Ambos casos aparecen en la lista
   - ✅ Se puede navegar a ambos casos
   - ✅ Los datos no se mezclan entre casos
   - ✅ sessionStorage contiene ambos casos

## 🔧 Comandos de Verificación en DevTools

### **Verificar sessionStorage:**
```javascript
// En DevTools Console:

// Ver todos los casos creados
console.log("Casos:", JSON.parse(sessionStorage.getItem("cases") || "[]"));

// Ver mensajes de un caso específico
console.log("Chat:", sessionStorage.getItem("chat-c-[id-del-caso]"));

// Ver tareas de un caso específico
console.log("Tareas:", sessionStorage.getItem("milestones-c-[id-del-caso]"));

// Ver documentos de un caso específico
console.log("Documentos:", sessionStorage.getItem("documents-c-[id-del-caso]"));

// Ver todo el sessionStorage
console.log("Session Storage completo:", {...sessionStorage});
```

### **Verificar que localStorage está limpio:**
```javascript
// Verificar que NO hay casos en localStorage
console.log("Local Storage (debe estar limpio de casos):", {...localStorage});

// Debe mostrar solo configuraciones como 'theme', NO casos
```

### **Limpiar sessionStorage manualmente (si necesario):**
```javascript
// Limpiar todo
sessionStorage.clear();

// O limpiar claves específicas
sessionStorage.removeItem("cases");
sessionStorage.removeItem("chat-c-[id]");
sessionStorage.removeItem("milestones-c-[id]");
sessionStorage.removeItem("documents-c-[id]");
```

## 📊 Resultados Esperados

### **✅ Comportamiento Correcto:**

#### **Durante la Sesión:**
- Casos se crean y persisten
- Navegación funciona perfectamente
- Todas las funcionalidades operativas
- Datos se mantienen en refresh
- sessionStorage se actualiza correctamente

#### **Entre Pestañas:**
- Casos solo visibles en pestaña de origen
- Otras pestañas muestran solo casos estáticos
- Aislamiento completo entre pestañas

#### **Entre Sesiones:**
- Casos de prueba desaparecen al cerrar navegador
- Nueva sesión inicia limpia
- Solo casos estáticos visibles
- sessionStorage vacío

### **❌ Problemas Posibles:**

#### **Si los casos persisten entre sesiones:**
- Verificar que se cambió localStorage por sessionStorage
- Verificar que no hay código residual usando localStorage
- Limpiar manualmente localStorage si tiene datos antiguos

#### **Si los casos no aparecen durante la sesión:**
- Verificar que sessionStorage se está escribiendo
- Verificar que CaseDetailWrapper lee de sessionStorage
- Verificar consola por errores JavaScript

#### **Si hay errores de navegación:**
- Verificar que CaseDetailWrapper maneja casos de sessionStorage
- Verificar que los IDs de casos son únicos
- Verificar que no hay conflictos entre casos estáticos y dinámicos

## 🎯 Escenarios de Demo

### **Demo Comercial Perfecta:**
1. **Preparación:** Cerrar navegador para limpiar datos
2. **Inicio:** Abrir navegador, mostrar casos estáticos
3. **Demostración:** Crear caso en vivo durante la presentación
4. **Interacción:** Mostrar todas las funcionalidades
5. **Finalización:** Cerrar navegador, datos se limpian automáticamente

### **Workshop de Entrenamiento:**
1. **Cada participante** inicia con datos limpios
2. **Ejercicios prácticos** sin interferencia de datos anteriores
3. **Sesiones independientes** entre participantes
4. **Limpieza automática** al final de cada sesión

## 🚀 Validación Final

### **Checklist de Éxito:**
- [ ] Casos se crean correctamente
- [ ] Navegación funciona durante la sesión
- [ ] Todas las pestañas operativas (Chat, Tareas, Documentos)
- [ ] Datos persisten en refresh (misma pestaña)
- [ ] Casos NO aparecen en nuevas pestañas
- [ ] Casos desaparecen al cerrar navegador
- [ ] sessionStorage se usa correctamente
- [ ] localStorage no contiene datos de casos
- [ ] No hay errores en consola
- [ ] Experiencia de demo es limpia y profesional

### **Si todos los checks pasan:**
🎉 **¡La migración a sessionStorage está completa y funcionando perfectamente!**

La aplicación ahora proporciona una experiencia de demostración ideal donde:
- Los datos de prueba no se acumulan
- Cada sesión inicia limpia
- La funcionalidad completa está disponible durante la sesión
- La privacidad y seguridad están mejoradas
