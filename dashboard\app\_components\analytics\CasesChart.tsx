"use client";

import { motion } from "framer-motion";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import { MonthlyStats, CasesByArea } from "../../_lib/types";

interface CasesChartProps {
  data: MonthlyStats[];
  detailed?: boolean;
  casesByArea?: CasesByArea[];
}

export function CasesChart({
  data,
  detailed = false,
  casesByArea,
}: CasesChartProps) {
  const maxCases = Math.max(
    ...data.map((d) => Math.max(d.newCases, d.completedCases))
  );
  const chartHeight = detailed ? 400 : 300;

  const getBarHeight = (value: number) => {
    return (value / maxCases) * (chartHeight - 100);
  };

  const formatMonth = (monthStr: string) => {
    const date = new Date(monthStr + "-01");
    return format(date, "MMM yyyy", { locale: es });
  };

  const areaColors = {
    Laboral: "bg-blue-500",
    Civil: "bg-purple-500",
    Familia: "bg-pink-500",
    Penal: "bg-red-500",
    Comercial: "bg-green-500",
  };

  return (
    <div className="space-y-6">
      {/* Main Chart */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">
            {detailed ? "Análisis Detallado de Casos" : "Casos por Mes"}
          </h3>
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded"></div>
              <span className="text-gray-600">Nuevos</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded"></div>
              <span className="text-gray-600">Completados</span>
            </div>
          </div>
        </div>

        <div className="relative" style={{ height: chartHeight }}>
          {/* Y-axis labels */}
          <div className="absolute left-0 top-0 bottom-0 w-12 flex flex-col justify-between text-xs text-gray-500">
            {[0, 0.25, 0.5, 0.75, 1].map((ratio) => (
              <div key={ratio} className="text-right pr-2">
                {Math.round(maxCases * ratio)}
              </div>
            ))}
          </div>

          {/* Chart area */}
          <div className="ml-12 mr-4 h-full relative">
            {/* Grid lines */}
            {[0, 0.25, 0.5, 0.75, 1].map((ratio) => (
              <div
                key={ratio}
                className="absolute left-0 right-0 border-t border-gray-100"
                style={{ bottom: `${ratio * (chartHeight - 100)}px` }}
              />
            ))}

            {/* Bars */}
            <div className="flex items-end justify-between h-full pb-12">
              {data.map((item, index) => (
                <motion.div
                  key={item.month}
                  initial={{ height: 0 }}
                  animate={{ height: "auto" }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  className="flex-1 flex items-end justify-center space-x-1 max-w-16"
                >
                  {/* New cases bar */}
                  <div
                    className="bg-blue-500 rounded-t w-6 relative group"
                    style={{ height: getBarHeight(item.newCases) }}
                  >
                    <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                      {item.newCases} nuevos
                    </div>
                  </div>
                  {/* Completed cases bar */}
                  <div
                    className="bg-green-500 rounded-t w-6 relative group"
                    style={{ height: getBarHeight(item.completedCases) }}
                  >
                    <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                      {item.completedCases} completados
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* X-axis labels */}
            <div className="absolute bottom-0 left-0 right-0 flex justify-between text-xs text-gray-500">
              {data.map((item) => (
                <div key={item.month} className="flex-1 text-center">
                  {formatMonth(item.month)}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Cases by Area (detailed view) */}
      {detailed && casesByArea && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">
            Distribución por Área Legal
          </h3>

          <div className="space-y-4">
            {casesByArea.map((area, index) => (
              <motion.div
                key={area.area}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="flex items-center justify-between"
              >
                <div className="flex items-center space-x-3 flex-1">
                  <div
                    className={`w-4 h-4 rounded ${
                      areaColors[area.area as keyof typeof areaColors] ||
                      "bg-gray-500"
                    }`}
                  ></div>
                  <span className="font-medium text-gray-900">{area.area}</span>
                </div>

                <div className="flex items-center space-x-6 text-sm">
                  <div className="text-center">
                    <div className="font-semibold text-gray-900">
                      {area.count}
                    </div>
                    <div className="text-gray-500">casos</div>
                  </div>
                  <div className="text-center">
                    <div className="font-semibold text-gray-900">
                      {area.percentage}%
                    </div>
                    <div className="text-gray-500">del total</div>
                  </div>
                  <div className="text-center">
                    <div className="font-semibold text-gray-900">
                      ARS {area.averageValue.toLocaleString("es-AR")}
                    </div>
                    <div className="text-gray-500">promedio</div>
                  </div>
                  <div className="text-center">
                    <div className="font-semibold text-green-600">
                      {area.successRate}%
                    </div>
                    <div className="text-gray-500">éxito</div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Summary */}
          <div className="mt-6 pt-6 border-t border-gray-200 grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {casesByArea.reduce((sum, area) => sum + area.count, 0)}
              </div>
              <div className="text-sm text-gray-600">Total de Casos</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                ARS{" "}
                {Math.round(
                  casesByArea.reduce(
                    (sum, area) => sum + area.averageValue * area.count,
                    0
                  ) / casesByArea.reduce((sum, area) => sum + area.count, 0)
                ).toLocaleString("es-AR")}
              </div>
              <div className="text-sm text-gray-600">Valor Promedio</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {Math.round(
                  casesByArea.reduce(
                    (sum, area) => sum + area.successRate * area.count,
                    0
                  ) / casesByArea.reduce((sum, area) => sum + area.count, 0)
                )}
                %
              </div>
              <div className="text-sm text-gray-600">Tasa de Éxito</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
