"use client";

import { useState, useEffect } from "react";
import { notFound } from "next/navigation";
import { CaseDetailPage } from "./CaseDetailPage";
import { Case } from "../../_lib/types";

interface CaseDetailWrapperProps {
  caseId: string;
}

export function CaseDetailWrapper({ caseId }: CaseDetailWrapperProps) {
  const [case_, setCase] = useState<Case | null>(null);
  const [loading, setLoading] = useState(true);
  const [notFoundCase, setNotFoundCase] = useState(false);

  useEffect(() => {
    const loadCase = async () => {
      try {
        // First, try to load from static JSON data
        const response = await fetch("/data/cases.json");
        const staticCases: Case[] = await response.json();
        const staticCase = staticCases.find((c) => c.id === caseId);
        
        if (staticCase) {
          setCase(staticCase);
          setLoading(false);
          return;
        }

        // If not found in static data, check sessionStorage
        const localCases = JSON.parse(sessionStorage.getItem("cases") || "[]");
        const localCase = localCases.find((c: Case) => c.id === caseId);
        
        if (localCase) {
          setCase(localCase);
        } else {
          setNotFoundCase(true);
        }
      } catch (error) {
        console.error("Error loading case:", error);
        setNotFoundCase(true);
      } finally {
        setLoading(false);
      }
    };

    loadCase();
  }, [caseId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 dark:border-blue-500"></div>
      </div>
    );
  }

  if (notFoundCase || !case_) {
    notFound();
  }

  return <CaseDetailPage case={case_} />;
}
