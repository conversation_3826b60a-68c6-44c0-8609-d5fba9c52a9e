"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  MagnifyingGlassIcon,
  PlusIcon,
  ArrowDownTrayIcon,
} from "@heroicons/react/24/outline";
import { StarIcon as StarSolidIcon } from "@heroicons/react/24/solid";
import * as Tabs from "@radix-ui/react-tabs";
import { Template, TemplateFilters } from "../../_lib/types";
import { TemplateCard } from "./TemplateCard";
import { TemplateFilters as TemplateFiltersComponent } from "./TemplateFilters";
import { UploadTemplateModal } from "../modals/UploadTemplateModal";
import { MyTemplatesSection } from "./MyTemplatesSection";

export function TemplatesMarketplace() {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("marketplace");
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [filters, setFilters] = useState<TemplateFilters>({
    legalArea: "all",
    documentType: "all",
    priceRange: "all",
    sortBy: "popularity",
    searchTerm: "",
  });

  // Load templates from JSON
  useEffect(() => {
    const loadTemplates = async () => {
      try {
        const response = await fetch("/data/templates.json");
        const templatesData = await response.json();
        setTemplates(templatesData);
        setFilteredTemplates(templatesData);
      } catch (error) {
        console.error("Error loading templates:", error);
      } finally {
        setLoading(false);
      }
    };

    loadTemplates();
  }, []);

  // Apply filters and sorting
  useEffect(() => {
    let filtered = [...templates];

    // Search filter
    if (filters.searchTerm) {
      filtered = filtered.filter(
        (template) =>
          template.title
            .toLowerCase()
            .includes(filters.searchTerm.toLowerCase()) ||
          template.description
            .toLowerCase()
            .includes(filters.searchTerm.toLowerCase()) ||
          template.tags.some((tag) =>
            tag.toLowerCase().includes(filters.searchTerm.toLowerCase())
          )
      );
    }

    // Legal area filter
    if (filters.legalArea !== "all") {
      filtered = filtered.filter(
        (template) => template.legalArea === filters.legalArea
      );
    }

    // Document type filter
    if (filters.documentType !== "all") {
      filtered = filtered.filter(
        (template) => template.documentType === filters.documentType
      );
    }

    // Price range filter
    if (filters.priceRange === "free") {
      filtered = filtered.filter((template) => template.price === 0);
    } else if (filters.priceRange === "paid") {
      filtered = filtered.filter((template) => template.price > 0);
    }

    // Sorting
    switch (filters.sortBy) {
      case "popularity":
        filtered.sort((a, b) => b.downloadCount - a.downloadCount);
        break;
      case "rating":
        filtered.sort((a, b) => b.rating - a.rating);
        break;
      case "newest":
        filtered.sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
        break;
      case "price_low":
        filtered.sort((a, b) => a.price - b.price);
        break;
      case "price_high":
        filtered.sort((a, b) => b.price - a.price);
        break;
    }

    setFilteredTemplates(filtered);
  }, [templates, filters]);

  const featuredTemplates = filteredTemplates.filter(
    (template) => template.featured
  );
  const regularTemplates = filteredTemplates.filter(
    (template) => !template.featured
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Tabs.Root value={activeTab} onValueChange={setActiveTab}>
        <Tabs.List className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
          <Tabs.Trigger
            value="marketplace"
            className="flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 text-gray-600 dark:text-gray-300"
          >
            <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
            Marketplace
          </Tabs.Trigger>
          <Tabs.Trigger
            value="my-templates"
            className="flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 text-gray-600 dark:text-gray-300"
          >
            <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
            Mis Plantillas
          </Tabs.Trigger>
        </Tabs.List>

        <div className="mt-6">
          <Tabs.Content value="marketplace">
            <div className="space-y-6">
              {/* Search and Filters */}
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
                    <input
                      type="text"
                      value={filters.searchTerm}
                      onChange={(e) =>
                        setFilters((prev) => ({
                          ...prev,
                          searchTerm: e.target.value,
                        }))
                      }
                      placeholder="Buscar plantillas..."
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <TemplateFiltersComponent
                    filters={filters}
                    onFiltersChange={setFilters}
                  />
                  <button
                    onClick={() => setShowUploadModal(true)}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white text-sm font-medium rounded-md hover:bg-blue-700 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900 transition-colors cursor-pointer"
                  >
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Subir Plantilla
                  </button>
                </div>
              </div>

              {/* Featured Templates */}
              {featuredTemplates.length > 0 && (
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                    <StarSolidIcon className="h-5 w-5 text-yellow-400 mr-2" />
                    Plantillas Destacadas
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {featuredTemplates.map((template, index) => (
                      <motion.div
                        key={template.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: index * 0.1 }}
                      >
                        <TemplateCard template={template} featured />
                      </motion.div>
                    ))}
                  </div>
                </div>
              )}

              {/* Regular Templates */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    Todas las Plantillas
                  </h2>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {filteredTemplates.length} plantillas encontradas
                  </span>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {regularTemplates.map((template, index) => (
                    <motion.div
                      key={template.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                    >
                      <TemplateCard template={template} />
                    </motion.div>
                  ))}
                </div>
              </div>

              {filteredTemplates.length === 0 && (
                <div className="text-center py-12">
                  <MagnifyingGlassIcon className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400">
                    No se encontraron plantillas que coincidan con los filtros
                    seleccionados.
                  </p>
                </div>
              )}
            </div>
          </Tabs.Content>

          <Tabs.Content value="my-templates">
            <MyTemplatesSection />
          </Tabs.Content>
        </div>
      </Tabs.Root>

      {/* Upload Modal */}
      {showUploadModal && (
        <UploadTemplateModal
          onClose={() => setShowUploadModal(false)}
          onUpload={(template) => {
            setTemplates((prev) => [...prev, template]);
            setShowUploadModal(false);
          }}
        />
      )}
    </div>
  );
}
