export interface Case {
  id: string;
  title: string;
  type: string;
  client: string;
  status: "new" | "in_progress" | "closed";
  progress: number;
  createdAt: string;
  similarCount: number;
  description: string;
  priority: "low" | "medium" | "high";
  estimatedCost?: string;
  complexityScore?: number;
  riskAssessment?: "low" | "medium" | "high";
  successProbability?: number;
  aiSummary?: string;
  keyFacts?: string[];
  nextActions?: string[];
  timeTracking?: TimeTracking;
  milestones: Milestone[];
  messages: Message[];
  documents: Document[];
  activities?: Activity[];
  unreadMessagesCount?: number;
}

export interface Milestone {
  id: string;
  title: string;
  completed: boolean;
  dueDate: string;
  dependencies?: string[];
}

export interface Message {
  id: string;
  sender: "client" | "lawyer";
  content: string;
  timestamp: string;
  status?: "sent" | "delivered" | "read";
  attachments?: Attachment[];
  threadId?: string;
}

export interface Document {
  id: string;
  name: string;
  type: string;
  uploadedAt: string;
  uploadedBy?: "client" | "lawyer";
  size?: string;
}

export interface Attachment {
  id: string;
  name: string;
  type: string;
  size: string;
}

export interface Activity {
  id: string;
  type:
    | "status_change"
    | "document_upload"
    | "milestone_completed"
    | "message_sent"
    | "court_filing";
  description: string;
  timestamp: string;
  user: "client" | "lawyer" | "system";
}

export interface TimeTracking {
  totalHours: number;
  billableHours: number;
  lastActivity: string;
}

export interface Jurisprudence {
  id: string;
  title: string;
  excerpt: string;
  link: string;
  court: string;
  date: string;
  caseType: string;
  relevance: number;
}

export interface Template {
  id: string;
  title: string;
  description: string;
  legalArea: string;
  documentType: string;
  price: number;
  rating: number;
  downloadCount: number;
  author: string;
  authorId: string;
  createdAt: string;
  updatedAt: string;
  tags: string[];
  preview: string;
  fileUrl: string;
  featured: boolean;
  status: "draft" | "published" | "under_review";
}

export interface TemplateFilters {
  legalArea: string;
  documentType: string;
  priceRange: "all" | "free" | "paid";
  sortBy: "popularity" | "rating" | "newest" | "price_low" | "price_high";
  searchTerm: string;
}

export interface UserTemplate {
  id: string;
  templateId: string;
  purchasedAt: string;
  downloadCount: number;
}

export interface AnalyticsOverview {
  totalCases: number;
  activeCases: number;
  completedCases: number;
  newCasesThisMonth: number;
  totalRevenue: number;
  averageCaseValue: number;
  clientSatisfaction: number;
  responseTime: number;
}

export interface MonthlyStats {
  month: string;
  newCases: number;
  completedCases: number;
  revenue: number;
  clientSatisfaction: number;
}

export interface CasesByArea {
  area: string;
  count: number;
  percentage: number;
  averageValue: number;
  successRate: number;
}

export interface TimeTrackingData {
  totalHours: number;
  billableHours: number;
  billabilityRate: number;
  averageHoursPerCase: number;
  hourlyRate: number;
  topTimeSpenders: {
    caseId: string;
    title: string;
    hours: number;
    billableHours: number;
  }[];
}

export interface ClientMetrics {
  totalClients: number;
  newClientsThisMonth: number;
  clientRetentionRate: number;
  averageClientValue: number;
  topClients: {
    name: string;
    totalValue: number;
    casesCount: number;
    satisfaction: number;
  }[];
}

export interface PerformanceMetrics {
  averageResponseTime: number;
  caseResolutionTime: number;
  clientSatisfactionTrend: {
    month: string;
    score: number;
  }[];
  successRateByArea: {
    area: string;
    rate: number;
  }[];
}

export interface FinancialMetrics {
  monthlyRevenue: {
    month: string;
    revenue: number;
    expenses: number;
    profit: number;
  }[];
  outstandingInvoices: number;
  averageCollectionTime: number;
  profitMargin: number;
}

export interface AnalyticsData {
  overview: AnalyticsOverview;
  monthlyStats: MonthlyStats[];
  casesByArea: CasesByArea[];
  timeTracking: TimeTrackingData;
  clientMetrics: ClientMetrics;
  performanceMetrics: PerformanceMetrics;
  financialMetrics: FinancialMetrics;
}

export interface AvailableCase {
  id: string;
  title: string;
  description: string;
  type: string;
  clientLocation: string;
  budgetRange: string;
  estimatedValue: number;
  postedAt: string;
  urgencyLevel: "low" | "medium" | "high" | "urgent";
  bidsCount: number;
  status: "open" | "closed" | "in_review";
  clientName: string;
  details: {
    [key: string]: unknown;
  };
}

export interface CaseBid {
  id: string;
  caseId: string;
  lawyerId: string;
  proposedFee: {
    type: "hourly" | "fixed" | "contingency";
    amount?: number;
    currency?: "ARS" | "USD" | "UMA";
    percentage?: number;
    description: string;
  };
  estimatedTimeline: string;
  experience: string;
  availability: string;
  submittedAt: string;
  status: "submitted" | "under_review" | "accepted" | "rejected";
}

export interface CaseFilters {
  type: string;
  location: string;
  budgetRange: string;
  urgencyLevel: string;
  sortBy: "newest" | "deadline" | "budget_high" | "budget_low" | "bids_count";
}
