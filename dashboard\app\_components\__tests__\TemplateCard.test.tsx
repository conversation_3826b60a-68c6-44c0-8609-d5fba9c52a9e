import { describe, it, expect, beforeEach, vi } from "vitest";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { TemplateCard } from "../templates/TemplateCard";
import { ThemeProvider } from "../../_contexts/ThemeContext";
import { Template } from "../../_lib/types";

const mockTemplate = {
  id: "template-1",
  title: "Contrato de Compraventa Inmobiliaria",
  description:
    "Modelo completo de boleto de compraventa de inmuebles con todas las cláusulas de seguridad.",
  legalArea: "Civil",
  documentType: "Contratos",
  price: 0,
  rating: 4.5,
  downloadCount: 1876,
  author: "<PERSON><PERSON><PERSON>",
  authorId: "author-1",
  createdAt: "2025-01-15T10:00:00-03:00",
  updatedAt: "2025-01-15T10:00:00-03:00",
  status: "published" as const,
  featured: false,
  tags: ["inmobiliario", "compraventa", "civil"],
  preview: "/templates/preview/template-1.pdf",
  fileUrl: "/templates/download/template-1.pdf",
};

const mockFeaturedTemplate = {
  ...mockTemplate,
  id: "template-2",
  featured: true,
  title: "Contrato de Trabajo Indefinido",
  legalArea: "Laboral",
  price: 2500,
};

function TestApp({
  template = mockTemplate,
  featured = false,
  onPurchase,
}: {
  template?: Template;
  featured?: boolean;
  onPurchase?: (template: Template) => void;
} = {}) {
  return (
    <ThemeProvider>
      <TemplateCard
        template={template}
        featured={featured}
        onPurchase={onPurchase || vi.fn()}
      />
    </ThemeProvider>
  );
}

describe("TemplateCard", () => {
  beforeEach(() => {
    localStorage.clear();
    document.documentElement.className = "";
  });

  it("should render template information correctly", () => {
    render(<TestApp />);

    expect(
      screen.getByText("Contrato de Compraventa Inmobiliaria")
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        "Modelo completo de boleto de compraventa de inmuebles con todas las cláusulas de seguridad."
      )
    ).toBeInTheDocument();
    expect(screen.getByText("Civil")).toBeInTheDocument();
    expect(screen.getByText("Contratos")).toBeInTheDocument();
    expect(screen.getByText("Gratis")).toBeInTheDocument();
    expect(screen.getByText("Por Dra. Patricia López")).toBeInTheDocument();
    expect(screen.getByText("1,876")).toBeInTheDocument();
  });

  it("should show price when template is not free", () => {
    const paidTemplate = { ...mockTemplate, price: 2500 };
    render(<TestApp template={paidTemplate} />);

    expect(screen.getByText("2.500")).toBeInTheDocument();
    expect(screen.queryByText("Gratis")).not.toBeInTheDocument();
  });

  it("should show featured badge when template is featured", () => {
    render(<TestApp template={mockFeaturedTemplate} featured={true} />);

    expect(screen.getByText("Destacado")).toBeInTheDocument();
  });

  it("should render star rating correctly", () => {
    render(<TestApp />);

    // Should show rating value
    expect(screen.getByText("(4.5)")).toBeInTheDocument();

    // Should render star icons (we can check for their presence)
    const starIcons = screen.getAllByRole("img", { hidden: true });
    expect(starIcons.length).toBeGreaterThan(0);
  });

  it("should call onPurchase when download button is clicked for free template", async () => {
    const mockOnPurchase = vi.fn();
    const user = userEvent.setup();

    render(<TestApp onPurchase={mockOnPurchase} />);

    const downloadButton = screen.getByText("Descargar Gratis");
    await user.click(downloadButton);

    expect(mockOnPurchase).toHaveBeenCalledWith(mockTemplate);
  });

  it("should call onPurchase when purchase button is clicked for paid template", async () => {
    const mockOnPurchase = vi.fn();
    const user = userEvent.setup();
    const paidTemplate = { ...mockTemplate, price: 2500 };

    render(<TestApp template={paidTemplate} onPurchase={mockOnPurchase} />);

    const purchaseButton = screen.getByText("Comprar ARS 2.500");
    await user.click(purchaseButton);

    expect(mockOnPurchase).toHaveBeenCalledWith(paidTemplate);
  });

  it("should apply dark theme classes correctly", () => {
    // Set dark theme
    document.documentElement.classList.add("dark");

    render(<TestApp />);

    // The component should render without errors in dark mode
    expect(
      screen.getByText("Contrato de Compraventa Inmobiliaria")
    ).toBeInTheDocument();
  });

  it("should handle different legal areas with correct colors", () => {
    const laborTemplate = { ...mockTemplate, legalArea: "Laboral" };
    render(<TestApp template={laborTemplate} />);

    expect(screen.getByText("Laboral")).toBeInTheDocument();
  });

  it("should show preview button", () => {
    render(<TestApp />);

    const previewButton = screen.getByRole("button", { name: /vista previa/i });
    expect(previewButton).toBeInTheDocument();
  });

  it("should handle hover animations", async () => {
    const user = userEvent.setup();
    render(<TestApp />);

    const card = screen
      .getByText("Contrato de Compraventa Inmobiliaria")
      .closest("div");
    expect(card).toBeInTheDocument();

    // Hover over the card
    if (card) {
      await user.hover(card);
      // The card should still be visible after hover
      expect(
        screen.getByText("Contrato de Compraventa Inmobiliaria")
      ).toBeInTheDocument();
    }
  });

  it("should display download count in correct format", () => {
    const highDownloadTemplate = { ...mockTemplate, downloadCount: 12345 };
    render(<TestApp template={highDownloadTemplate} />);

    expect(screen.getByText("12,345")).toBeInTheDocument();
  });

  it("should handle zero rating correctly", () => {
    const noRatingTemplate = { ...mockTemplate, rating: 0 };
    render(<TestApp template={noRatingTemplate} />);

    expect(screen.getByText("(0)")).toBeInTheDocument();
  });

  it("should render document type badge", () => {
    render(<TestApp />);

    expect(screen.getByText("Contratos")).toBeInTheDocument();
  });

  it("should handle long titles correctly", () => {
    const longTitleTemplate = {
      ...mockTemplate,
      title:
        "Este es un título muy largo para un contrato que debería ser truncado correctamente en la interfaz de usuario",
    };

    render(<TestApp template={longTitleTemplate} />);

    expect(screen.getByText(longTitleTemplate.title)).toBeInTheDocument();
  });

  it("should handle long descriptions correctly", () => {
    const longDescTemplate = {
      ...mockTemplate,
      description:
        "Esta es una descripción muy larga que debería ser truncada correctamente en la interfaz de usuario para mantener un diseño limpio y consistente en todas las tarjetas de plantillas.",
    };

    render(<TestApp template={longDescTemplate} />);

    expect(screen.getByText(longDescTemplate.description)).toBeInTheDocument();
  });
});
