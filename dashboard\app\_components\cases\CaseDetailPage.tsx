"use client";

import { useState } from "react";
import Link from "next/link";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import {
  ChevronLeftIcon,
  ChatBubbleLeftRightIcon,
  CheckCircleIcon,
  DocumentIcon,
  ScaleIcon,
  ChartBarIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import * as Tabs from "@radix-ui/react-tabs";
import { Case } from "../../_lib/types";
import { Chat } from "../ui/Chat";
import { MilestoneTimeline } from "./MilestoneTimeline";
import { DocumentUpload } from "../ui/DocumentUpload";
import { JurisprudencePanel } from "./JurisprudencePanel";
import { CaseContextPanel } from "./CaseContextPanel";
import { CaseHistory } from "./CaseHistory";

import { CaseIntelligence } from "./CaseIntelligence";
import { useIsMounted } from "../../_lib/useIsomorphicDate";
import config from "../../../public/data/config.json";
import { DeleteCaseModal } from "../modals/DeleteCaseModal";

interface CaseDetailPageProps {
  case: Case;
}

export function CaseDetailPage({ case: caseData }: CaseDetailPageProps) {
  const [activeTab, setActiveTab] = useState("context");
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const isMounted = useIsMounted();

  // Configuración de tabs
  const allTabs = [
    {
      value: "context",
      label: "Resumen",
      icon: DocumentIcon,
    },
    {
      value: "chat",
      label: "Chat",
      icon: ChatBubbleLeftRightIcon,
    },
    {
      value: "tasks",
      label: "Tareas",
      icon: CheckCircleIcon,
    },
    {
      value: "documents",
      label: "Documentos",
      icon: DocumentIcon,
    },
    {
      value: "jurisprudence",
      label: "Jurisprudencia",
      icon: ScaleIcon,
    },
    {
      value: "history",
      label: "Historial",
      icon: ChartBarIcon,
    },
    {
      value: "intelligence",
      label: "Inteligencia",
      icon: CheckCircleIcon,
    },
  ];

  // Verificar si es un caso creado por el usuario
  const isUserCreatedCase = () => {
    // Los casos creados por usuario tienen IDs con formato: c-xxxxxxxx (8 caracteres aleatorios)
    // Los casos del JSON tienen IDs como: c-001, c-002, etc.
    const idPattern = /^c-[a-f0-9]{8}$/i;
    return idPattern.test(caseData.id);
  };

  // Filtrar tabs basado en feature flags y tipo de caso - SOLO RENDERIZADO
  const tabsConfig = allTabs.filter(tab => {
    if (tab.value === "intelligence") {
      return config.featureFlags.caseDetails.intelligenceTab.enabled;
    }
    // Ocultar chat para casos creados por usuario
    if (tab.value === "chat" && isUserCreatedCase()) {
      return false;
    }
    return true;
  });

  const formattedDate = isMounted
    ? format(new Date(caseData.createdAt), "dd MMM yyyy", { locale: es })
    : "Fecha de creación";

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <nav className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
        <Link
          href="/dashboard"
          className="flex items-center hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 px-2 py-1 rounded-md transition-all duration-200 cursor-pointer"
        >
          <ChevronLeftIcon className="h-4 w-4 mr-1" />
          Casos
        </Link>
        <span>/</span>
        <span className="text-gray-900 dark:text-gray-100 font-medium">
          {caseData.title}
        </span>
      </nav>

      {/* Case Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="mb-4">
          <div className="flex items-start justify-between mb-2">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {caseData.title}
            </h1>
            <button
              onClick={() => setShowDeleteModal(true)}
              className="flex items-center px-3 py-2 text-sm font-medium text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md hover:bg-red-100 dark:hover:bg-red-900/30 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer"
              title="Eliminar caso"
            >
              <TrashIcon className="h-4 w-4 mr-2" />
              Eliminar
            </button>
          </div>
          <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
            <span>Cliente: {caseData.client}</span>
            <span>•</span>
            <span>Tipo: {caseData.type}</span>
            <span>•</span>
            <span>Creado: {formattedDate}</span>
          </div>
        </div>

        <p className="text-gray-700 dark:text-gray-300 mb-4">
          {caseData.description}
        </p>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium px-2 py-1 rounded-full">
              {caseData.type}
            </span>
          </div>
          {caseData.similarCount > 0 && (
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <ScaleIcon className="h-4 w-4 mr-1" />
              {caseData.similarCount} casos similares
            </div>
          )}
        </div>
      </div>

      {/* Tabs */}
      <Tabs.Root value={activeTab} onValueChange={setActiveTab}>
        <Tabs.List className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg overflow-x-auto">
          {tabsConfig.map((tab) => {
            const IconComponent = tab.icon;
            return (
              <Tabs.Trigger
                key={tab.value}
                value={tab.value}
                className="flex items-center cursor-pointer px-4 py-2 text-sm font-medium rounded-md transition-colors hover:bg-gray-200 dark:hover:bg-gray-600 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 text-gray-600 dark:text-gray-300 whitespace-nowrap"
              >
                <IconComponent className="h-4 w-4 mr-2" />
                {tab.label}
              </Tabs.Trigger>
            );
          })}
        </Tabs.List>

        <div className="mt-6">
          <Tabs.Content value="context">
            <CaseContextPanel case={caseData} onTabChange={setActiveTab} />
          </Tabs.Content>
          <Tabs.Content value="chat">
            <Chat messages={caseData.messages} caseId={caseData.id} />
          </Tabs.Content>
          <Tabs.Content value="tasks">
            <MilestoneTimeline
              milestones={caseData.milestones}
              caseId={caseData.id}
            />
          </Tabs.Content>
          <Tabs.Content value="documents">
            <DocumentUpload
              documents={caseData.documents}
              caseId={caseData.id}
            />
          </Tabs.Content>
          <Tabs.Content value="jurisprudence">
            <JurisprudencePanel caseType={caseData.type} />
          </Tabs.Content>
          <Tabs.Content value="history">
            <CaseHistory activities={caseData.activities || []} />
          </Tabs.Content>
          {config.featureFlags.caseDetails.intelligenceTab.enabled && (
            <Tabs.Content value="intelligence">
              <CaseIntelligence case={caseData} />
            </Tabs.Content>
          )}
        </div>
      </Tabs.Root>

      {/* Modal de eliminación */}
      <DeleteCaseModal
        case={caseData}
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
      />
    </div>
  );
}
